mod common;
mod config;
mod core;
mod generated;
mod types;
mod utils;

use common::endpoints::manager::EndpointManager;
use common::processor::EntryProcessor;
use core::config::load_config;
use core::logger::Logger;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::mpsc;
use types::EntryTransaction;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    rustls::crypto::ring::default_provider().install_default().expect("Failed to install crypto provider");

    let config = load_config()?;
    let _logger = Logger::init(&config.logger)?;

    let endpoint_count = config.endpoints.len();
    let filters = config.filters.clone();
    let mut endpoint_manager = EndpointManager::new(config.endpoints, config.filters);

    println!("Starting EndpointManager with {} endpoints...", endpoint_count);
    let mut entry_receiver = endpoint_manager.subscribe().await?;
    println!("EndpointManager started! Listening for entries from all endpoints...");

    let (tx, mut transaction_receiver) = mpsc::unbounded_channel::<EntryTransaction>();

    println!("Starting EntryProcessor...");

    let processor_tx = tx.clone();
    tokio::spawn(async move {
        let mut entry_count = 0;
        while let Some(enriched_entry) = entry_receiver.recv().await {
            let now_ns = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos() as u64;
            let processing_time_ns = now_ns - enriched_entry.received_at;
            let processing_time_us = processing_time_ns as f64 / 1000.0;

            entry_count += 1;

            println!(
                "Entry #{}: slot={}, data_size={} bytes, processing_time={:.2} μs, received_by={}",
                entry_count,
                enriched_entry.entry.slot,
                enriched_entry.entry.entries.len(),
                processing_time_us,
                enriched_entry.received_by
            );

            let processor = EntryProcessor::new(filters.clone());
            EntryProcessor::spawn_processor(processor, enriched_entry, processor_tx.clone());

            if entry_count >= 50 {
                println!("Processed {} entries. Stopping entry processing...", entry_count);
                break;
            }
        }
    });

    println!("Listening for processed transactions...");
    let mut transaction_count = 0;

    while let Some(entry_transaction) = transaction_receiver.recv().await {
        transaction_count += 1;

        println!(
            "Transaction #{}: slot={}, process_time={} ns, received_by={}, signature={}",
            transaction_count,
            entry_transaction.slot,
            entry_transaction.process_time,
            entry_transaction.received_by,
            entry_transaction.transaction.signatures.get(0).map(|sig| format!("{:.8}...", bs58::encode(sig).into_string())).unwrap_or_else(|| "no_signature".to_string())
        );

        if transaction_count >= 1000 {
            println!("Processed {} transactions successfully. Test completed!", transaction_count);
            break;
        }
    }

    Ok(())
}
