use super::deduplicator::EntryDeduplicator;
use super::errors::EndpointManagerError;
use crate::common::client::config::ShredstreamClientConfig;
use crate::common::client::config::ShredstreamSubscriptionFilters;
use crate::common::client::errors::ShredstreamError;
use crate::common::client::shredstream_client::ShredstreamClient;
use crate::config::endpoints::EndpointConfig;
use crate::generated::Entry;
use crate::types::EnrichedEntry;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use tracing::error;
use tracing::info;
use tracing::warn;

pub struct EndpointManager {
    endpoints: Vec<EndpointConfig>,
    shared_filters: Option<ShredstreamSubscriptionFilters>,
    deduplicator: Arc<EntryDeduplicator>,
}

impl EndpointManager {
    pub fn new(endpoints: Vec<EndpointConfig>, filters: Option<ShredstreamSubscriptionFilters>) -> Self {
        Self { endpoints, shared_filters: filters, deduplicator: Arc::new(EntryDeduplicator::new()) }
    }

    pub async fn subscribe(&mut self) -> Result<mpsc::UnboundedReceiver<EnrichedEntry>, EndpointManagerError> {
        let enabled_endpoints: Vec<_> = self.endpoints.iter().filter(|e| e.enabled).collect();

        if enabled_endpoints.is_empty() {
            return Err(EndpointManagerError::NoEnabledEndpoints);
        }

        let (sender, receiver) = mpsc::unbounded_channel();

        for endpoint_config in enabled_endpoints {
            let sender_clone = sender.clone();
            let endpoint_config_clone = endpoint_config.clone();
            let filters_clone = self.shared_filters.clone();
            let deduplicator_clone = self.deduplicator.clone();

            tokio::spawn(async move {
                Self::handle_endpoint(endpoint_config_clone, filters_clone, sender_clone, deduplicator_clone).await;
            });
        }

        Ok(receiver)
    }

    async fn handle_endpoint(endpoint_config: EndpointConfig, filters: Option<ShredstreamSubscriptionFilters>, sender: mpsc::UnboundedSender<EnrichedEntry>, deduplicator: Arc<EntryDeduplicator>) {
        loop {
            info!("Connecting to endpoint: {}", endpoint_config.name);

            if Self::try_connect_and_stream(&endpoint_config, &filters, &sender, &deduplicator).await {
                return;
            }

            info!("Endpoint {} entering cooldown for {:?}", endpoint_config.name, endpoint_config.cooldown_duration);
            tokio::time::sleep(endpoint_config.cooldown_duration).await;
        }
    }

    async fn try_connect_and_stream(endpoint_config: &EndpointConfig, filters: &Option<ShredstreamSubscriptionFilters>, sender: &mpsc::UnboundedSender<EnrichedEntry>, deduplicator: &Arc<EntryDeduplicator>) -> bool {
        let client_config = Self::create_client_config_with_callbacks(endpoint_config);
        let mut client = ShredstreamClient::new(endpoint_config.url.clone(), filters.clone(), Some(client_config));

        match client.subscribe().await {
            Ok(mut stream) => {
                info!("Successfully subscribed to endpoint: {}", endpoint_config.name);
                Self::process_stream(&mut stream, endpoint_config, sender, deduplicator).await
            }
            Err(e) => {
                error!("Failed to subscribe to {}: {:?}", endpoint_config.name, e);
                false
            }
        }
    }

    async fn process_stream(stream: &mut (impl StreamExt<Item = Result<Entry, tonic::Status>> + Unpin), endpoint_config: &EndpointConfig, sender: &mpsc::UnboundedSender<EnrichedEntry>, deduplicator: &Arc<EntryDeduplicator>) -> bool {
        while let Some(entry_result) = stream.next().await {
            match entry_result {
                Ok(entry) => {
                    let received_at = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos() as u64;
                    let enriched_entry = EnrichedEntry { entry, received_at, received_by: endpoint_config.name.clone() };

                    if !deduplicator.should_process(&enriched_entry) {
                        continue;
                    }

                    if sender.send(enriched_entry).is_err() {
                        warn!("Receiver dropped, stopping endpoint: {}", endpoint_config.name);
                        return true;
                    }
                }
                Err(e) => {
                    error!("Stream error from {}: {:?}", endpoint_config.name, e);
                    return false;
                }
            }
        }

        false
    }

    fn create_client_config_with_callbacks(endpoint_config: &EndpointConfig) -> ShredstreamClientConfig {
        let mut config = ShredstreamClientConfig::from(endpoint_config);

        config.disconnect_callback = Some(Box::new(|endpoint, error, delay, attempt| {
            Self::handle_disconnect_callback(endpoint, error, delay, attempt);
        }));

        config.retry_attempt_callback = Some(Box::new(|endpoint, attempt, max_attempts| {
            Self::handle_retry_attempt_callback(endpoint, attempt, max_attempts);
        }));

        config.retry_success_callback = Some(Box::new(|endpoint, attempt, elapsed_time| {
            Self::handle_retry_success_callback(endpoint, attempt, elapsed_time);
        }));

        config.failure_callback = Some(Box::new(|endpoint, error| {
            Self::handle_failure_callback(endpoint, error);
        }));

        config
    }

    fn handle_disconnect_callback(endpoint: &str, error: &ShredstreamError, delay: Duration, attempt: u32) {
        warn!(endpoint = endpoint, error = ?error, delay_ms = delay.as_millis(), attempt = attempt, "Connection lost, retrying after delay");
    }

    fn handle_retry_attempt_callback(endpoint: &str, attempt: u32, max_attempts: u32) {
        info!(endpoint = endpoint, attempt = attempt, max_attempts = max_attempts, "Starting retry attempt");
    }

    fn handle_retry_success_callback(endpoint: &str, attempt: u32, elapsed_time: Duration) {
        info!(endpoint = endpoint, successful_attempt = attempt, elapsed_time_ms = elapsed_time.as_millis(), "Connection restored after retries");
    }

    fn handle_failure_callback(endpoint: &str, error: &ShredstreamError) {
        error!(endpoint = endpoint, error = ?error, "Connection failed after all retry attempts or circuit breaker opened");
    }
}
