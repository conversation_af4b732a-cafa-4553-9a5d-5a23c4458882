use crate::common::client::config::ShredstreamSubscriptionFilters;
use crate::types::{EnrichedEntry, EntryTransaction};
use bincode;
use solana_entry::entry::Entry;
use solana_pubkey::Pubkey;
use solana_transaction::versioned::VersionedTransaction;
use std::collections::HashSet;
use std::str::FromStr;
use tokio::sync::mpsc;
use tracing::{debug, error, warn};

pub struct EntryProcessor {
    filter_pubkeys: Option<HashSet<Pubkey>>,
}

impl EntryProcessor {
    pub fn new(filters: Option<ShredstreamSubscriptionFilters>) -> Self {
        let filter_pubkeys = filters.as_ref().and_then(|f| f.accounts.as_ref()).map(|accounts| {
            accounts
                .iter()
                .filter_map(|account_str| match Pubkey::from_str(account_str) {
                    Ok(pubkey) => Some(pubkey),
                    Err(e) => {
                        warn!(account = %account_str, error = %e, "Failed to parse account pubkey, skipping");
                        None
                    }
                })
                .collect::<HashSet<_>>()
        });

        Self { filter_pubkeys }
    }

    fn validate_transaction(&self, transaction: &VersionedTransaction) -> bool {
        let Some(filter_pubkeys) = &self.filter_pubkeys else {
            return true;
        };

        if filter_pubkeys.is_empty() {
            return true;
        }

        transaction.message.static_account_keys().iter().any(|account_key| filter_pubkeys.contains(account_key))
    }

    pub async fn process_entry(&self, enriched_entry: EnrichedEntry, tx: mpsc::UnboundedSender<EntryTransaction>) -> Result<(), ProcessorError> {
        let slot = enriched_entry.entry.slot;
        let received_at = enriched_entry.received_at;
        let received_by = enriched_entry.received_by.clone();

        let entries: Vec<Entry> = match bincode::deserialize(&enriched_entry.entry.entries) {
            Ok(entries) => entries,
            Err(e) => {
                error!(slot = slot, received_by = %received_by, error = %e, "Failed to deserialize entry data");
                return Err(ProcessorError::DeserializationFailed(e.to_string()));
            }
        };

        let mut total_transactions = 0;
        let mut processed_transactions = 0;

        for entry in entries {
            total_transactions += entry.transactions.len();

            for transaction in entry.transactions {
                if self.validate_transaction(&transaction) {
                    let now_ns = std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_nanos() as u64;
                    let process_time = now_ns - received_at;
                    let entry_transaction = EntryTransaction { slot, received_at, received_by: received_by.clone(), process_time, transaction };

                    if let Err(e) = tx.send(entry_transaction) {
                        error!(slot = slot, received_by = %received_by, error = %e, "Failed to send processed transaction");
                        return Err(ProcessorError::ChannelSendFailed(e.to_string()));
                    }

                    processed_transactions += 1;
                }
            }
        }

        debug!(slot = slot, received_by = %received_by, total_transactions = total_transactions, processed_transactions = processed_transactions, "Entry processing completed");

        Ok(())
    }

    pub fn spawn_processor(processor: EntryProcessor, enriched_entry: EnrichedEntry, tx: mpsc::UnboundedSender<EntryTransaction>) {
        tokio::spawn(async move {
            if let Err(e) = processor.process_entry(enriched_entry, tx).await {
                warn!(error = %e, "Entry processing failed");
            }
        });
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ProcessorError {
    #[error("Deserialization failed: {0}")]
    DeserializationFailed(String),

    #[error("Channel send failed: {0}")]
    ChannelSendFailed(String),
}
