(function() {
    var type_impls = Object.fromEntries([["solana_message",[["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-AsMut%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.5.0\">1.5.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3849\">Source</a></span><a href=\"#impl-AsMut%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsMut.html\" title=\"trait core::convert::AsMut\">AsMut</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_mut\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3850\">Source</a><a href=\"#method.as_mut\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsMut.html#tymethod.as_mut\" class=\"fn\">as_mut</a>(&amp;mut self) -&gt; &amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>Converts this type into a mutable reference of the (usually inferred) input type.</div></details></div></details>","AsMut<[T]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-AsMut%3CVec%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.5.0\">1.5.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3835\">Source</a></span><a href=\"#impl-AsMut%3CVec%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsMut.html\" title=\"trait core::convert::AsMut\">AsMut</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_mut\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3836\">Source</a><a href=\"#method.as_mut\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsMut.html#tymethod.as_mut\" class=\"fn\">as_mut</a>(&amp;mut self) -&gt; &amp;mut <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section></summary><div class='docblock'>Converts this type into a mutable reference of the (usually inferred) input type.</div></details></div></details>","AsMut<Vec<T, A>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-AsRef%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3842\">Source</a></span><a href=\"#impl-AsRef%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsRef.html\" title=\"trait core::convert::AsRef\">AsRef</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_ref\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3843\">Source</a><a href=\"#method.as_ref\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsRef.html#tymethod.as_ref\" class=\"fn\">as_ref</a>(&amp;self) -&gt; &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>Converts this type into a shared reference of the (usually inferred) input type.</div></details></div></details>","AsRef<[T]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-AsRef%3CVec%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3828\">Source</a></span><a href=\"#impl-AsRef%3CVec%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsRef.html\" title=\"trait core::convert::AsRef\">AsRef</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_ref\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3829\">Source</a><a href=\"#method.as_ref\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.AsRef.html#tymethod.as_ref\" class=\"fn\">as_ref</a>(&amp;self) -&gt; &amp;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section></summary><div class='docblock'>Converts this type into a shared reference of the (usually inferred) input type.</div></details></div></details>","AsRef<Vec<T, A>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Borrow%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/slice.rs.html#791\">Source</a></span><a href=\"#impl-Borrow%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html\" title=\"trait core::borrow::Borrow\">Borrow</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.borrow\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/slice.rs.html#792\">Source</a><a href=\"#method.borrow\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html#tymethod.borrow\" class=\"fn\">borrow</a>(&amp;self) -&gt; &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href=\"https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html#tymethod.borrow\">Read more</a></div></details></div></details>","Borrow<[T]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-BorrowMut%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/slice.rs.html#798\">Source</a></span><a href=\"#impl-BorrowMut%3C%5BT%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html\" title=\"trait core::borrow::BorrowMut\">BorrowMut</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.borrow_mut\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/slice.rs.html#799\">Source</a><a href=\"#method.borrow_mut\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut\" class=\"fn\">borrow_mut</a>(&amp;mut self) -&gt; &amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href=\"https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut\">Read more</a></div></details></div></details>","BorrowMut<[T]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Clone-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3316\">Source</a></span><a href=\"#impl-Clone-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.clone_from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3346\">Source</a><a href=\"#method.clone_from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#method.clone_from\" class=\"fn\">clone_from</a>(&amp;mut self, source: &amp;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;)</h4></section></summary><div class=\"docblock\"><p>Overwrites the contents of <code>self</code> with a clone of the contents of <code>source</code>.</p>\n<p>This method is preferred over simply assigning <code>source.clone()</code> to <code>self</code>,\nas it avoids reallocation if possible. Additionally, if the element type\n<code>T</code> overrides <code>clone_from()</code>, this will reuse the resources of <code>self</code>’s\nelements as well.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x = <span class=\"macro\">vec!</span>[<span class=\"number\">5</span>, <span class=\"number\">6</span>, <span class=\"number\">7</span>];\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>y = <span class=\"macro\">vec!</span>[<span class=\"number\">8</span>, <span class=\"number\">9</span>, <span class=\"number\">10</span>];\n<span class=\"kw\">let </span>yp: <span class=\"kw-2\">*const </span>i32 = y.as_ptr();\n\ny.clone_from(<span class=\"kw-2\">&amp;</span>x);\n\n<span class=\"comment\">// The value is the same\n</span><span class=\"macro\">assert_eq!</span>(x, y);\n\n<span class=\"comment\">// And no reallocation occurred\n</span><span class=\"macro\">assert_eq!</span>(yp, y.as_ptr());</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.clone\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3318\">Source</a><a href=\"#method.clone\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#tymethod.clone\" class=\"fn\">clone</a>(&amp;self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section></summary><div class='docblock'>Returns a duplicate of the value. <a href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#tymethod.clone\">Read more</a></div></details></div></details>","Clone","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Debug-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3821\">Source</a></span><a href=\"#impl-Debug-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.fmt\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3822\">Source</a><a href=\"#method.fmt\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html#tymethod.fmt\" class=\"fn\">fmt</a>(&amp;self, f: &amp;mut <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/fmt/struct.Formatter.html\" title=\"struct core::fmt::Formatter\">Formatter</a>&lt;'_&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.unit.html\">()</a>, <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/fmt/struct.Error.html\" title=\"struct core::fmt::Error\">Error</a>&gt;</h4></section></summary><div class='docblock'>Formats the value using the given formatter. <a href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html#tymethod.fmt\">Read more</a></div></details></div></details>","Debug","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Default-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3811\">Source</a></span><a href=\"#impl-Default-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.default\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3815\">Source</a><a href=\"#method.default\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html#tymethod.default\" class=\"fn\">default</a>() -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Creates an empty <code>Vec&lt;T&gt;</code>.</p>\n<p>The vector will not allocate until elements are pushed onto it.</p>\n</div></details></div></details>","Default","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-DerefMut-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3304\">Source</a></span><a href=\"#impl-DerefMut-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.DerefMut.html\" title=\"trait core::ops::deref::DerefMut\">DerefMut</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.deref_mut\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3306\">Source</a><a href=\"#method.deref_mut\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.DerefMut.html#tymethod.deref_mut\" class=\"fn\">deref_mut</a>(&amp;mut self) -&gt; &amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>Mutably dereferences the value.</div></details></div></details>","DerefMut","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Deserialize%3C'de%3E-for-Vec%3CT%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/impls.rs.html#1146-1148\">Source</a><a href=\"#impl-Deserialize%3C'de%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'de, T&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.deserialize\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/impls.rs.html#1150-1152\">Source</a><a href=\"#method.deserialize\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize\" class=\"fn\">deserialize</a>&lt;D&gt;(\n    deserializer: D,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;, &lt;D as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html#associatedtype.Error\" title=\"type serde::de::Deserializer::Error\">Error</a>&gt;<div class=\"where\">where\n    D: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;,</div></h4></section></summary><div class='docblock'>Deserialize this value from the given Serde deserializer. <a href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize\">Read more</a></div></details></div></details>","Deserialize<'de>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Drop-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3798\">Source</a></span><a href=\"#impl-Drop-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/drop/trait.Drop.html\" title=\"trait core::ops::drop::Drop\">Drop</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.drop\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3799\">Source</a><a href=\"#method.drop\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/ops/drop/trait.Drop.html#tymethod.drop\" class=\"fn\">drop</a>(&amp;mut self)</h4></section></summary><div class='docblock'>Executes the destructor for this type. <a href=\"https://doc.rust-lang.org/nightly/core/ops/drop/trait.Drop.html#tymethod.drop\">Read more</a></div></details></div></details>","Drop","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Extend%3C%26T%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.2.0\">1.2.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3742\">Source</a></span><a href=\"#impl-Extend%3C%26T%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'a, T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html\" title=\"trait core::iter::traits::collect::Extend\">Extend</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;'a T</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a> + 'a,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3><div class=\"docblock\"><p>Extend implementation that copies elements out of references before pushing them onto the Vec.</p>\n</div></section></summary><div class=\"docblock\"><p>This implementation is specialized for slice iterators, where it uses <a href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html#method.copy_from_slice\" title=\"method slice::copy_from_slice\"><code>copy_from_slice</code></a> to\nappend the entire slice at once.</p>\n</div><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3744\">Source</a><a href=\"#method.extend\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#tymethod.extend\" class=\"fn\">extend</a>&lt;I&gt;(&amp;mut self, iter: I)<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&lt;Item = <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;'a T</a>&gt;,</div></h4></section></summary><div class='docblock'>Extends a collection with the contents of an iterator. <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#tymethod.extend\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend_one\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3750\">Source</a><a href=\"#method.extend_one\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#method.extend_one\" class=\"fn\">extend_one</a>(&amp;mut self, _: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;'a T</a>)</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>extend_one</code>)</span></div></span><div class='docblock'>Extends a collection with exactly one element.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend_reserve\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3756\">Source</a><a href=\"#method.extend_reserve\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#method.extend_reserve\" class=\"fn\">extend_reserve</a>(&amp;mut self, additional: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>extend_one</code>)</span></div></span><div class='docblock'>Reserves capacity in a collection for the given number of additional elements. <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#method.extend_reserve\">Read more</a></div></details></div></details>","Extend<&'a T>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Extend%3CT%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3503\">Source</a></span><a href=\"#impl-Extend%3CT%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html\" title=\"trait core::iter::traits::collect::Extend\">Extend</a>&lt;T&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3506\">Source</a><a href=\"#method.extend\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#tymethod.extend\" class=\"fn\">extend</a>&lt;I&gt;(&amp;mut self, iter: I)<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&lt;Item = T&gt;,</div></h4></section></summary><div class='docblock'>Extends a collection with the contents of an iterator. <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#tymethod.extend\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend_one\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3512\">Source</a><a href=\"#method.extend_one\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#method.extend_one\" class=\"fn\">extend_one</a>(&amp;mut self, item: T)</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>extend_one</code>)</span></div></span><div class='docblock'>Extends a collection with exactly one element.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend_reserve\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3518\">Source</a><a href=\"#method.extend_reserve\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#method.extend_reserve\" class=\"fn\">extend_reserve</a>(&amp;mut self, additional: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>extend_one</code>)</span></div></span><div class='docblock'>Reserves capacity in a collection for the given number of additional elements. <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#method.extend_reserve\">Read more</a></div></details></div></details>","Extend<T>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3C%26%5BT%5D%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3857\">Source</a></span><a href=\"#impl-From%3C%26%5BT%5D%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3866\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Allocates a <code>Vec&lt;T&gt;</code> and fills it by cloning <code>s</code>’s items.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"macro\">assert_eq!</span>(Vec::from(<span class=\"kw-2\">&amp;</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>][..]), <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details></div></details>","From<&[T]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3C%26%5BT;+N%5D%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.74.0\">1.74.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3889\">Source</a></span><a href=\"#impl-From%3C%26%5BT;+N%5D%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, const N: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[T; N]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3898\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[T; N]</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Allocates a <code>Vec&lt;T&gt;</code> and fills it by cloning <code>s</code>’s items.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"macro\">assert_eq!</span>(Vec::from(<span class=\"kw-2\">&amp;</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]), <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details></div></details>","From<&[T; N]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3C%26mut+%5BT%5D%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.19.0\">1.19.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3873\">Source</a></span><a href=\"#impl-From%3C%26mut+%5BT%5D%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;&amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3882\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: &amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Allocates a <code>Vec&lt;T&gt;</code> and fills it by cloning <code>s</code>’s items.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"macro\">assert_eq!</span>(Vec::from(<span class=\"kw-2\">&amp;mut </span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>][..]), <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details></div></details>","From<&mut [T]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3C%26mut+%5BT;+N%5D%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.74.0\">1.74.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3905\">Source</a></span><a href=\"#impl-From%3C%26mut+%5BT;+N%5D%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, const N: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;&amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[T; N]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3914\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: &amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[T; N]</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Allocates a <code>Vec&lt;T&gt;</code> and fills it by cloning <code>s</code>’s items.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"macro\">assert_eq!</span>(Vec::from(<span class=\"kw-2\">&amp;mut </span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]), <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details></div></details>","From<&mut [T; N]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3C%5BT;+N%5D%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.44.0\">1.44.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3921\">Source</a></span><a href=\"#impl-From%3C%5BT;+N%5D%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, const N: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[T; N]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3930\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[T; N]</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Allocates a <code>Vec&lt;T&gt;</code> and moves <code>s</code>’s items into it.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"macro\">assert_eq!</span>(Vec::from([<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]), <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details></div></details>","From<[T; N]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3CBinaryHeap%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.5.0\">1.5.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/collections/binary_heap/mod.rs.html#1886\">Source</a></span><a href=\"#impl-From%3CBinaryHeap%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/binary_heap/struct.BinaryHeap.html\" title=\"struct alloc::collections::binary_heap::BinaryHeap\">BinaryHeap</a>&lt;T, A&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/collections/binary_heap/mod.rs.html#1891\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(heap: <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/binary_heap/struct.BinaryHeap.html\" title=\"struct alloc::collections::binary_heap::BinaryHeap\">BinaryHeap</a>&lt;T, A&gt;) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section></summary><div class=\"docblock\"><p>Converts a <code>BinaryHeap&lt;T&gt;</code> into a <code>Vec&lt;T&gt;</code>.</p>\n<p>This conversion requires no data movement or allocation, and has\nconstant time complexity.</p>\n</div></details></div></details>","From<BinaryHeap<T, A>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3CBox%3C%5BT%5D,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.18.0\">1.18.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3962\">Source</a></span><a href=\"#impl-From%3CBox%3C%5BT%5D,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/boxed/struct.Box.html\" title=\"struct alloc::boxed::Box\">Box</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>, A&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3972\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/boxed/struct.Box.html\" title=\"struct alloc::boxed::Box\">Box</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>, A&gt;) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section></summary><div class=\"docblock\"><p>Converts a boxed slice into a vector by transferring ownership of\nthe existing heap allocation.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>b: Box&lt;[i32]&gt; = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>].into_boxed_slice();\n<span class=\"macro\">assert_eq!</span>(Vec::from(b), <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details></div></details>","From<Box<[T], A>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3CCow%3C'a,+%5BT%5D%3E%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.14.0\">1.14.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3936-3938\">Source</a></span><a href=\"#impl-From%3CCow%3C'a,+%5BT%5D%3E%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'a, T&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/alloc/borrow/enum.Cow.html\" title=\"enum alloc::borrow::Cow\">Cow</a>&lt;'a, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html\" title=\"trait alloc::borrow::ToOwned\">ToOwned</a>&lt;Owned = <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3955\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(s: <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/alloc/borrow/enum.Cow.html\" title=\"enum alloc::borrow::Cow\">Cow</a>&lt;'a, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt;) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Converts a clone-on-write slice into a vector.</p>\n<p>If <code>s</code> already owns a <code>Vec&lt;T&gt;</code>, it will be returned directly.\nIf <code>s</code> is borrowing a slice, a new <code>Vec&lt;T&gt;</code> will be allocated and\nfilled by cloning <code>s</code>’s items into it.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>o: Cow&lt;<span class=\"lifetime\">'_</span>, [i32]&gt; = Cow::Owned(<span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n<span class=\"kw\">let </span>b: Cow&lt;<span class=\"lifetime\">'_</span>, [i32]&gt; = Cow::Borrowed(<span class=\"kw-2\">&amp;</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n<span class=\"macro\">assert_eq!</span>(Vec::from(o), Vec::from(b));</code></pre></div>\n</div></details></div></details>","From<Cow<'a, [T]>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3CVecDeque%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.10.0\">1.10.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/collections/vec_deque/mod.rs.html#3149\">Source</a></span><a href=\"#impl-From%3CVecDeque%3CT,+A%3E%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/vec_deque/struct.VecDeque.html\" title=\"struct alloc::collections::vec_deque::VecDeque\">VecDeque</a>&lt;T, A&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/collections/vec_deque/mod.rs.html#3179\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(other: <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/vec_deque/struct.VecDeque.html\" title=\"struct alloc::collections::vec_deque::VecDeque\">VecDeque</a>&lt;T, A&gt;) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section></summary><div class=\"docblock\"><p>Turn a <a href=\"https://doc.rust-lang.org/nightly/alloc/collections/vec_deque/struct.VecDeque.html\" title=\"struct alloc::collections::vec_deque::VecDeque\"><code>VecDeque&lt;T&gt;</code></a> into a <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\"><code>Vec&lt;T&gt;</code></a>.</p>\n<p>This never needs to re-allocate, but does need to do <em>O</em>(<em>n</em>) data movement if\nthe circular buffer doesn’t happen to be at the beginning of the allocation.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::collections::VecDeque;\n\n<span class=\"comment\">// This one is *O*(1).\n</span><span class=\"kw\">let </span>deque: VecDeque&lt;<span class=\"kw\">_</span>&gt; = (<span class=\"number\">1</span>..<span class=\"number\">5</span>).collect();\n<span class=\"kw\">let </span>ptr = deque.as_slices().<span class=\"number\">0</span>.as_ptr();\n<span class=\"kw\">let </span>vec = Vec::from(deque);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>]);\n<span class=\"macro\">assert_eq!</span>(vec.as_ptr(), ptr);\n\n<span class=\"comment\">// This one needs data rearranging.\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>deque: VecDeque&lt;<span class=\"kw\">_</span>&gt; = (<span class=\"number\">1</span>..<span class=\"number\">5</span>).collect();\ndeque.push_front(<span class=\"number\">9</span>);\ndeque.push_front(<span class=\"number\">8</span>);\n<span class=\"kw\">let </span>ptr = deque.as_slices().<span class=\"number\">1</span>.as_ptr();\n<span class=\"kw\">let </span>vec = Vec::from(deque);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">8</span>, <span class=\"number\">9</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>]);\n<span class=\"macro\">assert_eq!</span>(vec.as_ptr(), ptr);</code></pre></div>\n</div></details></div></details>","From<VecDeque<T, A>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-FromIterator%3CT%3E-for-Vec%3CT%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3434\">Source</a></span><a href=\"#impl-FromIterator%3CT%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.FromIterator.html\" title=\"trait core::iter::traits::collect::FromIterator\">FromIterator</a>&lt;T&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h3><div class=\"docblock\"><p>Collects an iterator into a Vec, commonly called via <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/iterator/trait.Iterator.html#method.collect\" title=\"method core::iter::traits::iterator::Iterator::collect\"><code>Iterator::collect()</code></a></p>\n</div></section></summary><div class=\"docblock\"><h4 id=\"allocation-behavior\"><a class=\"doc-anchor\" href=\"#allocation-behavior\">§</a>Allocation behavior</h4>\n<p>In general <code>Vec</code> does not guarantee any particular growth or allocation strategy.\nThat also applies to this trait impl.</p>\n<p><strong>Note:</strong> This section covers implementation details and is therefore exempt from\nstability guarantees.</p>\n<p>Vec may use any or none of the following strategies,\ndepending on the supplied iterator:</p>\n<ul>\n<li>preallocate based on <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/iterator/trait.Iterator.html#method.size_hint\" title=\"method core::iter::traits::iterator::Iterator::size_hint\"><code>Iterator::size_hint()</code></a>\n<ul>\n<li>and panic if the number of items is outside the provided lower/upper bounds</li>\n</ul>\n</li>\n<li>use an amortized growth strategy similar to <code>pushing</code> one item at a time</li>\n<li>perform the iteration in-place on the original allocation backing the iterator</li>\n</ul>\n<p>The last case warrants some attention. It is an optimization that in many cases reduces peak memory\nconsumption and improves cache locality. But when big, short-lived allocations are created,\nonly a small fraction of their items get collected, no further use is made of the spare capacity\nand the resulting <code>Vec</code> is moved into a longer-lived structure, then this can lead to the large\nallocations having their lifetimes unnecessarily extended which can result in increased memory\nfootprint.</p>\n<p>In cases where this is an issue, the excess capacity can be discarded with <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.shrink_to\" title=\"method alloc::vec::Vec::shrink_to\"><code>Vec::shrink_to()</code></a>,\n<a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.shrink_to_fit\" title=\"method alloc::vec::Vec::shrink_to_fit\"><code>Vec::shrink_to_fit()</code></a> or by collecting into <a href=\"https://doc.rust-lang.org/nightly/alloc/boxed/struct.Box.html\" title=\"struct alloc::boxed::Box\"><code>Box&lt;[T]&gt;</code></a> instead, which additionally reduces\nthe size of the long-lived struct.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">static </span>LONG_LIVED: Mutex&lt;Vec&lt;Vec&lt;u16&gt;&gt;&gt; = Mutex::new(Vec::new());\n\n<span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..<span class=\"number\">10 </span>{\n    <span class=\"kw\">let </span>big_temporary: Vec&lt;u16&gt; = (<span class=\"number\">0</span>..<span class=\"number\">1024</span>).collect();\n    <span class=\"comment\">// discard most items\n    </span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>result: Vec&lt;<span class=\"kw\">_</span>&gt; = big_temporary.into_iter().filter(|i| i % <span class=\"number\">100 </span>== <span class=\"number\">0</span>).collect();\n    <span class=\"comment\">// without this a lot of unused capacity might be moved into the global\n    </span>result.shrink_to_fit();\n    LONG_LIVED.lock().unwrap().push(result);\n}</code></pre></div>\n</div><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_iter\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3437\">Source</a><a href=\"#method.from_iter\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.FromIterator.html#tymethod.from_iter\" class=\"fn\">from_iter</a>&lt;I&gt;(iter: I) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&lt;Item = T&gt;,</div></h4></section></summary><div class='docblock'>Creates a value from an iterator. <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.FromIterator.html#tymethod.from_iter\">Read more</a></div></details></div></details>","FromIterator<T>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Hash-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3363\">Source</a></span><a href=\"#impl-Hash-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hash.html\" title=\"trait core::hash::Hash\">Hash</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hash.html\" title=\"trait core::hash::Hash\">Hash</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3><div class=\"docblock\"><p>The hash of a vector is the same as that of the corresponding slice,\nas required by the <code>core::borrow::Borrow</code> implementation.</p>\n</div></section></summary><div class=\"docblock\">\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::hash::BuildHasher;\n\n<span class=\"kw\">let </span>b = std::hash::RandomState::new();\n<span class=\"kw\">let </span>v: Vec&lt;u8&gt; = <span class=\"macro\">vec!</span>[<span class=\"number\">0xa8</span>, <span class=\"number\">0x3c</span>, <span class=\"number\">0x09</span>];\n<span class=\"kw\">let </span>s: <span class=\"kw-2\">&amp;</span>[u8] = <span class=\"kw-2\">&amp;</span>[<span class=\"number\">0xa8</span>, <span class=\"number\">0x3c</span>, <span class=\"number\">0x09</span>];\n<span class=\"macro\">assert_eq!</span>(b.hash_one(v), b.hash_one(s));</code></pre></div>\n</div><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.hash\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3365\">Source</a><a href=\"#method.hash\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hash.html#tymethod.hash\" class=\"fn\">hash</a>&lt;H&gt;(&amp;self, state: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut H</a>)<div class=\"where\">where\n    H: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\">Hasher</a>,</div></h4></section></summary><div class='docblock'>Feeds this value into the given <a href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\"><code>Hasher</code></a>. <a href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hash.html#tymethod.hash\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.hash_slice\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.3.0\">1.3.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/hash/mod.rs.html#235-237\">Source</a></span><a href=\"#method.hash_slice\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hash.html#method.hash_slice\" class=\"fn\">hash_slice</a>&lt;H&gt;(data: &amp;[Self], state: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut H</a>)<div class=\"where\">where\n    H: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\">Hasher</a>,\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Feeds a slice of this type into the given <a href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\"><code>Hasher</code></a>. <a href=\"https://doc.rust-lang.org/nightly/core/hash/trait.Hash.html#method.hash_slice\">Read more</a></div></details></div></details>","Hash","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Index%3CI%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3371\">Source</a></span><a href=\"#impl-Index%3CI%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, I, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html\" title=\"trait core::ops::index::Index\">Index</a>&lt;I&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/slice/index/trait.SliceIndex.html\" title=\"trait core::slice::index::SliceIndex\">SliceIndex</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt;,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle\" open><summary><section id=\"associatedtype.Output\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3372\">Source</a><a href=\"#associatedtype.Output\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html#associatedtype.Output\" class=\"associatedtype\">Output</a> = &lt;I as <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/slice/index/trait.SliceIndex.html\" title=\"trait core::slice::index::SliceIndex\">SliceIndex</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt;&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/nightly/core/slice/index/trait.SliceIndex.html#associatedtype.Output\" title=\"type core::slice::index::SliceIndex::Output\">Output</a></h4></section></summary><div class='docblock'>The returned type after indexing.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.index\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3375\">Source</a><a href=\"#method.index\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html#tymethod.index\" class=\"fn\">index</a>(&amp;self, index: I) -&gt; &amp;&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html\" title=\"trait core::ops::index::Index\">Index</a>&lt;I&gt;&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html#associatedtype.Output\" title=\"type core::ops::index::Index::Output\">Output</a> <a href=\"#\" class=\"tooltip\" data-notable-ty=\"&amp;&lt;Vec&lt;T, A&gt; as Index&lt;I&gt;&gt;::Output\">ⓘ</a></h4></section></summary><div class='docblock'>Performs the indexing (<code>container[index]</code>) operation. <a href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html#tymethod.index\">Read more</a></div></details></div></details>","Index<I>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-IndexMut%3CI%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3381\">Source</a></span><a href=\"#impl-IndexMut%3CI%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, I, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.IndexMut.html\" title=\"trait core::ops::index::IndexMut\">IndexMut</a>&lt;I&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/slice/index/trait.SliceIndex.html\" title=\"trait core::slice::index::SliceIndex\">SliceIndex</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>&gt;,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.index_mut\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3383\">Source</a><a href=\"#method.index_mut\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.IndexMut.html#tymethod.index_mut\" class=\"fn\">index_mut</a>(&amp;mut self, index: I) -&gt; &amp;mut &lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html\" title=\"trait core::ops::index::Index\">Index</a>&lt;I&gt;&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.Index.html#associatedtype.Output\" title=\"type core::ops::index::Index::Output\">Output</a> <a href=\"#\" class=\"tooltip\" data-notable-ty=\"&amp;mut &lt;Vec&lt;T, A&gt; as Index&lt;I&gt;&gt;::Output\">ⓘ</a></h4></section></summary><div class='docblock'>Performs the mutable indexing (<code>container[index]</code>) operation. <a href=\"https://doc.rust-lang.org/nightly/core/ops/index/trait.IndexMut.html#tymethod.index_mut\">Read more</a></div></details></div></details>","IndexMut<I>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-IntoDeserializer%3C'de,+E%3E-for-Vec%3CT%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/value.rs.html#1136-1139\">Source</a><a href=\"#impl-IntoDeserializer%3C'de,+E%3E-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'de, T, E&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.IntoDeserializer.html\" title=\"trait serde::de::IntoDeserializer\">IntoDeserializer</a>&lt;'de, E&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.IntoDeserializer.html\" title=\"trait serde::de::IntoDeserializer\">IntoDeserializer</a>&lt;'de, E&gt;,\n    E: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Error.html\" title=\"trait serde::de::Error\">Error</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle\" open><summary><section id=\"associatedtype.Deserializer\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/value.rs.html#1141\">Source</a><a href=\"#associatedtype.Deserializer\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://docs.rs/serde/1.0.219/serde/de/trait.IntoDeserializer.html#associatedtype.Deserializer\" class=\"associatedtype\">Deserializer</a> = <a class=\"struct\" href=\"https://docs.rs/serde/1.0.219/serde/de/value/struct.SeqDeserializer.html\" title=\"struct serde::de::value::SeqDeserializer\">SeqDeserializer</a>&lt;&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.IntoIter\" title=\"type core::iter::traits::collect::IntoIterator::IntoIter\">IntoIter</a>, E&gt;</h4></section></summary><div class='docblock'>The type of the deserializer being converted into.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_deserializer\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/value.rs.html#1143\">Source</a><a href=\"#method.into_deserializer\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde/1.0.219/serde/de/trait.IntoDeserializer.html#tymethod.into_deserializer\" class=\"fn\">into_deserializer</a>(self) -&gt; &lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt; as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.IntoDeserializer.html\" title=\"trait serde::de::IntoDeserializer\">IntoDeserializer</a>&lt;'de, E&gt;&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.IntoDeserializer.html#associatedtype.Deserializer\" title=\"type serde::de::IntoDeserializer::Deserializer\">Deserializer</a> <a href=\"#\" class=\"tooltip\" data-notable-ty=\"&lt;Vec&lt;T&gt; as IntoDeserializer&lt;&#39;de, E&gt;&gt;::Deserializer\">ⓘ</a></h4></section></summary><div class='docblock'>Convert this value into a deserializer.</div></details></div></details>","IntoDeserializer<'de, E>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-IntoIterator-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3443\">Source</a></span><a href=\"#impl-IntoIterator-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_iter\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3464\">Source</a><a href=\"#method.into_iter\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html#tymethod.into_iter\" class=\"fn\">into_iter</a>(self) -&gt; &lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.IntoIter\" title=\"type core::iter::traits::collect::IntoIterator::IntoIter\">IntoIter</a> <a href=\"#\" class=\"tooltip\" data-notable-ty=\"&lt;Vec&lt;T, A&gt; as IntoIterator&gt;::IntoIter\">ⓘ</a></h4></section></summary><div class=\"docblock\"><p>Creates a consuming iterator, that is, one that moves each value out of\nthe vector (from start to end). The vector cannot be used after calling\nthis.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"string\">\"a\"</span>.to_string(), <span class=\"string\">\"b\"</span>.to_string()];\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v_iter = v.into_iter();\n\n<span class=\"kw\">let </span>first_element: <span class=\"prelude-ty\">Option</span>&lt;String&gt; = v_iter.next();\n\n<span class=\"macro\">assert_eq!</span>(first_element, <span class=\"prelude-val\">Some</span>(<span class=\"string\">\"a\"</span>.to_string()));\n<span class=\"macro\">assert_eq!</span>(v_iter.next(), <span class=\"prelude-val\">Some</span>(<span class=\"string\">\"b\"</span>.to_string()));\n<span class=\"macro\">assert_eq!</span>(v_iter.next(), <span class=\"prelude-val\">None</span>);</code></pre></div>\n</div></details><details class=\"toggle\" open><summary><section id=\"associatedtype.Item\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3444\">Source</a><a href=\"#associatedtype.Item\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.Item\" class=\"associatedtype\">Item</a> = T</h4></section></summary><div class='docblock'>The type of the elements being iterated over.</div></details><details class=\"toggle\" open><summary><section id=\"associatedtype.IntoIter\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3445\">Source</a><a href=\"#associatedtype.IntoIter\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.IntoIter\" class=\"associatedtype\">IntoIter</a> = <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/into_iter/struct.IntoIter.html\" title=\"struct alloc::vec::into_iter::IntoIter\">IntoIter</a>&lt;T, A&gt;</h4></section></summary><div class='docblock'>Which kind of iterator are we turning this into?</div></details></div></details>","IntoIterator","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Ord-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3790\">Source</a></span><a href=\"#impl-Ord-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html\" title=\"trait core::cmp::Ord\">Ord</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html\" title=\"trait core::cmp::Ord\">Ord</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3><div class=\"docblock\"><p>Implements ordering of vectors, <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#lexicographical-comparison\" title=\"trait core::cmp::Ord\">lexicographically</a>.</p>\n</div></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.cmp\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3792\">Source</a><a href=\"#method.cmp\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#tymethod.cmp\" class=\"fn\">cmp</a>(&amp;self, other: &amp;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/cmp/enum.Ordering.html\" title=\"enum core::cmp::Ordering\">Ordering</a></h4></section></summary><div class='docblock'>This method returns an <a href=\"https://doc.rust-lang.org/nightly/core/cmp/enum.Ordering.html\" title=\"enum core::cmp::Ordering\"><code>Ordering</code></a> between <code>self</code> and <code>other</code>. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#tymethod.cmp\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.max\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.21.0\">1.21.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1009-1011\">Source</a></span><a href=\"#method.max\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#method.max\" class=\"fn\">max</a>(self, other: Self) -&gt; Self<div class=\"where\">where\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Compares and returns the maximum of two values. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#method.max\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.min\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.21.0\">1.21.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1048-1050\">Source</a></span><a href=\"#method.min\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#method.min\" class=\"fn\">min</a>(self, other: Self) -&gt; Self<div class=\"where\">where\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Compares and returns the minimum of two values. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#method.min\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.clamp\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.50.0\">1.50.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1074-1076\">Source</a></span><a href=\"#method.clamp\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#method.clamp\" class=\"fn\">clamp</a>(self, min: Self, max: Self) -&gt; Self<div class=\"where\">where\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Restrict a value to a certain interval. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#method.clamp\">Read more</a></div></details></div></details>","Ord","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq%3C%26%5BU%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#23\">Source</a></span><a href=\"#impl-PartialEq%3C%26%5BU%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#23\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#23\">Source</a><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: &amp;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq<&[U]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq%3C%26%5BU;+N%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#36\">Source</a></span><a href=\"#impl-PartialEq%3C%26%5BU;+N%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, A, const N: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[U; N]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#36\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[U; N]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#36\">Source</a><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: &amp;&amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[U; N]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq<&[U; N]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq%3C%26mut+%5BU%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#24\">Source</a></span><a href=\"#impl-PartialEq%3C%26mut+%5BU%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;&amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#24\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;&amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#24\">Source</a><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: &amp;&amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq<&mut [U]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq%3C%5BU%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.48.0\">1.48.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#27\">Source</a></span><a href=\"#impl-PartialEq%3C%5BU%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#27\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#27\">Source</a><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[U]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq<[U]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq%3C%5BU;+N%5D%3E-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#35\">Source</a></span><a href=\"#impl-PartialEq%3C%5BU;+N%5D%3E-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, A, const N: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[U; N]</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#35\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[U; N]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#35\">Source</a><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.array.html\">[U; N]</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq<[U; N]>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq%3CVec%3CU,+A2%3E%3E-for-Vec%3CT,+A1%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#22\">Source</a></span><a href=\"#impl-PartialEq%3CVec%3CU,+A2%3E%3E-for-Vec%3CT,+A1%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, A1, A2&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;U, A2&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A1&gt;<div class=\"where\">where\n    A1: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    A2: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#22\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;U, A2&gt;) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/partial_eq.rs.html#22\">Source</a><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: &amp;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;U, A2&gt;) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq<Vec<U, A2>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialOrd%3CVec%3CT,+A2%3E%3E-for-Vec%3CT,+A1%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3773-3777\">Source</a></span><a href=\"#impl-PartialOrd%3CVec%3CT,+A2%3E%3E-for-Vec%3CT,+A1%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A1, A2&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html\" title=\"trait core::cmp::PartialOrd\">PartialOrd</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A2&gt;&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A1&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html\" title=\"trait core::cmp::PartialOrd\">PartialOrd</a>,\n    A1: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,\n    A2: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3><div class=\"docblock\"><p>Implements comparison of vectors, <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Ord.html#lexicographical-comparison\" title=\"trait core::cmp::Ord\">lexicographically</a>.</p>\n</div></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.partial_cmp\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3780\">Source</a><a href=\"#method.partial_cmp\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#tymethod.partial_cmp\" class=\"fn\">partial_cmp</a>(&amp;self, other: &amp;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A2&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/cmp/enum.Ordering.html\" title=\"enum core::cmp::Ordering\">Ordering</a>&gt;</h4></section></summary><div class='docblock'>This method returns an ordering between <code>self</code> and <code>other</code> values if one exists. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#tymethod.partial_cmp\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.lt\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1382\">Source</a></span><a href=\"#method.lt\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.lt\" class=\"fn\">lt</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests less than (for <code>self</code> and <code>other</code>) and is used by the <code>&lt;</code> operator. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.lt\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.le\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1400\">Source</a></span><a href=\"#method.le\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.le\" class=\"fn\">le</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests less than or equal to (for <code>self</code> and <code>other</code>) and is used by the\n<code>&lt;=</code> operator. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.le\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.gt\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1418\">Source</a></span><a href=\"#method.gt\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.gt\" class=\"fn\">gt</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests greater than (for <code>self</code> and <code>other</code>) and is used by the <code>&gt;</code>\noperator. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.gt\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ge\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#1436\">Source</a></span><a href=\"#method.ge\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.ge\" class=\"fn\">ge</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests greater than or equal to (for <code>self</code> and <code>other</code>) and is used by\nthe <code>&gt;=</code> operator. <a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialOrd.html#method.ge\">Read more</a></div></details></div></details>","PartialOrd<Vec<T, A2>>","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Sanitize-for-Vec%3CT%3E\" class=\"impl\"><a href=\"#impl-Sanitize-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; Sanitize for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: Sanitize,</div></h3></section></summary><div class=\"impl-items\"><section id=\"method.sanitize\" class=\"method trait-impl\"><a href=\"#method.sanitize\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">sanitize</a>(&amp;self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.unit.html\">()</a>, SanitizeError&gt;</h4></section></div></details>","Sanitize","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Serialize-for-Vec%3CT%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/ser/impls.rs.html#257-261\">Source</a><a href=\"#impl-Serialize-for-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html\" title=\"trait serde::ser::Serialize\">Serialize</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html\" title=\"trait serde::ser::Serialize\">Serialize</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.serialize\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/ser/impls.rs.html#257-261\">Source</a><a href=\"#method.serialize\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize\" class=\"fn\">serialize</a>&lt;S&gt;(\n    &amp;self,\n    serializer: S,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;&lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Ok\" title=\"type serde::ser::Serializer::Ok\">Ok</a>, &lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Error\" title=\"type serde::ser::Serializer::Error\">Error</a>&gt;<div class=\"where\">where\n    S: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>,</div></h4></section></summary><div class='docblock'>Serialize this value into the given Serde serializer. <a href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize\">Read more</a></div></details></div></details>","Serialize","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Vec%3CT%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#418\">Source</a><a href=\"#impl-Vec%3CT%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.new\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.39.0\">1.0.0 (const: 1.39.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#434\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.new\" class=\"fn\">new</a>() -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Constructs a new, empty <code>Vec&lt;T&gt;</code>.</p>\n<p>The vector will not allocate until elements are pushed onto it.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec: Vec&lt;i32&gt; = Vec::new();</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.with_capacity\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#494\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.with_capacity\" class=\"fn\">with_capacity</a>(capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Constructs a new, empty <code>Vec&lt;T&gt;</code> with at least the specified capacity.</p>\n<p>The vector will be able to hold at least <code>capacity</code> elements without\nreallocating. This method is allowed to allocate for more elements than\n<code>capacity</code>. If <code>capacity</code> is zero, the vector will not allocate.</p>\n<p>It is important to note that although the returned vector has the\nminimum <em>capacity</em> specified, the vector will have a zero <em>length</em>. For\nan explanation of the difference between length and capacity, see\n<em><a href=\"#capacity-and-reallocation\">Capacity and reallocation</a></em>.</p>\n<p>If it is important to know the exact allocated capacity of a <code>Vec</code>,\nalways use the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.capacity\" title=\"method alloc::vec::Vec::capacity\"><code>capacity</code></a> method after construction.</p>\n<p>For <code>Vec&lt;T&gt;</code> where <code>T</code> is a zero-sized type, there will be no allocation\nand the capacity will always be <code>usize::MAX</code>.</p>\n<h5 id=\"panics\"><a class=\"doc-anchor\" href=\"#panics\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = Vec::with_capacity(<span class=\"number\">10</span>);\n\n<span class=\"comment\">// The vector contains no items, even though it has capacity for more\n</span><span class=\"macro\">assert_eq!</span>(vec.len(), <span class=\"number\">0</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\n\n<span class=\"comment\">// These are all done without reallocating...\n</span><span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..<span class=\"number\">10 </span>{\n    vec.push(i);\n}\n<span class=\"macro\">assert_eq!</span>(vec.len(), <span class=\"number\">10</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\n\n<span class=\"comment\">// ...but this may make the vector reallocate\n</span>vec.push(<span class=\"number\">11</span>);\n<span class=\"macro\">assert_eq!</span>(vec.len(), <span class=\"number\">11</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">11</span>);\n\n<span class=\"comment\">// A vector of a zero-sized type will always over-allocate, since no\n// allocation is necessary\n</span><span class=\"kw\">let </span>vec_units = Vec::&lt;()&gt;::with_capacity(<span class=\"number\">10</span>);\n<span class=\"macro\">assert_eq!</span>(vec_units.capacity(), usize::MAX);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_with_capacity\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#510\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.try_with_capacity\" class=\"fn\">try_with_capacity</a>(capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;, <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/struct.TryReserveError.html\" title=\"struct alloc::collections::TryReserveError\">TryReserveError</a>&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_with_capacity</code>)</span></div></span></summary><div class=\"docblock\"><p>Constructs a new, empty <code>Vec&lt;T&gt;</code> with at least the specified capacity.</p>\n<p>The vector will be able to hold at least <code>capacity</code> elements without\nreallocating. This method is allowed to allocate for more elements than\n<code>capacity</code>. If <code>capacity</code> is zero, the vector will not allocate.</p>\n<h5 id=\"errors\"><a class=\"doc-anchor\" href=\"#errors\">§</a>Errors</h5>\n<p>Returns an error if the capacity exceeds <code>isize::MAX</code> <em>bytes</em>,\nor if the allocator reports allocation failure.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_raw_parts\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#617\">Source</a></span><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.from_raw_parts\" class=\"fn\">from_raw_parts</a>(\n    ptr: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html\">*mut T</a>,\n    length: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Creates a <code>Vec&lt;T&gt;</code> directly from a pointer, a length, and a capacity.</p>\n<h5 id=\"safety\"><a class=\"doc-anchor\" href=\"#safety\">§</a>Safety</h5>\n<p>This is highly unsafe, due to the number of invariants that aren’t\nchecked:</p>\n<ul>\n<li><code>ptr</code> must have been allocated using the global allocator, such as via\nthe <a href=\"https://doc.rust-lang.org/nightly/alloc/alloc/fn.alloc.html\" title=\"fn alloc::alloc::alloc\"><code>alloc::alloc</code></a> function.</li>\n<li><code>T</code> needs to have the same alignment as what <code>ptr</code> was allocated with.\n(<code>T</code> having a less strict alignment is not sufficient, the alignment really\nneeds to be equal to satisfy the <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> requirement that memory must be\nallocated and deallocated with the same layout.)</li>\n<li>The size of <code>T</code> times the <code>capacity</code> (ie. the allocated size in bytes) needs\nto be the same size as the pointer was allocated with. (Because similar to\nalignment, <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> must be called with the same layout <code>size</code>.)</li>\n<li><code>length</code> needs to be less than or equal to <code>capacity</code>.</li>\n<li>The first <code>length</code> values must be properly initialized values of type <code>T</code>.</li>\n<li><code>capacity</code> needs to be the capacity that the pointer was allocated with.</li>\n<li>The allocated size in bytes must be no larger than <code>isize::MAX</code>.\nSee the safety documentation of <a href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html#method.offset\" title=\"method pointer::offset\"><code>pointer::offset</code></a>.</li>\n</ul>\n<p>These requirements are always upheld by any <code>ptr</code> that has been allocated\nvia <code>Vec&lt;T&gt;</code>. Other allocation sources are allowed if the invariants are\nupheld.</p>\n<p>Violating these may cause problems like corrupting the allocator’s\ninternal data structures. For example it is normally <strong>not</strong> safe\nto build a <code>Vec&lt;u8&gt;</code> from a pointer to a C <code>char</code> array with length\n<code>size_t</code>, doing so is only safe if the array was initially allocated by\na <code>Vec</code> or <code>String</code>.\nIt’s also not safe to build one from a <code>Vec&lt;u16&gt;</code> and its length, because\nthe allocator cares about the alignment, and these two types have different\nalignments. The buffer was allocated with alignment 2 (for <code>u16</code>), but after\nturning it into a <code>Vec&lt;u8&gt;</code> it’ll be deallocated with alignment 1. To avoid\nthese issues, it is often preferable to do casting/transmuting using\n<a href=\"https://doc.rust-lang.org/nightly/core/slice/raw/fn.from_raw_parts.html\" title=\"fn core::slice::raw::from_raw_parts\"><code>slice::from_raw_parts</code></a> instead.</p>\n<p>The ownership of <code>ptr</code> is effectively transferred to the\n<code>Vec&lt;T&gt;</code> which may then deallocate, reallocate or change the\ncontents of memory pointed to by the pointer at will. Ensure\nthat nothing else uses the pointer after calling this\nfunction.</p>\n<h5 id=\"examples-2\"><a class=\"doc-anchor\" href=\"#examples-2\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::ptr;\n<span class=\"kw\">use </span>std::mem;\n\n<span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n\n<span class=\"comment\">// Prevent running `v`'s destructor so we are in complete control\n// of the allocation.\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = mem::ManuallyDrop::new(v);\n\n<span class=\"comment\">// Pull out the various important pieces of information about `v`\n</span><span class=\"kw\">let </span>p = v.as_mut_ptr();\n<span class=\"kw\">let </span>len = v.len();\n<span class=\"kw\">let </span>cap = v.capacity();\n\n<span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// Overwrite memory with 4, 5, 6\n    </span><span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..len {\n        ptr::write(p.add(i), <span class=\"number\">4 </span>+ i);\n    }\n\n    <span class=\"comment\">// Put everything back together into a Vec\n    </span><span class=\"kw\">let </span>rebuilt = Vec::from_raw_parts(p, len, cap);\n    <span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>]);\n}</code></pre></div>\n<p>Using memory that was allocated elsewhere:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::alloc::{alloc, Layout};\n\n<span class=\"kw\">fn </span>main() {\n    <span class=\"kw\">let </span>layout = Layout::array::&lt;u32&gt;(<span class=\"number\">16</span>).expect(<span class=\"string\">\"overflow cannot happen\"</span>);\n\n    <span class=\"kw\">let </span>vec = <span class=\"kw\">unsafe </span>{\n        <span class=\"kw\">let </span>mem = alloc(layout).cast::&lt;u32&gt;();\n        <span class=\"kw\">if </span>mem.is_null() {\n            <span class=\"kw\">return</span>;\n        }\n\n        mem.write(<span class=\"number\">1_000_000</span>);\n\n        Vec::from_raw_parts(mem, <span class=\"number\">1</span>, <span class=\"number\">16</span>)\n    };\n\n    <span class=\"macro\">assert_eq!</span>(vec, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">1_000_000</span>]);\n    <span class=\"macro\">assert_eq!</span>(vec.capacity(), <span class=\"number\">16</span>);\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_parts\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#729\">Source</a><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.from_parts\" class=\"fn\">from_parts</a>(\n    ptr: <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/ptr/non_null/struct.NonNull.html\" title=\"struct core::ptr::non_null::NonNull\">NonNull</a>&lt;T&gt;,\n    length: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>box_vec_non_null</code>)</span></div></span></summary><div class=\"docblock\"><p>Creates a <code>Vec&lt;T&gt;</code> directly from a <code>NonNull</code> pointer, a length, and a capacity.</p>\n<h5 id=\"safety-1\"><a class=\"doc-anchor\" href=\"#safety-1\">§</a>Safety</h5>\n<p>This is highly unsafe, due to the number of invariants that aren’t\nchecked:</p>\n<ul>\n<li><code>ptr</code> must have been allocated using the global allocator, such as via\nthe <a href=\"https://doc.rust-lang.org/nightly/alloc/alloc/fn.alloc.html\" title=\"fn alloc::alloc::alloc\"><code>alloc::alloc</code></a> function.</li>\n<li><code>T</code> needs to have the same alignment as what <code>ptr</code> was allocated with.\n(<code>T</code> having a less strict alignment is not sufficient, the alignment really\nneeds to be equal to satisfy the <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> requirement that memory must be\nallocated and deallocated with the same layout.)</li>\n<li>The size of <code>T</code> times the <code>capacity</code> (ie. the allocated size in bytes) needs\nto be the same size as the pointer was allocated with. (Because similar to\nalignment, <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> must be called with the same layout <code>size</code>.)</li>\n<li><code>length</code> needs to be less than or equal to <code>capacity</code>.</li>\n<li>The first <code>length</code> values must be properly initialized values of type <code>T</code>.</li>\n<li><code>capacity</code> needs to be the capacity that the pointer was allocated with.</li>\n<li>The allocated size in bytes must be no larger than <code>isize::MAX</code>.\nSee the safety documentation of <a href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html#method.offset\" title=\"method pointer::offset\"><code>pointer::offset</code></a>.</li>\n</ul>\n<p>These requirements are always upheld by any <code>ptr</code> that has been allocated\nvia <code>Vec&lt;T&gt;</code>. Other allocation sources are allowed if the invariants are\nupheld.</p>\n<p>Violating these may cause problems like corrupting the allocator’s\ninternal data structures. For example it is normally <strong>not</strong> safe\nto build a <code>Vec&lt;u8&gt;</code> from a pointer to a C <code>char</code> array with length\n<code>size_t</code>, doing so is only safe if the array was initially allocated by\na <code>Vec</code> or <code>String</code>.\nIt’s also not safe to build one from a <code>Vec&lt;u16&gt;</code> and its length, because\nthe allocator cares about the alignment, and these two types have different\nalignments. The buffer was allocated with alignment 2 (for <code>u16</code>), but after\nturning it into a <code>Vec&lt;u8&gt;</code> it’ll be deallocated with alignment 1. To avoid\nthese issues, it is often preferable to do casting/transmuting using\n<a href=\"https://doc.rust-lang.org/nightly/core/ptr/non_null/struct.NonNull.html#method.slice_from_raw_parts\" title=\"associated function core::ptr::non_null::NonNull::slice_from_raw_parts\"><code>NonNull::slice_from_raw_parts</code></a> instead.</p>\n<p>The ownership of <code>ptr</code> is effectively transferred to the\n<code>Vec&lt;T&gt;</code> which may then deallocate, reallocate or change the\ncontents of memory pointed to by the pointer at will. Ensure\nthat nothing else uses the pointer after calling this\nfunction.</p>\n<h5 id=\"examples-3\"><a class=\"doc-anchor\" href=\"#examples-3\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(box_vec_non_null)]\n\n</span><span class=\"kw\">use </span>std::ptr::NonNull;\n<span class=\"kw\">use </span>std::mem;\n\n<span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n\n<span class=\"comment\">// Prevent running `v`'s destructor so we are in complete control\n// of the allocation.\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = mem::ManuallyDrop::new(v);\n\n<span class=\"comment\">// Pull out the various important pieces of information about `v`\n</span><span class=\"kw\">let </span>p = <span class=\"kw\">unsafe </span>{ NonNull::new_unchecked(v.as_mut_ptr()) };\n<span class=\"kw\">let </span>len = v.len();\n<span class=\"kw\">let </span>cap = v.capacity();\n\n<span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// Overwrite memory with 4, 5, 6\n    </span><span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..len {\n        p.add(i).write(<span class=\"number\">4 </span>+ i);\n    }\n\n    <span class=\"comment\">// Put everything back together into a Vec\n    </span><span class=\"kw\">let </span>rebuilt = Vec::from_parts(p, len, cap);\n    <span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>]);\n}</code></pre></div>\n<p>Using memory that was allocated elsewhere:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(box_vec_non_null)]\n\n</span><span class=\"kw\">use </span>std::alloc::{alloc, Layout};\n<span class=\"kw\">use </span>std::ptr::NonNull;\n\n<span class=\"kw\">fn </span>main() {\n    <span class=\"kw\">let </span>layout = Layout::array::&lt;u32&gt;(<span class=\"number\">16</span>).expect(<span class=\"string\">\"overflow cannot happen\"</span>);\n\n    <span class=\"kw\">let </span>vec = <span class=\"kw\">unsafe </span>{\n        <span class=\"kw\">let </span><span class=\"prelude-val\">Some</span>(mem) = NonNull::new(alloc(layout).cast::&lt;u32&gt;()) <span class=\"kw\">else </span>{\n            <span class=\"kw\">return</span>;\n        };\n\n        mem.write(<span class=\"number\">1_000_000</span>);\n\n        Vec::from_parts(mem, <span class=\"number\">1</span>, <span class=\"number\">16</span>)\n    };\n\n    <span class=\"macro\">assert_eq!</span>(vec, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">1_000_000</span>]);\n    <span class=\"macro\">assert_eq!</span>(vec.capacity(), <span class=\"number\">16</span>);\n}</code></pre></div>\n</div></details></div></details>",0,"solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Vec%3CT,+A%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3004\">Source</a><a href=\"#impl-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.resize\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.5.0\">1.5.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3035\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.resize\" class=\"fn\">resize</a>(&amp;mut self, new_len: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, value: T)</h4></section></summary><div class=\"docblock\"><p>Resizes the <code>Vec</code> in-place so that <code>len</code> is equal to <code>new_len</code>.</p>\n<p>If <code>new_len</code> is greater than <code>len</code>, the <code>Vec</code> is extended by the\ndifference, with each additional slot filled with <code>value</code>.\nIf <code>new_len</code> is less than <code>len</code>, the <code>Vec</code> is simply truncated.</p>\n<p>This method requires <code>T</code> to implement <a href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\"><code>Clone</code></a>,\nin order to be able to clone the passed value.\nIf you need more flexibility (or want to rely on <a href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\"><code>Default</code></a> instead of\n<a href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\"><code>Clone</code></a>), use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.resize_with\" title=\"method alloc::vec::Vec::resize_with\"><code>Vec::resize_with</code></a>.\nIf you only need to resize to a smaller size, use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.truncate\" title=\"method alloc::vec::Vec::truncate\"><code>Vec::truncate</code></a>.</p>\n<h5 id=\"panics\"><a class=\"doc-anchor\" href=\"#panics\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"string\">\"hello\"</span>];\nvec.resize(<span class=\"number\">3</span>, <span class=\"string\">\"world\"</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"string\">\"hello\"</span>, <span class=\"string\">\"world\"</span>, <span class=\"string\">\"world\"</span>]);\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>, <span class=\"string\">'d'</span>];\nvec.resize(<span class=\"number\">2</span>, <span class=\"string\">'_'</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend_from_slice\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.6.0\">1.6.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3066\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.extend_from_slice\" class=\"fn\">extend_from_slice</a>(&amp;mut self, other: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>)</h4></section></summary><div class=\"docblock\"><p>Clones and appends all elements in a slice to the <code>Vec</code>.</p>\n<p>Iterates over the slice <code>other</code>, clones each element, and then appends\nit to this <code>Vec</code>. The <code>other</code> slice is traversed in-order.</p>\n<p>Note that this function is the same as <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.extend\" title=\"method alloc::vec::Vec::extend\"><code>extend</code></a>,\nexcept that it also works with slice elements that are Clone but not Copy.\nIf Rust gets specialization this function may be deprecated.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>];\nvec.extend_from_slice(<span class=\"kw-2\">&amp;</span>[<span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>]);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extend_from_within\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.53.0\">1.53.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3097-3099\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.extend_from_within\" class=\"fn\">extend_from_within</a>&lt;R&gt;(&amp;mut self, src: R)<div class=\"where\">where\n    R: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/range/trait.RangeBounds.html\" title=\"trait core::ops::range::RangeBounds\">RangeBounds</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Given a range <code>src</code>, clones a slice of elements in that range and appends it to the end.</p>\n<p><code>src</code> must be a range that can form a valid subslice of the <code>Vec</code>.</p>\n<h5 id=\"panics-1\"><a class=\"doc-anchor\" href=\"#panics-1\">§</a>Panics</h5>\n<p>Panics if starting index is greater than the end index\nor if the index is greater than the length of the vector.</p>\n<h5 id=\"examples-2\"><a class=\"doc-anchor\" href=\"#examples-2\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>characters = <span class=\"macro\">vec!</span>[<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>, <span class=\"string\">'d'</span>, <span class=\"string\">'e'</span>];\ncharacters.extend_from_within(<span class=\"number\">2</span>..);\n<span class=\"macro\">assert_eq!</span>(characters, [<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>, <span class=\"string\">'d'</span>, <span class=\"string\">'e'</span>, <span class=\"string\">'c'</span>, <span class=\"string\">'d'</span>, <span class=\"string\">'e'</span>]);\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>numbers = <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>];\nnumbers.extend_from_within(..<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(numbers, [<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>]);\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>strings = <span class=\"macro\">vec!</span>[String::from(<span class=\"string\">\"hello\"</span>), String::from(<span class=\"string\">\"world\"</span>), String::from(<span class=\"string\">\"!\"</span>)];\nstrings.extend_from_within(<span class=\"number\">1</span>..=<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(strings, [<span class=\"string\">\"hello\"</span>, <span class=\"string\">\"world\"</span>, <span class=\"string\">\"!\"</span>, <span class=\"string\">\"world\"</span>, <span class=\"string\">\"!\"</span>]);</code></pre></div>\n</div></details></div></details>",0,"solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Vec%3CT,+A%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3188\">Source</a><a href=\"#impl-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.dedup\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3205\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.dedup\" class=\"fn\">dedup</a>(&amp;mut self)</h4></section></summary><div class=\"docblock\"><p>Removes consecutive repeated elements in the vector according to the\n<a href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\"><code>PartialEq</code></a> trait implementation.</p>\n<p>If the vector is sorted, this removes all duplicates.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">2</span>];\n\nvec.dedup();\n\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">2</span>]);</code></pre></div>\n</div></details></div></details>",0,"solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Vec%3CT,+A%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3533\">Source</a><a href=\"#impl-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.splice\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.21.0\">1.21.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3643-3646\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.splice\" class=\"fn\">splice</a>&lt;R, I&gt;(\n    &amp;mut self,\n    range: R,\n    replace_with: I,\n) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/splice/struct.Splice.html\" title=\"struct alloc::vec::splice::Splice\">Splice</a>&lt;'_, &lt;I as <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.IntoIter\" title=\"type core::iter::traits::collect::IntoIterator::IntoIter\">IntoIter</a>, A&gt;<div class=\"where\">where\n    R: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/range/trait.RangeBounds.html\" title=\"trait core::ops::range::RangeBounds\">RangeBounds</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt;,\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&lt;Item = T&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Creates a splicing iterator that replaces the specified range in the vector\nwith the given <code>replace_with</code> iterator and yields the removed items.\n<code>replace_with</code> does not need to be the same length as <code>range</code>.</p>\n<p><code>range</code> is removed even if the <code>Splice</code> iterator is not consumed before it is dropped.</p>\n<p>It is unspecified how many elements are removed from the vector\nif the <code>Splice</code> value is leaked.</p>\n<p>The input iterator <code>replace_with</code> is only consumed when the <code>Splice</code> value is dropped.</p>\n<p>This is optimal if:</p>\n<ul>\n<li>The tail (elements in the vector after <code>range</code>) is empty,</li>\n<li>or <code>replace_with</code> yields fewer or equal elements than <code>range</code>’s length</li>\n<li>or the lower bound of its <code>size_hint()</code> is exact.</li>\n</ul>\n<p>Otherwise, a temporary vector is allocated and the tail is moved twice.</p>\n<h5 id=\"panics\"><a class=\"doc-anchor\" href=\"#panics\">§</a>Panics</h5>\n<p>Panics if the starting point is greater than the end point or if\nthe end point is greater than the length of the vector.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>];\n<span class=\"kw\">let </span>new = [<span class=\"number\">7</span>, <span class=\"number\">8</span>, <span class=\"number\">9</span>];\n<span class=\"kw\">let </span>u: Vec&lt;<span class=\"kw\">_</span>&gt; = v.splice(<span class=\"number\">1</span>..<span class=\"number\">3</span>, new).collect();\n<span class=\"macro\">assert_eq!</span>(v, [<span class=\"number\">1</span>, <span class=\"number\">7</span>, <span class=\"number\">8</span>, <span class=\"number\">9</span>, <span class=\"number\">4</span>]);\n<span class=\"macro\">assert_eq!</span>(u, [<span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n<p>Using <code>splice</code> to insert new items into a vector efficiently at a specific position\nindicated by an empty range:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">5</span>];\n<span class=\"kw\">let </span>new = [<span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>];\nv.splice(<span class=\"number\">1</span>..<span class=\"number\">1</span>, new);\n<span class=\"macro\">assert_eq!</span>(v, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>, <span class=\"number\">5</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.extract_if\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.87.0\">1.87.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3725-3728\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.extract_if\" class=\"fn\">extract_if</a>&lt;F, R&gt;(\n    &amp;mut self,\n    range: R,\n    filter: F,\n) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/extract_if/struct.ExtractIf.html\" title=\"struct alloc::vec::extract_if::ExtractIf\">ExtractIf</a>&lt;'_, T, F, A&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnMut.html\" title=\"trait core::ops::function::FnMut\">FnMut</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut T</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a>,\n    R: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/range/trait.RangeBounds.html\" title=\"trait core::ops::range::RangeBounds\">RangeBounds</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Creates an iterator which uses a closure to determine if an element in the range should be removed.</p>\n<p>If the closure returns <code>true</code>, the element is removed from the vector\nand yielded. If the closure returns <code>false</code>, or panics, the element\nremains in the vector and will not be yielded.</p>\n<p>Only elements that fall in the provided range are considered for extraction, but any elements\nafter the range will still have to be moved if any element has been extracted.</p>\n<p>If the returned <code>ExtractIf</code> is not exhausted, e.g. because it is dropped without iterating\nor the iteration short-circuits, then the remaining elements will be retained.\nUse <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.retain_mut\" title=\"method alloc::vec::Vec::retain_mut\"><code>retain_mut</code></a> with a negated predicate if you do not need the returned iterator.</p>\n<p>Using this method is equivalent to the following code:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>i = range.start;\n<span class=\"kw\">let </span>end_items = vec.len() - range.end;\n\n<span class=\"kw\">while </span>i &lt; vec.len() - end_items {\n    <span class=\"kw\">if </span>some_predicate(<span class=\"kw-2\">&amp;mut </span>vec[i]) {\n        <span class=\"kw\">let </span>val = vec.remove(i);\n        <span class=\"comment\">// your code here\n    </span>} <span class=\"kw\">else </span>{\n        i += <span class=\"number\">1</span>;\n    }\n}\n</code></pre></div>\n<p>But <code>extract_if</code> is easier to use. <code>extract_if</code> is also more efficient,\nbecause it can backshift the elements of the array in bulk.</p>\n<p>The iterator also lets you mutate the value of each element in the\nclosure, regardless of whether you choose to keep or remove it.</p>\n<h5 id=\"panics-1\"><a class=\"doc-anchor\" href=\"#panics-1\">§</a>Panics</h5>\n<p>If <code>range</code> is out of bounds.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<p>Splitting a vector into even and odd values, reusing the original vector:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>numbers = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>, <span class=\"number\">8</span>, <span class=\"number\">9</span>, <span class=\"number\">11</span>, <span class=\"number\">13</span>, <span class=\"number\">14</span>, <span class=\"number\">15</span>];\n\n<span class=\"kw\">let </span>evens = numbers.extract_if(.., |x| <span class=\"kw-2\">*</span>x % <span class=\"number\">2 </span>== <span class=\"number\">0</span>).collect::&lt;Vec&lt;<span class=\"kw\">_</span>&gt;&gt;();\n<span class=\"kw\">let </span>odds = numbers;\n\n<span class=\"macro\">assert_eq!</span>(evens, <span class=\"macro\">vec!</span>[<span class=\"number\">2</span>, <span class=\"number\">4</span>, <span class=\"number\">6</span>, <span class=\"number\">8</span>, <span class=\"number\">14</span>]);\n<span class=\"macro\">assert_eq!</span>(odds, <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">3</span>, <span class=\"number\">5</span>, <span class=\"number\">9</span>, <span class=\"number\">11</span>, <span class=\"number\">13</span>, <span class=\"number\">15</span>]);</code></pre></div>\n<p>Using the range argument to only process a part of the vector:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>items = <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>];\n<span class=\"kw\">let </span>ones = items.extract_if(<span class=\"number\">7</span>.., |x| <span class=\"kw-2\">*</span>x == <span class=\"number\">1</span>).collect::&lt;Vec&lt;<span class=\"kw\">_</span>&gt;&gt;();\n<span class=\"macro\">assert_eq!</span>(items, <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">2</span>, <span class=\"number\">2</span>, <span class=\"number\">2</span>]);\n<span class=\"macro\">assert_eq!</span>(ones.len(), <span class=\"number\">3</span>);</code></pre></div>\n</div></details></div></details>",0,"solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Vec%3CT,+A%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#734\">Source</a><a href=\"#impl-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.new_in\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#751\">Source</a><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.new_in\" class=\"fn\">new_in</a>(alloc: A) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Constructs a new, empty <code>Vec&lt;T, A&gt;</code>.</p>\n<p>The vector will not allocate until elements are pushed onto it.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api)]\n\n</span><span class=\"kw\">use </span>std::alloc::System;\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec: Vec&lt;i32, <span class=\"kw\">_</span>&gt; = Vec::new_in(System);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.with_capacity_in\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#814\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.with_capacity_in\" class=\"fn\">with_capacity_in</a>(capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, alloc: A) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Constructs a new, empty <code>Vec&lt;T, A&gt;</code> with at least the specified capacity\nwith the provided allocator.</p>\n<p>The vector will be able to hold at least <code>capacity</code> elements without\nreallocating. This method is allowed to allocate for more elements than\n<code>capacity</code>. If <code>capacity</code> is zero, the vector will not allocate.</p>\n<p>It is important to note that although the returned vector has the\nminimum <em>capacity</em> specified, the vector will have a zero <em>length</em>. For\nan explanation of the difference between length and capacity, see\n<em><a href=\"#capacity-and-reallocation\">Capacity and reallocation</a></em>.</p>\n<p>If it is important to know the exact allocated capacity of a <code>Vec</code>,\nalways use the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.capacity\" title=\"method alloc::vec::Vec::capacity\"><code>capacity</code></a> method after construction.</p>\n<p>For <code>Vec&lt;T, A&gt;</code> where <code>T</code> is a zero-sized type, there will be no allocation\nand the capacity will always be <code>usize::MAX</code>.</p>\n<h5 id=\"panics\"><a class=\"doc-anchor\" href=\"#panics\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api)]\n\n</span><span class=\"kw\">use </span>std::alloc::System;\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = Vec::with_capacity_in(<span class=\"number\">10</span>, System);\n\n<span class=\"comment\">// The vector contains no items, even though it has capacity for more\n</span><span class=\"macro\">assert_eq!</span>(vec.len(), <span class=\"number\">0</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\n\n<span class=\"comment\">// These are all done without reallocating...\n</span><span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..<span class=\"number\">10 </span>{\n    vec.push(i);\n}\n<span class=\"macro\">assert_eq!</span>(vec.len(), <span class=\"number\">10</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\n\n<span class=\"comment\">// ...but this may make the vector reallocate\n</span>vec.push(<span class=\"number\">11</span>);\n<span class=\"macro\">assert_eq!</span>(vec.len(), <span class=\"number\">11</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">11</span>);\n\n<span class=\"comment\">// A vector of a zero-sized type will always over-allocate, since no\n// allocation is necessary\n</span><span class=\"kw\">let </span>vec_units = Vec::&lt;(), System&gt;::with_capacity_in(<span class=\"number\">10</span>, System);\n<span class=\"macro\">assert_eq!</span>(vec_units.capacity(), usize::MAX);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_with_capacity_in\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#832\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.try_with_capacity_in\" class=\"fn\">try_with_capacity_in</a>(\n    capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    alloc: A,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;, <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/struct.TryReserveError.html\" title=\"struct alloc::collections::TryReserveError\">TryReserveError</a>&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Constructs a new, empty <code>Vec&lt;T, A&gt;</code> with at least the specified capacity\nwith the provided allocator.</p>\n<p>The vector will be able to hold at least <code>capacity</code> elements without\nreallocating. This method is allowed to allocate for more elements than\n<code>capacity</code>. If <code>capacity</code> is zero, the vector will not allocate.</p>\n<h5 id=\"errors\"><a class=\"doc-anchor\" href=\"#errors\">§</a>Errors</h5>\n<p>Returns an error if the capacity exceeds <code>isize::MAX</code> <em>bytes</em>,\nor if the allocator reports allocation failure.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_raw_parts_in\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#946\">Source</a><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.from_raw_parts_in\" class=\"fn\">from_raw_parts_in</a>(\n    ptr: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html\">*mut T</a>,\n    length: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    alloc: A,\n) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Creates a <code>Vec&lt;T, A&gt;</code> directly from a pointer, a length, a capacity,\nand an allocator.</p>\n<h5 id=\"safety\"><a class=\"doc-anchor\" href=\"#safety\">§</a>Safety</h5>\n<p>This is highly unsafe, due to the number of invariants that aren’t\nchecked:</p>\n<ul>\n<li><code>ptr</code> must be <a href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html#currently-allocated-memory\" title=\"trait core::alloc::Allocator\"><em>currently allocated</em></a> via the given allocator <code>alloc</code>.</li>\n<li><code>T</code> needs to have the same alignment as what <code>ptr</code> was allocated with.\n(<code>T</code> having a less strict alignment is not sufficient, the alignment really\nneeds to be equal to satisfy the <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> requirement that memory must be\nallocated and deallocated with the same layout.)</li>\n<li>The size of <code>T</code> times the <code>capacity</code> (ie. the allocated size in bytes) needs\nto be the same size as the pointer was allocated with. (Because similar to\nalignment, <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> must be called with the same layout <code>size</code>.)</li>\n<li><code>length</code> needs to be less than or equal to <code>capacity</code>.</li>\n<li>The first <code>length</code> values must be properly initialized values of type <code>T</code>.</li>\n<li><code>capacity</code> needs to <a href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html#memory-fitting\" title=\"trait core::alloc::Allocator\"><em>fit</em></a> the layout size that the pointer was allocated with.</li>\n<li>The allocated size in bytes must be no larger than <code>isize::MAX</code>.\nSee the safety documentation of <a href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html#method.offset\" title=\"method pointer::offset\"><code>pointer::offset</code></a>.</li>\n</ul>\n<p>These requirements are always upheld by any <code>ptr</code> that has been allocated\nvia <code>Vec&lt;T, A&gt;</code>. Other allocation sources are allowed if the invariants are\nupheld.</p>\n<p>Violating these may cause problems like corrupting the allocator’s\ninternal data structures. For example it is <strong>not</strong> safe\nto build a <code>Vec&lt;u8&gt;</code> from a pointer to a C <code>char</code> array with length <code>size_t</code>.\nIt’s also not safe to build one from a <code>Vec&lt;u16&gt;</code> and its length, because\nthe allocator cares about the alignment, and these two types have different\nalignments. The buffer was allocated with alignment 2 (for <code>u16</code>), but after\nturning it into a <code>Vec&lt;u8&gt;</code> it’ll be deallocated with alignment 1.</p>\n<p>The ownership of <code>ptr</code> is effectively transferred to the\n<code>Vec&lt;T&gt;</code> which may then deallocate, reallocate or change the\ncontents of memory pointed to by the pointer at will. Ensure\nthat nothing else uses the pointer after calling this\nfunction.</p>\n<h5 id=\"examples-2\"><a class=\"doc-anchor\" href=\"#examples-2\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api)]\n\n</span><span class=\"kw\">use </span>std::alloc::System;\n\n<span class=\"kw\">use </span>std::ptr;\n<span class=\"kw\">use </span>std::mem;\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = Vec::with_capacity_in(<span class=\"number\">3</span>, System);\nv.push(<span class=\"number\">1</span>);\nv.push(<span class=\"number\">2</span>);\nv.push(<span class=\"number\">3</span>);\n\n<span class=\"comment\">// Prevent running `v`'s destructor so we are in complete control\n// of the allocation.\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = mem::ManuallyDrop::new(v);\n\n<span class=\"comment\">// Pull out the various important pieces of information about `v`\n</span><span class=\"kw\">let </span>p = v.as_mut_ptr();\n<span class=\"kw\">let </span>len = v.len();\n<span class=\"kw\">let </span>cap = v.capacity();\n<span class=\"kw\">let </span>alloc = v.allocator();\n\n<span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// Overwrite memory with 4, 5, 6\n    </span><span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..len {\n        ptr::write(p.add(i), <span class=\"number\">4 </span>+ i);\n    }\n\n    <span class=\"comment\">// Put everything back together into a Vec\n    </span><span class=\"kw\">let </span>rebuilt = Vec::from_raw_parts_in(p, len, cap, alloc.clone());\n    <span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>]);\n}</code></pre></div>\n<p>Using memory that was allocated elsewhere:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api)]\n\n</span><span class=\"kw\">use </span>std::alloc::{AllocError, Allocator, Global, Layout};\n\n<span class=\"kw\">fn </span>main() {\n    <span class=\"kw\">let </span>layout = Layout::array::&lt;u32&gt;(<span class=\"number\">16</span>).expect(<span class=\"string\">\"overflow cannot happen\"</span>);\n\n    <span class=\"kw\">let </span>vec = <span class=\"kw\">unsafe </span>{\n        <span class=\"kw\">let </span>mem = <span class=\"kw\">match </span>Global.allocate(layout) {\n            <span class=\"prelude-val\">Ok</span>(mem) =&gt; mem.cast::&lt;u32&gt;().as_ptr(),\n            <span class=\"prelude-val\">Err</span>(AllocError) =&gt; <span class=\"kw\">return</span>,\n        };\n\n        mem.write(<span class=\"number\">1_000_000</span>);\n\n        Vec::from_raw_parts_in(mem, <span class=\"number\">1</span>, <span class=\"number\">16</span>, Global)\n    };\n\n    <span class=\"macro\">assert_eq!</span>(vec, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">1_000_000</span>]);\n    <span class=\"macro\">assert_eq!</span>(vec.capacity(), <span class=\"number\">16</span>);\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_parts_in\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1062\">Source</a><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.from_parts_in\" class=\"fn\">from_parts_in</a>(\n    ptr: <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/ptr/non_null/struct.NonNull.html\" title=\"struct core::ptr::non_null::NonNull\">NonNull</a>&lt;T&gt;,\n    length: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n    alloc: A,\n) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Creates a <code>Vec&lt;T, A&gt;</code> directly from a <code>NonNull</code> pointer, a length, a capacity,\nand an allocator.</p>\n<h5 id=\"safety-1\"><a class=\"doc-anchor\" href=\"#safety-1\">§</a>Safety</h5>\n<p>This is highly unsafe, due to the number of invariants that aren’t\nchecked:</p>\n<ul>\n<li><code>ptr</code> must be <a href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html#currently-allocated-memory\" title=\"trait core::alloc::Allocator\"><em>currently allocated</em></a> via the given allocator <code>alloc</code>.</li>\n<li><code>T</code> needs to have the same alignment as what <code>ptr</code> was allocated with.\n(<code>T</code> having a less strict alignment is not sufficient, the alignment really\nneeds to be equal to satisfy the <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> requirement that memory must be\nallocated and deallocated with the same layout.)</li>\n<li>The size of <code>T</code> times the <code>capacity</code> (ie. the allocated size in bytes) needs\nto be the same size as the pointer was allocated with. (Because similar to\nalignment, <a href=\"https://doc.rust-lang.org/nightly/core/alloc/global/trait.GlobalAlloc.html#tymethod.dealloc\" title=\"method core::alloc::global::GlobalAlloc::dealloc\"><code>dealloc</code></a> must be called with the same layout <code>size</code>.)</li>\n<li><code>length</code> needs to be less than or equal to <code>capacity</code>.</li>\n<li>The first <code>length</code> values must be properly initialized values of type <code>T</code>.</li>\n<li><code>capacity</code> needs to <a href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html#memory-fitting\" title=\"trait core::alloc::Allocator\"><em>fit</em></a> the layout size that the pointer was allocated with.</li>\n<li>The allocated size in bytes must be no larger than <code>isize::MAX</code>.\nSee the safety documentation of <a href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html#method.offset\" title=\"method pointer::offset\"><code>pointer::offset</code></a>.</li>\n</ul>\n<p>These requirements are always upheld by any <code>ptr</code> that has been allocated\nvia <code>Vec&lt;T, A&gt;</code>. Other allocation sources are allowed if the invariants are\nupheld.</p>\n<p>Violating these may cause problems like corrupting the allocator’s\ninternal data structures. For example it is <strong>not</strong> safe\nto build a <code>Vec&lt;u8&gt;</code> from a pointer to a C <code>char</code> array with length <code>size_t</code>.\nIt’s also not safe to build one from a <code>Vec&lt;u16&gt;</code> and its length, because\nthe allocator cares about the alignment, and these two types have different\nalignments. The buffer was allocated with alignment 2 (for <code>u16</code>), but after\nturning it into a <code>Vec&lt;u8&gt;</code> it’ll be deallocated with alignment 1.</p>\n<p>The ownership of <code>ptr</code> is effectively transferred to the\n<code>Vec&lt;T&gt;</code> which may then deallocate, reallocate or change the\ncontents of memory pointed to by the pointer at will. Ensure\nthat nothing else uses the pointer after calling this\nfunction.</p>\n<h5 id=\"examples-3\"><a class=\"doc-anchor\" href=\"#examples-3\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api, box_vec_non_null)]\n\n</span><span class=\"kw\">use </span>std::alloc::System;\n\n<span class=\"kw\">use </span>std::ptr::NonNull;\n<span class=\"kw\">use </span>std::mem;\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = Vec::with_capacity_in(<span class=\"number\">3</span>, System);\nv.push(<span class=\"number\">1</span>);\nv.push(<span class=\"number\">2</span>);\nv.push(<span class=\"number\">3</span>);\n\n<span class=\"comment\">// Prevent running `v`'s destructor so we are in complete control\n// of the allocation.\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = mem::ManuallyDrop::new(v);\n\n<span class=\"comment\">// Pull out the various important pieces of information about `v`\n</span><span class=\"kw\">let </span>p = <span class=\"kw\">unsafe </span>{ NonNull::new_unchecked(v.as_mut_ptr()) };\n<span class=\"kw\">let </span>len = v.len();\n<span class=\"kw\">let </span>cap = v.capacity();\n<span class=\"kw\">let </span>alloc = v.allocator();\n\n<span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// Overwrite memory with 4, 5, 6\n    </span><span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..len {\n        p.add(i).write(<span class=\"number\">4 </span>+ i);\n    }\n\n    <span class=\"comment\">// Put everything back together into a Vec\n    </span><span class=\"kw\">let </span>rebuilt = Vec::from_parts_in(p, len, cap, alloc.clone());\n    <span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>]);\n}</code></pre></div>\n<p>Using memory that was allocated elsewhere:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api, box_vec_non_null)]\n\n</span><span class=\"kw\">use </span>std::alloc::{AllocError, Allocator, Global, Layout};\n\n<span class=\"kw\">fn </span>main() {\n    <span class=\"kw\">let </span>layout = Layout::array::&lt;u32&gt;(<span class=\"number\">16</span>).expect(<span class=\"string\">\"overflow cannot happen\"</span>);\n\n    <span class=\"kw\">let </span>vec = <span class=\"kw\">unsafe </span>{\n        <span class=\"kw\">let </span>mem = <span class=\"kw\">match </span>Global.allocate(layout) {\n            <span class=\"prelude-val\">Ok</span>(mem) =&gt; mem.cast::&lt;u32&gt;(),\n            <span class=\"prelude-val\">Err</span>(AllocError) =&gt; <span class=\"kw\">return</span>,\n        };\n\n        mem.write(<span class=\"number\">1_000_000</span>);\n\n        Vec::from_parts_in(mem, <span class=\"number\">1</span>, <span class=\"number\">16</span>, Global)\n    };\n\n    <span class=\"macro\">assert_eq!</span>(vec, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">1_000_000</span>]);\n    <span class=\"macro\">assert_eq!</span>(vec.capacity(), <span class=\"number\">16</span>);\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_raw_parts\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1100\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.into_raw_parts\" class=\"fn\">into_raw_parts</a>(self) -&gt; (<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html\">*mut T</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>vec_into_raw_parts</code>)</span></div></span></summary><div class=\"docblock\"><p>Decomposes a <code>Vec&lt;T&gt;</code> into its raw components: <code>(pointer, length, capacity)</code>.</p>\n<p>Returns the raw pointer to the underlying data, the length of\nthe vector (in elements), and the allocated capacity of the\ndata (in elements). These are the same arguments in the same\norder as the arguments to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_raw_parts\" title=\"associated function alloc::vec::Vec::from_raw_parts\"><code>from_raw_parts</code></a>.</p>\n<p>After calling this function, the caller is responsible for the\nmemory previously managed by the <code>Vec</code>. The only way to do\nthis is to convert the raw pointer, length, and capacity back\ninto a <code>Vec</code> with the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_raw_parts\" title=\"associated function alloc::vec::Vec::from_raw_parts\"><code>from_raw_parts</code></a> function, allowing\nthe destructor to perform the cleanup.</p>\n<h5 id=\"examples-4\"><a class=\"doc-anchor\" href=\"#examples-4\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(vec_into_raw_parts)]\n</span><span class=\"kw\">let </span>v: Vec&lt;i32&gt; = <span class=\"macro\">vec!</span>[-<span class=\"number\">1</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>];\n\n<span class=\"kw\">let </span>(ptr, len, cap) = v.into_raw_parts();\n\n<span class=\"kw\">let </span>rebuilt = <span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// We can now make changes to the components, such as\n    // transmuting the raw pointer to a compatible type.\n    </span><span class=\"kw\">let </span>ptr = ptr <span class=\"kw\">as </span><span class=\"kw-2\">*mut </span>u32;\n\n    Vec::from_raw_parts(ptr, len, cap)\n};\n<span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4294967295</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_parts\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1142\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.into_parts\" class=\"fn\">into_parts</a>(self) -&gt; (<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/ptr/non_null/struct.NonNull.html\" title=\"struct core::ptr::non_null::NonNull\">NonNull</a>&lt;T&gt;, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>box_vec_non_null</code>)</span></div></span></summary><div class=\"docblock\"><p>Decomposes a <code>Vec&lt;T&gt;</code> into its raw components: <code>(NonNull pointer, length, capacity)</code>.</p>\n<p>Returns the <code>NonNull</code> pointer to the underlying data, the length of\nthe vector (in elements), and the allocated capacity of the\ndata (in elements). These are the same arguments in the same\norder as the arguments to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_parts\" title=\"associated function alloc::vec::Vec::from_parts\"><code>from_parts</code></a>.</p>\n<p>After calling this function, the caller is responsible for the\nmemory previously managed by the <code>Vec</code>. The only way to do\nthis is to convert the <code>NonNull</code> pointer, length, and capacity back\ninto a <code>Vec</code> with the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_parts\" title=\"associated function alloc::vec::Vec::from_parts\"><code>from_parts</code></a> function, allowing\nthe destructor to perform the cleanup.</p>\n<h5 id=\"examples-5\"><a class=\"doc-anchor\" href=\"#examples-5\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(vec_into_raw_parts, box_vec_non_null)]\n\n</span><span class=\"kw\">let </span>v: Vec&lt;i32&gt; = <span class=\"macro\">vec!</span>[-<span class=\"number\">1</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>];\n\n<span class=\"kw\">let </span>(ptr, len, cap) = v.into_parts();\n\n<span class=\"kw\">let </span>rebuilt = <span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// We can now make changes to the components, such as\n    // transmuting the raw pointer to a compatible type.\n    </span><span class=\"kw\">let </span>ptr = ptr.cast::&lt;u32&gt;();\n\n    Vec::from_parts(ptr, len, cap)\n};\n<span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4294967295</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_raw_parts_with_alloc\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1188\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.into_raw_parts_with_alloc\" class=\"fn\">into_raw_parts_with_alloc</a>(self) -&gt; (<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html\">*mut T</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, A)</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Decomposes a <code>Vec&lt;T&gt;</code> into its raw components: <code>(pointer, length, capacity, allocator)</code>.</p>\n<p>Returns the raw pointer to the underlying data, the length of the vector (in elements),\nthe allocated capacity of the data (in elements), and the allocator. These are the same\narguments in the same order as the arguments to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_raw_parts_in\" title=\"associated function alloc::vec::Vec::from_raw_parts_in\"><code>from_raw_parts_in</code></a>.</p>\n<p>After calling this function, the caller is responsible for the\nmemory previously managed by the <code>Vec</code>. The only way to do\nthis is to convert the raw pointer, length, and capacity back\ninto a <code>Vec</code> with the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_raw_parts_in\" title=\"associated function alloc::vec::Vec::from_raw_parts_in\"><code>from_raw_parts_in</code></a> function, allowing\nthe destructor to perform the cleanup.</p>\n<h5 id=\"examples-6\"><a class=\"doc-anchor\" href=\"#examples-6\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api, vec_into_raw_parts)]\n\n</span><span class=\"kw\">use </span>std::alloc::System;\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v: Vec&lt;i32, System&gt; = Vec::new_in(System);\nv.push(-<span class=\"number\">1</span>);\nv.push(<span class=\"number\">0</span>);\nv.push(<span class=\"number\">1</span>);\n\n<span class=\"kw\">let </span>(ptr, len, cap, alloc) = v.into_raw_parts_with_alloc();\n\n<span class=\"kw\">let </span>rebuilt = <span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// We can now make changes to the components, such as\n    // transmuting the raw pointer to a compatible type.\n    </span><span class=\"kw\">let </span>ptr = ptr <span class=\"kw\">as </span><span class=\"kw-2\">*mut </span>u32;\n\n    Vec::from_raw_parts_in(ptr, len, cap, alloc)\n};\n<span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4294967295</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_parts_with_alloc\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1239\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.into_parts_with_alloc\" class=\"fn\">into_parts_with_alloc</a>(self) -&gt; (<a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/ptr/non_null/struct.NonNull.html\" title=\"struct core::ptr::non_null::NonNull\">NonNull</a>&lt;T&gt;, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, A)</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Decomposes a <code>Vec&lt;T&gt;</code> into its raw components: <code>(NonNull pointer, length, capacity, allocator)</code>.</p>\n<p>Returns the <code>NonNull</code> pointer to the underlying data, the length of the vector (in elements),\nthe allocated capacity of the data (in elements), and the allocator. These are the same\narguments in the same order as the arguments to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_parts_in\" title=\"associated function alloc::vec::Vec::from_parts_in\"><code>from_parts_in</code></a>.</p>\n<p>After calling this function, the caller is responsible for the\nmemory previously managed by the <code>Vec</code>. The only way to do\nthis is to convert the <code>NonNull</code> pointer, length, and capacity back\ninto a <code>Vec</code> with the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.from_parts_in\" title=\"associated function alloc::vec::Vec::from_parts_in\"><code>from_parts_in</code></a> function, allowing\nthe destructor to perform the cleanup.</p>\n<h5 id=\"examples-7\"><a class=\"doc-anchor\" href=\"#examples-7\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(allocator_api, vec_into_raw_parts, box_vec_non_null)]\n\n</span><span class=\"kw\">use </span>std::alloc::System;\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v: Vec&lt;i32, System&gt; = Vec::new_in(System);\nv.push(-<span class=\"number\">1</span>);\nv.push(<span class=\"number\">0</span>);\nv.push(<span class=\"number\">1</span>);\n\n<span class=\"kw\">let </span>(ptr, len, cap, alloc) = v.into_parts_with_alloc();\n\n<span class=\"kw\">let </span>rebuilt = <span class=\"kw\">unsafe </span>{\n    <span class=\"comment\">// We can now make changes to the components, such as\n    // transmuting the raw pointer to a compatible type.\n    </span><span class=\"kw\">let </span>ptr = ptr.cast::&lt;u32&gt;();\n\n    Vec::from_parts_in(ptr, len, cap, alloc)\n};\n<span class=\"macro\">assert_eq!</span>(rebuilt, [<span class=\"number\">4294967295</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.capacity\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.87.0\">1.0.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1271\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.capacity\" class=\"fn\">capacity</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a></h4></section></summary><div class=\"docblock\"><p>Returns the total number of elements the vector can hold without\nreallocating.</p>\n<h5 id=\"examples-8\"><a class=\"doc-anchor\" href=\"#examples-8\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec: Vec&lt;i32&gt; = Vec::with_capacity(<span class=\"number\">10</span>);\nvec.push(<span class=\"number\">42</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);</code></pre></div>\n<p>A vector with zero-sized elements will always have a capacity of usize::MAX:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#[derive(Clone)]\n</span><span class=\"kw\">struct </span>ZeroSized;\n\n<span class=\"kw\">fn </span>main() {\n    <span class=\"macro\">assert_eq!</span>(std::mem::size_of::&lt;ZeroSized&gt;(), <span class=\"number\">0</span>);\n    <span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[ZeroSized; <span class=\"number\">0</span>];\n    <span class=\"macro\">assert_eq!</span>(v.capacity(), usize::MAX);\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.reserve\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1296\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.reserve\" class=\"fn\">reserve</a>(&amp;mut self, additional: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><div class=\"docblock\"><p>Reserves capacity for at least <code>additional</code> more elements to be inserted\nin the given <code>Vec&lt;T&gt;</code>. The collection may reserve more space to\nspeculatively avoid frequent reallocations. After calling <code>reserve</code>,\ncapacity will be greater than or equal to <code>self.len() + additional</code>.\nDoes nothing if capacity is already sufficient.</p>\n<h5 id=\"panics-1\"><a class=\"doc-anchor\" href=\"#panics-1\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-9\"><a class=\"doc-anchor\" href=\"#examples-9\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>];\nvec.reserve(<span class=\"number\">10</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">11</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.reserve_exact\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1327\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.reserve_exact\" class=\"fn\">reserve_exact</a>(&amp;mut self, additional: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><div class=\"docblock\"><p>Reserves the minimum capacity for at least <code>additional</code> more elements to\nbe inserted in the given <code>Vec&lt;T&gt;</code>. Unlike <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.reserve\" title=\"method alloc::vec::Vec::reserve\"><code>reserve</code></a>, this will not\ndeliberately over-allocate to speculatively avoid frequent allocations.\nAfter calling <code>reserve_exact</code>, capacity will be greater than or equal to\n<code>self.len() + additional</code>. Does nothing if the capacity is already\nsufficient.</p>\n<p>Note that the allocator may give the collection more space than it\nrequests. Therefore, capacity can not be relied upon to be precisely\nminimal. Prefer <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.reserve\" title=\"method alloc::vec::Vec::reserve\"><code>reserve</code></a> if future insertions are expected.</p>\n<h5 id=\"panics-2\"><a class=\"doc-anchor\" href=\"#panics-2\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-10\"><a class=\"doc-anchor\" href=\"#examples-10\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>];\nvec.reserve_exact(<span class=\"number\">10</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">11</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_reserve\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.57.0\">1.57.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1364\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.try_reserve\" class=\"fn\">try_reserve</a>(&amp;mut self, additional: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.unit.html\">()</a>, <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/struct.TryReserveError.html\" title=\"struct alloc::collections::TryReserveError\">TryReserveError</a>&gt;</h4></section></summary><div class=\"docblock\"><p>Tries to reserve capacity for at least <code>additional</code> more elements to be inserted\nin the given <code>Vec&lt;T&gt;</code>. The collection may reserve more space to speculatively avoid\nfrequent reallocations. After calling <code>try_reserve</code>, capacity will be\ngreater than or equal to <code>self.len() + additional</code> if it returns\n<code>Ok(())</code>. Does nothing if capacity is already sufficient. This method\npreserves the contents even if an error occurs.</p>\n<h5 id=\"errors-1\"><a class=\"doc-anchor\" href=\"#errors-1\">§</a>Errors</h5>\n<p>If the capacity overflows, or the allocator reports a failure, then an error\nis returned.</p>\n<h5 id=\"examples-11\"><a class=\"doc-anchor\" href=\"#examples-11\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::collections::TryReserveError;\n\n<span class=\"kw\">fn </span>process_data(data: <span class=\"kw-2\">&amp;</span>[u32]) -&gt; <span class=\"prelude-ty\">Result</span>&lt;Vec&lt;u32&gt;, TryReserveError&gt; {\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>output = Vec::new();\n\n    <span class=\"comment\">// Pre-reserve the memory, exiting if we can't\n    </span>output.try_reserve(data.len())<span class=\"question-mark\">?</span>;\n\n    <span class=\"comment\">// Now we know this can't OOM in the middle of our complex work\n    </span>output.extend(data.iter().map(|<span class=\"kw-2\">&amp;</span>val| {\n        val * <span class=\"number\">2 </span>+ <span class=\"number\">5 </span><span class=\"comment\">// very complicated\n    </span>}));\n\n    <span class=\"prelude-val\">Ok</span>(output)\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_reserve_exact\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.57.0\">1.57.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1407\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.try_reserve_exact\" class=\"fn\">try_reserve_exact</a>(\n    &amp;mut self,\n    additional: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.unit.html\">()</a>, <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/collections/struct.TryReserveError.html\" title=\"struct alloc::collections::TryReserveError\">TryReserveError</a>&gt;</h4></section></summary><div class=\"docblock\"><p>Tries to reserve the minimum capacity for at least <code>additional</code>\nelements to be inserted in the given <code>Vec&lt;T&gt;</code>. Unlike <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.try_reserve\" title=\"method alloc::vec::Vec::try_reserve\"><code>try_reserve</code></a>,\nthis will not deliberately over-allocate to speculatively avoid frequent\nallocations. After calling <code>try_reserve_exact</code>, capacity will be greater\nthan or equal to <code>self.len() + additional</code> if it returns <code>Ok(())</code>.\nDoes nothing if the capacity is already sufficient.</p>\n<p>Note that the allocator may give the collection more space than it\nrequests. Therefore, capacity can not be relied upon to be precisely\nminimal. Prefer <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.try_reserve\" title=\"method alloc::vec::Vec::try_reserve\"><code>try_reserve</code></a> if future insertions are expected.</p>\n<h5 id=\"errors-2\"><a class=\"doc-anchor\" href=\"#errors-2\">§</a>Errors</h5>\n<p>If the capacity overflows, or the allocator reports a failure, then an error\nis returned.</p>\n<h5 id=\"examples-12\"><a class=\"doc-anchor\" href=\"#examples-12\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::collections::TryReserveError;\n\n<span class=\"kw\">fn </span>process_data(data: <span class=\"kw-2\">&amp;</span>[u32]) -&gt; <span class=\"prelude-ty\">Result</span>&lt;Vec&lt;u32&gt;, TryReserveError&gt; {\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>output = Vec::new();\n\n    <span class=\"comment\">// Pre-reserve the memory, exiting if we can't\n    </span>output.try_reserve_exact(data.len())<span class=\"question-mark\">?</span>;\n\n    <span class=\"comment\">// Now we know this can't OOM in the middle of our complex work\n    </span>output.extend(data.iter().map(|<span class=\"kw-2\">&amp;</span>val| {\n        val * <span class=\"number\">2 </span>+ <span class=\"number\">5 </span><span class=\"comment\">// very complicated\n    </span>}));\n\n    <span class=\"prelude-val\">Ok</span>(output)\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.shrink_to_fit\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1432\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.shrink_to_fit\" class=\"fn\">shrink_to_fit</a>(&amp;mut self)</h4></section></summary><div class=\"docblock\"><p>Shrinks the capacity of the vector as much as possible.</p>\n<p>The behavior of this method depends on the allocator, which may either shrink the vector\nin-place or reallocate. The resulting vector might still have some excess capacity, just as\nis the case for <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.with_capacity\" title=\"associated function alloc::vec::Vec::with_capacity\"><code>with_capacity</code></a>. See <a href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html#method.shrink\" title=\"method core::alloc::Allocator::shrink\"><code>Allocator::shrink</code></a> for more details.</p>\n<h5 id=\"examples-13\"><a class=\"doc-anchor\" href=\"#examples-13\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = Vec::with_capacity(<span class=\"number\">10</span>);\nvec.extend([<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\nvec.shrink_to_fit();\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">3</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.shrink_to\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.56.0\">1.56.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1462\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.shrink_to\" class=\"fn\">shrink_to</a>(&amp;mut self, min_capacity: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><div class=\"docblock\"><p>Shrinks the capacity of the vector with a lower bound.</p>\n<p>The capacity will remain at least as large as both the length\nand the supplied value.</p>\n<p>If the current capacity is less than the lower limit, this is a no-op.</p>\n<h5 id=\"examples-14\"><a class=\"doc-anchor\" href=\"#examples-14\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = Vec::with_capacity(<span class=\"number\">10</span>);\nvec.extend([<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\nvec.shrink_to(<span class=\"number\">4</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">4</span>);\nvec.shrink_to(<span class=\"number\">0</span>);\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">3</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_boxed_slice\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1496\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.into_boxed_slice\" class=\"fn\">into_boxed_slice</a>(self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/boxed/struct.Box.html\" title=\"struct alloc::boxed::Box\">Box</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>, A&gt;</h4></section></summary><div class=\"docblock\"><p>Converts the vector into <a href=\"https://doc.rust-lang.org/nightly/alloc/boxed/struct.Box.html\" title=\"struct alloc::boxed::Box\"><code>Box&lt;[T]&gt;</code></a>.</p>\n<p>Before doing the conversion, this method discards excess capacity like <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.shrink_to_fit\" title=\"method alloc::vec::Vec::shrink_to_fit\"><code>shrink_to_fit</code></a>.</p>\n<h5 id=\"examples-15\"><a class=\"doc-anchor\" href=\"#examples-15\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n\n<span class=\"kw\">let </span>slice = v.into_boxed_slice();</code></pre></div>\n<p>Any excess capacity is removed:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = Vec::with_capacity(<span class=\"number\">10</span>);\nvec.extend([<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n\n<span class=\"macro\">assert!</span>(vec.capacity() &gt;= <span class=\"number\">10</span>);\n<span class=\"kw\">let </span>slice = vec.into_boxed_slice();\n<span class=\"macro\">assert_eq!</span>(slice.into_vec().capacity(), <span class=\"number\">3</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.truncate\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1549\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.truncate\" class=\"fn\">truncate</a>(&amp;mut self, len: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><div class=\"docblock\"><p>Shortens the vector, keeping the first <code>len</code> elements and dropping\nthe rest.</p>\n<p>If <code>len</code> is greater or equal to the vector’s current length, this has\nno effect.</p>\n<p>The <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.drain\" title=\"method alloc::vec::Vec::drain\"><code>drain</code></a> method can emulate <code>truncate</code>, but causes the excess\nelements to be returned instead of dropped.</p>\n<p>Note that this method has no effect on the allocated capacity\nof the vector.</p>\n<h5 id=\"examples-16\"><a class=\"doc-anchor\" href=\"#examples-16\">§</a>Examples</h5>\n<p>Truncating a five element vector to two elements:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>, <span class=\"number\">5</span>];\nvec.truncate(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>]);</code></pre></div>\n<p>No truncation occurs when <code>len</code> is greater than the vector’s current\nlength:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\nvec.truncate(<span class=\"number\">8</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n<p>Truncating when <code>len == 0</code> is equivalent to calling the <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.clear\" title=\"method alloc::vec::Vec::clear\"><code>clear</code></a>\nmethod.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\nvec.truncate(<span class=\"number\">0</span>);\n<span class=\"macro\">assert_eq!</span>(vec, []);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_slice\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.7.0, const since 1.87.0\">1.7.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1586\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.as_slice\" class=\"fn\">as_slice</a>(&amp;self) -&gt; &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class=\"docblock\"><p>Extracts a slice containing the entire vector.</p>\n<p>Equivalent to <code>&amp;s[..]</code>.</p>\n<h5 id=\"examples-17\"><a class=\"doc-anchor\" href=\"#examples-17\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::io::{<span class=\"self\">self</span>, Write};\n<span class=\"kw\">let </span>buffer = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">5</span>, <span class=\"number\">8</span>];\nio::sink().write(buffer.as_slice()).unwrap();</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_mut_slice\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.7.0, const since 1.87.0\">1.7.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1618\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.as_mut_slice\" class=\"fn\">as_mut_slice</a>(&amp;mut self) -&gt; &amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class=\"docblock\"><p>Extracts a mutable slice of the entire vector.</p>\n<p>Equivalent to <code>&amp;mut s[..]</code>.</p>\n<h5 id=\"examples-18\"><a class=\"doc-anchor\" href=\"#examples-18\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::io::{<span class=\"self\">self</span>, Read};\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>buffer = <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>; <span class=\"number\">3</span>];\nio::repeat(<span class=\"number\">0b101</span>).read_exact(buffer.as_mut_slice()).unwrap();</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_ptr\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.37.0, const since 1.87.0\">1.37.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1693\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.as_ptr\" class=\"fn\">as_ptr</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html\">*const T</a></h4></section></summary><div class=\"docblock\"><p>Returns a raw pointer to the vector’s buffer, or a dangling raw pointer\nvalid for zero sized reads if the vector didn’t allocate.</p>\n<p>The caller must ensure that the vector outlives the pointer this\nfunction returns, or else it will end up dangling.\nModifying the vector may cause its buffer to be reallocated,\nwhich would also make any pointers to it invalid.</p>\n<p>The caller must also ensure that the memory the pointer (non-transitively) points to\nis never written to (except inside an <code>UnsafeCell</code>) using this pointer or any pointer\nderived from it. If you need to mutate the contents of the slice, use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_mut_ptr\" title=\"method alloc::vec::Vec::as_mut_ptr\"><code>as_mut_ptr</code></a>.</p>\n<p>This method guarantees that for the purpose of the aliasing model, this method\ndoes not materialize a reference to the underlying slice, and thus the returned pointer\nwill remain valid when mixed with other calls to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_ptr\" title=\"method alloc::vec::Vec::as_ptr\"><code>as_ptr</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_mut_ptr\" title=\"method alloc::vec::Vec::as_mut_ptr\"><code>as_mut_ptr</code></a>,\nand <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_non_null\" title=\"method alloc::vec::Vec::as_non_null\"><code>as_non_null</code></a>.\nNote that calling other methods that materialize mutable references to the slice,\nor mutable references to specific elements you are planning on accessing through this pointer,\nas well as writing to those elements, may still invalidate this pointer.\nSee the second example below for how this guarantee can be used.</p>\n<h5 id=\"examples-19\"><a class=\"doc-anchor\" href=\"#examples-19\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">4</span>];\n<span class=\"kw\">let </span>x_ptr = x.as_ptr();\n\n<span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..x.len() {\n        <span class=\"macro\">assert_eq!</span>(<span class=\"kw-2\">*</span>x_ptr.add(i), <span class=\"number\">1 </span>&lt;&lt; i);\n    }\n}</code></pre></div>\n<p>Due to the aliasing guarantee, the following code is legal:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>];\n    <span class=\"kw\">let </span>ptr1 = v.as_ptr();\n    <span class=\"kw\">let _ </span>= ptr1.read();\n    <span class=\"kw\">let </span>ptr2 = v.as_mut_ptr().offset(<span class=\"number\">2</span>);\n    ptr2.write(<span class=\"number\">2</span>);\n    <span class=\"comment\">// Notably, the write to `ptr2` did *not* invalidate `ptr1`\n    // because it mutated a different element:\n    </span><span class=\"kw\">let _ </span>= ptr1.read();\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_mut_ptr\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.37.0, const since 1.87.0\">1.37.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1756\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.as_mut_ptr\" class=\"fn\">as_mut_ptr</a>(&amp;mut self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.pointer.html\">*mut T</a></h4></section></summary><div class=\"docblock\"><p>Returns a raw mutable pointer to the vector’s buffer, or a dangling\nraw pointer valid for zero sized reads if the vector didn’t allocate.</p>\n<p>The caller must ensure that the vector outlives the pointer this\nfunction returns, or else it will end up dangling.\nModifying the vector may cause its buffer to be reallocated,\nwhich would also make any pointers to it invalid.</p>\n<p>This method guarantees that for the purpose of the aliasing model, this method\ndoes not materialize a reference to the underlying slice, and thus the returned pointer\nwill remain valid when mixed with other calls to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_ptr\" title=\"method alloc::vec::Vec::as_ptr\"><code>as_ptr</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_mut_ptr\" title=\"method alloc::vec::Vec::as_mut_ptr\"><code>as_mut_ptr</code></a>,\nand <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_non_null\" title=\"method alloc::vec::Vec::as_non_null\"><code>as_non_null</code></a>.\nNote that calling other methods that materialize references to the slice,\nor references to specific elements you are planning on accessing through this pointer,\nmay still invalidate this pointer.\nSee the second example below for how this guarantee can be used.</p>\n<h5 id=\"examples-20\"><a class=\"doc-anchor\" href=\"#examples-20\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"comment\">// Allocate vector big enough for 4 elements.\n</span><span class=\"kw\">let </span>size = <span class=\"number\">4</span>;\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: Vec&lt;i32&gt; = Vec::with_capacity(size);\n<span class=\"kw\">let </span>x_ptr = x.as_mut_ptr();\n\n<span class=\"comment\">// Initialize elements via raw pointer writes, then set length.\n</span><span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..size {\n        <span class=\"kw-2\">*</span>x_ptr.add(i) = i <span class=\"kw\">as </span>i32;\n    }\n    x.set_len(size);\n}\n<span class=\"macro\">assert_eq!</span>(<span class=\"kw-2\">&amp;*</span>x, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n<p>Due to the aliasing guarantee, the following code is legal:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>];\n    <span class=\"kw\">let </span>ptr1 = v.as_mut_ptr();\n    ptr1.write(<span class=\"number\">1</span>);\n    <span class=\"kw\">let </span>ptr2 = v.as_mut_ptr();\n    ptr2.write(<span class=\"number\">2</span>);\n    <span class=\"comment\">// Notably, the write to `ptr2` did *not* invalidate `ptr1`:\n    </span>ptr1.write(<span class=\"number\">3</span>);\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_non_null\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1821\">Source</a><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.as_non_null\" class=\"fn\">as_non_null</a>(&amp;mut self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/core/ptr/non_null/struct.NonNull.html\" title=\"struct core::ptr::non_null::NonNull\">NonNull</a>&lt;T&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>box_vec_non_null</code>)</span></div></span></summary><div class=\"docblock\"><p>Returns a <code>NonNull</code> pointer to the vector’s buffer, or a dangling\n<code>NonNull</code> pointer valid for zero sized reads if the vector didn’t allocate.</p>\n<p>The caller must ensure that the vector outlives the pointer this\nfunction returns, or else it will end up dangling.\nModifying the vector may cause its buffer to be reallocated,\nwhich would also make any pointers to it invalid.</p>\n<p>This method guarantees that for the purpose of the aliasing model, this method\ndoes not materialize a reference to the underlying slice, and thus the returned pointer\nwill remain valid when mixed with other calls to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_ptr\" title=\"method alloc::vec::Vec::as_ptr\"><code>as_ptr</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_mut_ptr\" title=\"method alloc::vec::Vec::as_mut_ptr\"><code>as_mut_ptr</code></a>,\nand <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.as_non_null\" title=\"method alloc::vec::Vec::as_non_null\"><code>as_non_null</code></a>.\nNote that calling other methods that materialize references to the slice,\nor references to specific elements you are planning on accessing through this pointer,\nmay still invalidate this pointer.\nSee the second example below for how this guarantee can be used.</p>\n<h5 id=\"examples-21\"><a class=\"doc-anchor\" href=\"#examples-21\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(box_vec_non_null)]\n\n</span><span class=\"comment\">// Allocate vector big enough for 4 elements.\n</span><span class=\"kw\">let </span>size = <span class=\"number\">4</span>;\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: Vec&lt;i32&gt; = Vec::with_capacity(size);\n<span class=\"kw\">let </span>x_ptr = x.as_non_null();\n\n<span class=\"comment\">// Initialize elements via raw pointer writes, then set length.\n</span><span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">for </span>i <span class=\"kw\">in </span><span class=\"number\">0</span>..size {\n        x_ptr.add(i).write(i <span class=\"kw\">as </span>i32);\n    }\n    x.set_len(size);\n}\n<span class=\"macro\">assert_eq!</span>(<span class=\"kw-2\">&amp;*</span>x, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n<p>Due to the aliasing guarantee, the following code is legal:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(box_vec_non_null)]\n\n</span><span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>];\n    <span class=\"kw\">let </span>ptr1 = v.as_non_null();\n    ptr1.write(<span class=\"number\">1</span>);\n    <span class=\"kw\">let </span>ptr2 = v.as_non_null();\n    ptr2.write(<span class=\"number\">2</span>);\n    <span class=\"comment\">// Notably, the write to `ptr2` did *not* invalidate `ptr1`:\n    </span>ptr1.write(<span class=\"number\">3</span>);\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.allocator\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1828\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.allocator\" class=\"fn\">allocator</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;A</a></h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>allocator_api</code>)</span></div></span></summary><div class=\"docblock\"><p>Returns a reference to the underlying allocator.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.set_len\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1920\">Source</a></span><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.set_len\" class=\"fn\">set_len</a>(&amp;mut self, new_len: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>)</h4></section></summary><div class=\"docblock\"><p>Forces the length of the vector to <code>new_len</code>.</p>\n<p>This is a low-level operation that maintains none of the normal\ninvariants of the type. Normally changing the length of a vector\nis done using one of the safe operations instead, such as\n<a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.truncate\" title=\"method alloc::vec::Vec::truncate\"><code>truncate</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.resize\" title=\"method alloc::vec::Vec::resize\"><code>resize</code></a>, <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.Extend.html#tymethod.extend\" title=\"method core::iter::traits::collect::Extend::extend\"><code>extend</code></a>, or <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.clear\" title=\"method alloc::vec::Vec::clear\"><code>clear</code></a>.</p>\n<h5 id=\"safety-2\"><a class=\"doc-anchor\" href=\"#safety-2\">§</a>Safety</h5>\n<ul>\n<li><code>new_len</code> must be less than or equal to <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.capacity\" title=\"method alloc::vec::Vec::capacity\"><code>capacity()</code></a>.</li>\n<li>The elements at <code>old_len..new_len</code> must be initialized.</li>\n</ul>\n<h5 id=\"examples-22\"><a class=\"doc-anchor\" href=\"#examples-22\">§</a>Examples</h5>\n<p>See <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.spare_capacity_mut\" title=\"method alloc::vec::Vec::spare_capacity_mut\"><code>spare_capacity_mut()</code></a> for an example with safe\ninitialization of capacity elements and use of this method.</p>\n<p><code>set_len()</code> can be useful for situations in which the vector\nis serving as a buffer for other code, particularly over FFI:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">pub fn </span>get_dictionary(<span class=\"kw-2\">&amp;</span><span class=\"self\">self</span>) -&gt; <span class=\"prelude-ty\">Option</span>&lt;Vec&lt;u8&gt;&gt; {\n    <span class=\"comment\">// Per the FFI method's docs, \"32768 bytes is always enough\".\n    </span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>dict = Vec::with_capacity(<span class=\"number\">32_768</span>);\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>dict_length = <span class=\"number\">0</span>;\n    <span class=\"comment\">// SAFETY: When `deflateGetDictionary` returns `Z_OK`, it holds that:\n    // 1. `dict_length` elements were initialized.\n    // 2. `dict_length` &lt;= the capacity (32_768)\n    // which makes `set_len` safe to call.\n    </span><span class=\"kw\">unsafe </span>{\n        <span class=\"comment\">// Make the FFI call...\n        </span><span class=\"kw\">let </span>r = deflateGetDictionary(<span class=\"self\">self</span>.strm, dict.as_mut_ptr(), <span class=\"kw-2\">&amp;mut </span>dict_length);\n        <span class=\"kw\">if </span>r == Z_OK {\n            <span class=\"comment\">// ...and update the length to what was initialized.\n            </span>dict.set_len(dict_length);\n            <span class=\"prelude-val\">Some</span>(dict)\n        } <span class=\"kw\">else </span>{\n            <span class=\"prelude-val\">None\n        </span>}\n    }\n}</code></pre></div>\n<p>While the following example is sound, there is a memory leak since\nthe inner vectors were not freed prior to the <code>set_len</code> call:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>],\n                   <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">0</span>],\n                   <span class=\"macro\">vec!</span>[<span class=\"number\">0</span>, <span class=\"number\">0</span>, <span class=\"number\">1</span>]];\n<span class=\"comment\">// SAFETY:\n// 1. `old_len..0` is empty so no elements need to be initialized.\n// 2. `0 &lt;= capacity` always holds whatever `capacity` is.\n</span><span class=\"kw\">unsafe </span>{\n    vec.set_len(<span class=\"number\">0</span>);\n}</code></pre></div>\n<p>Normally, here, one would use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.clear\" title=\"method alloc::vec::Vec::clear\"><code>clear</code></a> instead to correctly drop\nthe contents and thus not leak memory.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.swap_remove\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#1952\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.swap_remove\" class=\"fn\">swap_remove</a>(&amp;mut self, index: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>) -&gt; T</h4></section></summary><div class=\"docblock\"><p>Removes an element from the vector and returns it.</p>\n<p>The removed element is replaced by the last element of the vector.</p>\n<p>This does not preserve ordering of the remaining elements, but is <em>O</em>(1).\nIf you need to preserve the element order, use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.remove\" title=\"method alloc::vec::Vec::remove\"><code>remove</code></a> instead.</p>\n<h5 id=\"panics-3\"><a class=\"doc-anchor\" href=\"#panics-3\">§</a>Panics</h5>\n<p>Panics if <code>index</code> is out of bounds.</p>\n<h5 id=\"examples-23\"><a class=\"doc-anchor\" href=\"#examples-23\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"string\">\"foo\"</span>, <span class=\"string\">\"bar\"</span>, <span class=\"string\">\"baz\"</span>, <span class=\"string\">\"qux\"</span>];\n\n<span class=\"macro\">assert_eq!</span>(v.swap_remove(<span class=\"number\">1</span>), <span class=\"string\">\"bar\"</span>);\n<span class=\"macro\">assert_eq!</span>(v, [<span class=\"string\">\"foo\"</span>, <span class=\"string\">\"qux\"</span>, <span class=\"string\">\"baz\"</span>]);\n\n<span class=\"macro\">assert_eq!</span>(v.swap_remove(<span class=\"number\">0</span>), <span class=\"string\">\"foo\"</span>);\n<span class=\"macro\">assert_eq!</span>(v, [<span class=\"string\">\"baz\"</span>, <span class=\"string\">\"qux\"</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.insert\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2002\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.insert\" class=\"fn\">insert</a>(&amp;mut self, index: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, element: T)</h4></section></summary><div class=\"docblock\"><p>Inserts an element at position <code>index</code> within the vector, shifting all\nelements after it to the right.</p>\n<h5 id=\"panics-4\"><a class=\"doc-anchor\" href=\"#panics-4\">§</a>Panics</h5>\n<p>Panics if <code>index &gt; len</code>.</p>\n<h5 id=\"examples-24\"><a class=\"doc-anchor\" href=\"#examples-24\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>];\nvec.insert(<span class=\"number\">1</span>, <span class=\"string\">'d'</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"string\">'a'</span>, <span class=\"string\">'d'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>]);\nvec.insert(<span class=\"number\">4</span>, <span class=\"string\">'e'</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"string\">'a'</span>, <span class=\"string\">'d'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>, <span class=\"string\">'e'</span>]);</code></pre></div>\n<h5 id=\"time-complexity\"><a class=\"doc-anchor\" href=\"#time-complexity\">§</a>Time complexity</h5>\n<p>Takes <em>O</em>(<a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.len\" title=\"method alloc::vec::Vec::len\"><code>Vec::len</code></a>) time. All items after the insertion index must be\nshifted to the right. In the worst case, all elements are shifted when\nthe insertion index is 0.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.remove\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2065\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.remove\" class=\"fn\">remove</a>(&amp;mut self, index: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>) -&gt; T</h4></section></summary><div class=\"docblock\"><p>Removes and returns the element at position <code>index</code> within the vector,\nshifting all elements after it to the left.</p>\n<p>Note: Because this shifts over the remaining elements, it has a\nworst-case performance of <em>O</em>(<em>n</em>). If you don’t need the order of elements\nto be preserved, use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.swap_remove\" title=\"method alloc::vec::Vec::swap_remove\"><code>swap_remove</code></a> instead. If you’d like to remove\nelements from the beginning of the <code>Vec</code>, consider using\n<a href=\"https://doc.rust-lang.org/nightly/alloc/collections/vec_deque/struct.VecDeque.html#method.pop_front\" title=\"method alloc::collections::vec_deque::VecDeque::pop_front\"><code>VecDeque::pop_front</code></a> instead.</p>\n<h5 id=\"panics-5\"><a class=\"doc-anchor\" href=\"#panics-5\">§</a>Panics</h5>\n<p>Panics if <code>index</code> is out of bounds.</p>\n<h5 id=\"examples-25\"><a class=\"doc-anchor\" href=\"#examples-25\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>];\n<span class=\"macro\">assert_eq!</span>(v.remove(<span class=\"number\">1</span>), <span class=\"string\">'b'</span>);\n<span class=\"macro\">assert_eq!</span>(v, [<span class=\"string\">'a'</span>, <span class=\"string\">'c'</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.retain\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2121-2123\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.retain\" class=\"fn\">retain</a>&lt;F&gt;(&amp;mut self, f: F)<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnMut.html\" title=\"trait core::ops::function::FnMut\">FnMut</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;T</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a>,</div></h4></section></summary><div class=\"docblock\"><p>Retains only the elements specified by the predicate.</p>\n<p>In other words, remove all elements <code>e</code> for which <code>f(&amp;e)</code> returns <code>false</code>.\nThis method operates in place, visiting each element exactly once in the\noriginal order, and preserves the order of the retained elements.</p>\n<h5 id=\"examples-26\"><a class=\"doc-anchor\" href=\"#examples-26\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>];\nvec.retain(|<span class=\"kw-2\">&amp;</span>x| x % <span class=\"number\">2 </span>== <span class=\"number\">0</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">2</span>, <span class=\"number\">4</span>]);</code></pre></div>\n<p>Because the elements are visited exactly once in the original order,\nexternal state may be used to decide which elements to keep.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>, <span class=\"number\">5</span>];\n<span class=\"kw\">let </span>keep = [<span class=\"bool-val\">false</span>, <span class=\"bool-val\">true</span>, <span class=\"bool-val\">true</span>, <span class=\"bool-val\">false</span>, <span class=\"bool-val\">true</span>];\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>iter = keep.iter();\nvec.retain(|<span class=\"kw\">_</span>| <span class=\"kw-2\">*</span>iter.next().unwrap());\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">5</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.retain_mut\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.61.0\">1.61.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2147-2149\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.retain_mut\" class=\"fn\">retain_mut</a>&lt;F&gt;(&amp;mut self, f: F)<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnMut.html\" title=\"trait core::ops::function::FnMut\">FnMut</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut T</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a>,</div></h4></section></summary><div class=\"docblock\"><p>Retains only the elements specified by the predicate, passing a mutable reference to it.</p>\n<p>In other words, remove all elements <code>e</code> such that <code>f(&amp;mut e)</code> returns <code>false</code>.\nThis method operates in place, visiting each element exactly once in the\noriginal order, and preserves the order of the retained elements.</p>\n<h5 id=\"examples-27\"><a class=\"doc-anchor\" href=\"#examples-27\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>];\nvec.retain_mut(|x| <span class=\"kw\">if </span><span class=\"kw-2\">*</span>x &lt;= <span class=\"number\">3 </span>{\n    <span class=\"kw-2\">*</span>x += <span class=\"number\">1</span>;\n    <span class=\"bool-val\">true\n</span>} <span class=\"kw\">else </span>{\n    <span class=\"bool-val\">false\n</span>});\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.dedup_by_key\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.16.0\">1.16.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2262-2265\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.dedup_by_key\" class=\"fn\">dedup_by_key</a>&lt;F, K&gt;(&amp;mut self, key: F)<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnMut.html\" title=\"trait core::ops::function::FnMut\">FnMut</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut T</a>) -&gt; K,\n    K: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>,</div></h4></section></summary><div class=\"docblock\"><p>Removes all but the first of consecutive elements in the vector that resolve to the same\nkey.</p>\n<p>If the vector is sorted, this removes all duplicates.</p>\n<h5 id=\"examples-28\"><a class=\"doc-anchor\" href=\"#examples-28\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">10</span>, <span class=\"number\">20</span>, <span class=\"number\">21</span>, <span class=\"number\">30</span>, <span class=\"number\">20</span>];\n\nvec.dedup_by_key(|i| <span class=\"kw-2\">*</span>i / <span class=\"number\">10</span>);\n\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">10</span>, <span class=\"number\">20</span>, <span class=\"number\">30</span>, <span class=\"number\">20</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.dedup_by\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.16.0\">1.16.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2289-2291\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.dedup_by\" class=\"fn\">dedup_by</a>&lt;F&gt;(&amp;mut self, same_bucket: F)<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnMut.html\" title=\"trait core::ops::function::FnMut\">FnMut</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut T</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut T</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a>,</div></h4></section></summary><div class=\"docblock\"><p>Removes all but the first of consecutive elements in the vector satisfying a given equality\nrelation.</p>\n<p>The <code>same_bucket</code> function is passed references to two elements from the vector and\nmust determine if the elements compare equal. The elements are passed in opposite order\nfrom their order in the slice, so if <code>same_bucket(a, b)</code> returns <code>true</code>, <code>a</code> is removed.</p>\n<p>If the vector is sorted, this removes all duplicates.</p>\n<h5 id=\"examples-29\"><a class=\"doc-anchor\" href=\"#examples-29\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"string\">\"foo\"</span>, <span class=\"string\">\"bar\"</span>, <span class=\"string\">\"Bar\"</span>, <span class=\"string\">\"baz\"</span>, <span class=\"string\">\"bar\"</span>];\n\nvec.dedup_by(|a, b| a.eq_ignore_ascii_case(b));\n\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"string\">\"foo\"</span>, <span class=\"string\">\"bar\"</span>, <span class=\"string\">\"baz\"</span>, <span class=\"string\">\"bar\"</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.push\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2442\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.push\" class=\"fn\">push</a>(&amp;mut self, value: T)</h4></section></summary><div class=\"docblock\"><p>Appends an element to the back of a collection.</p>\n<h5 id=\"panics-6\"><a class=\"doc-anchor\" href=\"#panics-6\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-30\"><a class=\"doc-anchor\" href=\"#examples-30\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>];\nvec.push(<span class=\"number\">3</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n<h5 id=\"time-complexity-1\"><a class=\"doc-anchor\" href=\"#time-complexity-1\">§</a>Time complexity</h5>\n<p>Takes amortized <em>O</em>(1) time. If the vector’s length would exceed its\ncapacity after the push, <em>O</em>(<em>capacity</em>) time is taken to copy the\nvector’s elements to a larger allocation. This expensive operation is\noffset by the <em>capacity</em> <em>O</em>(1) insertions it allows.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.push_within_capacity\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2494\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.push_within_capacity\" class=\"fn\">push_within_capacity</a>(&amp;mut self, value: T) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.unit.html\">()</a>, T&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>vec_push_within_capacity</code>)</span></div></span></summary><div class=\"docblock\"><p>Appends an element if there is sufficient spare capacity, otherwise an error is returned\nwith the element.</p>\n<p>Unlike <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.push\" title=\"method alloc::vec::Vec::push\"><code>push</code></a> this method will not reallocate when there’s insufficient capacity.\nThe caller should use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.reserve\" title=\"method alloc::vec::Vec::reserve\"><code>reserve</code></a> or <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.try_reserve\" title=\"method alloc::vec::Vec::try_reserve\"><code>try_reserve</code></a> to ensure that there is enough capacity.</p>\n<h5 id=\"examples-31\"><a class=\"doc-anchor\" href=\"#examples-31\">§</a>Examples</h5>\n<p>A manual, panic-free alternative to <a href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.FromIterator.html\" title=\"trait core::iter::traits::collect::FromIterator\"><code>FromIterator</code></a>:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(vec_push_within_capacity)]\n\n</span><span class=\"kw\">use </span>std::collections::TryReserveError;\n<span class=\"kw\">fn </span>from_iter_fallible&lt;T&gt;(iter: <span class=\"kw\">impl </span>Iterator&lt;Item=T&gt;) -&gt; <span class=\"prelude-ty\">Result</span>&lt;Vec&lt;T&gt;, TryReserveError&gt; {\n    <span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = Vec::new();\n    <span class=\"kw\">for </span>value <span class=\"kw\">in </span>iter {\n        <span class=\"kw\">if let </span><span class=\"prelude-val\">Err</span>(value) = vec.push_within_capacity(value) {\n            vec.try_reserve(<span class=\"number\">1</span>)<span class=\"question-mark\">?</span>;\n            <span class=\"comment\">// this cannot fail, the previous line either returned or added at least 1 free slot\n            </span><span class=\"kw\">let _ </span>= vec.push_within_capacity(value);\n        }\n    }\n    <span class=\"prelude-val\">Ok</span>(vec)\n}\n<span class=\"macro\">assert_eq!</span>(from_iter_fallible(<span class=\"number\">0</span>..<span class=\"number\">100</span>), <span class=\"prelude-val\">Ok</span>(Vec::from_iter(<span class=\"number\">0</span>..<span class=\"number\">100</span>)));</code></pre></div>\n<h5 id=\"time-complexity-2\"><a class=\"doc-anchor\" href=\"#time-complexity-2\">§</a>Time complexity</h5>\n<p>Takes <em>O</em>(1) time.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.pop\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2528\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.pop\" class=\"fn\">pop</a>(&amp;mut self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Removes the last element from a vector and returns it, or <a href=\"https://doc.rust-lang.org/nightly/core/option/enum.Option.html#variant.None\" title=\"variant core::option::Option::None\"><code>None</code></a> if it\nis empty.</p>\n<p>If you’d like to pop the first element, consider using\n<a href=\"https://doc.rust-lang.org/nightly/alloc/collections/vec_deque/struct.VecDeque.html#method.pop_front\" title=\"method alloc::collections::vec_deque::VecDeque::pop_front\"><code>VecDeque::pop_front</code></a> instead.</p>\n<h5 id=\"examples-32\"><a class=\"doc-anchor\" href=\"#examples-32\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n<span class=\"macro\">assert_eq!</span>(vec.pop(), <span class=\"prelude-val\">Some</span>(<span class=\"number\">3</span>));\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>]);</code></pre></div>\n<h5 id=\"time-complexity-3\"><a class=\"doc-anchor\" href=\"#time-complexity-3\">§</a>Time complexity</h5>\n<p>Takes <em>O</em>(1) time.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.pop_if\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.86.0\">1.86.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2555\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.pop_if\" class=\"fn\">pop_if</a>(&amp;mut self, predicate: impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.reference.html\">&amp;mut T</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a>) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/nightly/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Removes and returns the last element from a vector if the predicate\nreturns <code>true</code>, or <a href=\"https://doc.rust-lang.org/nightly/core/option/enum.Option.html#variant.None\" title=\"variant core::option::Option::None\"><code>None</code></a> if the predicate returns false or the vector\nis empty (the predicate will not be called in that case).</p>\n<h5 id=\"examples-33\"><a class=\"doc-anchor\" href=\"#examples-33\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>];\n<span class=\"kw\">let </span>pred = |x: <span class=\"kw-2\">&amp;mut </span>i32| <span class=\"kw-2\">*</span>x % <span class=\"number\">2 </span>== <span class=\"number\">0</span>;\n\n<span class=\"macro\">assert_eq!</span>(vec.pop_if(pred), <span class=\"prelude-val\">Some</span>(<span class=\"number\">4</span>));\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n<span class=\"macro\">assert_eq!</span>(vec.pop_if(pred), <span class=\"prelude-val\">None</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.append\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.4.0\">1.4.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2579\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.append\" class=\"fn\">append</a>(&amp;mut self, other: &amp;mut <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;)</h4></section></summary><div class=\"docblock\"><p>Moves all the elements of <code>other</code> into <code>self</code>, leaving <code>other</code> empty.</p>\n<h5 id=\"panics-7\"><a class=\"doc-anchor\" href=\"#panics-7\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-34\"><a class=\"doc-anchor\" href=\"#examples-34\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec2 = <span class=\"macro\">vec!</span>[<span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>];\nvec.append(<span class=\"kw-2\">&amp;mut </span>vec2);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">4</span>, <span class=\"number\">5</span>, <span class=\"number\">6</span>]);\n<span class=\"macro\">assert_eq!</span>(vec2, []);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.drain\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.6.0\">1.6.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2631-2633\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.drain\" class=\"fn\">drain</a>&lt;R&gt;(&amp;mut self, range: R) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/drain/struct.Drain.html\" title=\"struct alloc::vec::drain::Drain\">Drain</a>&lt;'_, T, A&gt;<div class=\"where\">where\n    R: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/range/trait.RangeBounds.html\" title=\"trait core::ops::range::RangeBounds\">RangeBounds</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Removes the subslice indicated by the given range from the vector,\nreturning a double-ended iterator over the removed subslice.</p>\n<p>If the iterator is dropped before being fully consumed,\nit drops the remaining removed elements.</p>\n<p>The returned iterator keeps a mutable borrow on the vector to optimize\nits implementation.</p>\n<h5 id=\"panics-8\"><a class=\"doc-anchor\" href=\"#panics-8\">§</a>Panics</h5>\n<p>Panics if the starting point is greater than the end point or if\nthe end point is greater than the length of the vector.</p>\n<h5 id=\"leaking\"><a class=\"doc-anchor\" href=\"#leaking\">§</a>Leaking</h5>\n<p>If the returned iterator goes out of scope without being dropped (due to\n<a href=\"https://doc.rust-lang.org/nightly/core/mem/fn.forget.html\" title=\"fn core::mem::forget\"><code>mem::forget</code></a>, for example), the vector may have lost and leaked\nelements arbitrarily, including elements outside the range.</p>\n<h5 id=\"examples-35\"><a class=\"doc-anchor\" href=\"#examples-35\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n<span class=\"kw\">let </span>u: Vec&lt;<span class=\"kw\">_</span>&gt; = v.drain(<span class=\"number\">1</span>..).collect();\n<span class=\"macro\">assert_eq!</span>(v, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">1</span>]);\n<span class=\"macro\">assert_eq!</span>(u, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">2</span>, <span class=\"number\">3</span>]);\n\n<span class=\"comment\">// A full range clears the vector, like `clear()` does\n</span>v.drain(..);\n<span class=\"macro\">assert_eq!</span>(v, <span class=\"kw-2\">&amp;</span>[]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.clear\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2677\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.clear\" class=\"fn\">clear</a>(&amp;mut self)</h4></section></summary><div class=\"docblock\"><p>Clears the vector, removing all values.</p>\n<p>Note that this method has no effect on the allocated capacity\nof the vector.</p>\n<h5 id=\"examples-36\"><a class=\"doc-anchor\" href=\"#examples-36\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n\nv.clear();\n\n<span class=\"macro\">assert!</span>(v.is_empty());</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.len\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.87.0\">1.0.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2705\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.len\" class=\"fn\">len</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a></h4></section></summary><div class=\"docblock\"><p>Returns the number of elements in the vector, also referred to\nas its ‘length’.</p>\n<h5 id=\"examples-37\"><a class=\"doc-anchor\" href=\"#examples-37\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>a = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n<span class=\"macro\">assert_eq!</span>(a.len(), <span class=\"number\">3</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.is_empty\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.87.0\">1.0.0 (const: 1.87.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2730\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.is_empty\" class=\"fn\">is_empty</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.bool.html\">bool</a></h4></section></summary><div class=\"docblock\"><p>Returns <code>true</code> if the vector contains no elements.</p>\n<h5 id=\"examples-38\"><a class=\"doc-anchor\" href=\"#examples-38\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = Vec::new();\n<span class=\"macro\">assert!</span>(v.is_empty());\n\nv.push(<span class=\"number\">1</span>);\n<span class=\"macro\">assert!</span>(!v.is_empty());</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.split_off\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.4.0\">1.4.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2763-2765\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.split_off\" class=\"fn\">split_off</a>(&amp;mut self, at: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h4></section></summary><div class=\"docblock\"><p>Splits the collection into two at the given index.</p>\n<p>Returns a newly allocated vector containing the elements in the range\n<code>[at, len)</code>. After the call, the original vector will be left containing\nthe elements <code>[0, at)</code> with its previous capacity unchanged.</p>\n<ul>\n<li>If you want to take ownership of the entire contents and capacity of\nthe vector, see <a href=\"https://doc.rust-lang.org/nightly/core/mem/fn.take.html\" title=\"fn core::mem::take\"><code>mem::take</code></a> or <a href=\"https://doc.rust-lang.org/nightly/core/mem/fn.replace.html\" title=\"fn core::mem::replace\"><code>mem::replace</code></a>.</li>\n<li>If you don’t need the returned vector at all, see <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.truncate\" title=\"method alloc::vec::Vec::truncate\"><code>Vec::truncate</code></a>.</li>\n<li>If you want to take ownership of an arbitrary subslice, or you don’t\nnecessarily want to store the removed items in a vector, see <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.drain\" title=\"method alloc::vec::Vec::drain\"><code>Vec::drain</code></a>.</li>\n</ul>\n<h5 id=\"panics-9\"><a class=\"doc-anchor\" href=\"#panics-9\">§</a>Panics</h5>\n<p>Panics if <code>at &gt; len</code>.</p>\n<h5 id=\"examples-39\"><a class=\"doc-anchor\" href=\"#examples-39\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"string\">'a'</span>, <span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>];\n<span class=\"kw\">let </span>vec2 = vec.split_off(<span class=\"number\">1</span>);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"string\">'a'</span>]);\n<span class=\"macro\">assert_eq!</span>(vec2, [<span class=\"string\">'b'</span>, <span class=\"string\">'c'</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.resize_with\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.33.0\">1.33.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2825-2827\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.resize_with\" class=\"fn\">resize_with</a>&lt;F&gt;(&amp;mut self, new_len: <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.usize.html\">usize</a>, f: F)<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/function/trait.FnMut.html\" title=\"trait core::ops::function::FnMut\">FnMut</a>() -&gt; T,</div></h4></section></summary><div class=\"docblock\"><p>Resizes the <code>Vec</code> in-place so that <code>len</code> is equal to <code>new_len</code>.</p>\n<p>If <code>new_len</code> is greater than <code>len</code>, the <code>Vec</code> is extended by the\ndifference, with each additional slot filled with the result of\ncalling the closure <code>f</code>. The return values from <code>f</code> will end up\nin the <code>Vec</code> in the order they have been generated.</p>\n<p>If <code>new_len</code> is less than <code>len</code>, the <code>Vec</code> is simply truncated.</p>\n<p>This method uses a closure to create new values on every push. If\nyou’d rather <a href=\"https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\"><code>Clone</code></a> a given value, use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.resize\" title=\"method alloc::vec::Vec::resize\"><code>Vec::resize</code></a>. If you\nwant to use the <a href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\"><code>Default</code></a> trait to generate values, you can\npass <a href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html#tymethod.default\" title=\"associated function core::default::Default::default\"><code>Default::default</code></a> as the second argument.</p>\n<h5 id=\"panics-10\"><a class=\"doc-anchor\" href=\"#panics-10\">§</a>Panics</h5>\n<p>Panics if the new capacity exceeds <code>isize::MAX</code> <em>bytes</em>.</p>\n<h5 id=\"examples-40\"><a class=\"doc-anchor\" href=\"#examples-40\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\nvec.resize_with(<span class=\"number\">5</span>, Default::default);\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>, <span class=\"number\">0</span>, <span class=\"number\">0</span>]);\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>vec = <span class=\"macro\">vec!</span>[];\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>p = <span class=\"number\">1</span>;\nvec.resize_with(<span class=\"number\">4</span>, || { p <span class=\"kw-2\">*</span>= <span class=\"number\">2</span>; p });\n<span class=\"macro\">assert_eq!</span>(vec, [<span class=\"number\">2</span>, <span class=\"number\">4</span>, <span class=\"number\">8</span>, <span class=\"number\">16</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.leak\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.47.0\">1.47.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2867-2869\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.leak\" class=\"fn\">leak</a>&lt;'a&gt;(self) -&gt; &amp;'a mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a><div class=\"where\">where\n    A: 'a,</div></h4></section></summary><div class=\"docblock\"><p>Consumes and leaks the <code>Vec</code>, returning a mutable reference to the contents,\n<code>&amp;'a mut [T]</code>.</p>\n<p>Note that the type <code>T</code> must outlive the chosen lifetime <code>'a</code>. If the type\nhas only static references, or none at all, then this may be chosen to be\n<code>'static</code>.</p>\n<p>As of Rust 1.57, this method does not reallocate or shrink the <code>Vec</code>,\nso the leaked allocation may include unused capacity that is not part\nof the returned slice.</p>\n<p>This function is mainly useful for data that lives for the remainder of\nthe program’s life. Dropping the returned reference will cause a memory\nleak.</p>\n<h5 id=\"examples-41\"><a class=\"doc-anchor\" href=\"#examples-41\">§</a>Examples</h5>\n<p>Simple usage:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>];\n<span class=\"kw\">let </span>static_ref: <span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span><span class=\"kw-2\">mut </span>[usize] = x.leak();\nstatic_ref[<span class=\"number\">0</span>] += <span class=\"number\">1</span>;\n<span class=\"macro\">assert_eq!</span>(static_ref, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">2</span>, <span class=\"number\">2</span>, <span class=\"number\">3</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.spare_capacity_mut\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.60.0\">1.60.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2905\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.spare_capacity_mut\" class=\"fn\">spare_capacity_mut</a>(&amp;mut self) -&gt; &amp;mut [<a class=\"union\" href=\"https://doc.rust-lang.org/nightly/core/mem/maybe_uninit/union.MaybeUninit.html\" title=\"union core::mem::maybe_uninit::MaybeUninit\">MaybeUninit</a>&lt;T&gt;]</h4></section></summary><div class=\"docblock\"><p>Returns the remaining spare capacity of the vector as a slice of\n<code>MaybeUninit&lt;T&gt;</code>.</p>\n<p>The returned slice can be used to fill the vector with data (e.g. by\nreading from a file) before marking the data as initialized using the\n<a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.set_len\" title=\"method alloc::vec::Vec::set_len\"><code>set_len</code></a> method.</p>\n<h5 id=\"examples-42\"><a class=\"doc-anchor\" href=\"#examples-42\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"comment\">// Allocate vector big enough for 10 elements.\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = Vec::with_capacity(<span class=\"number\">10</span>);\n\n<span class=\"comment\">// Fill in the first 3 elements.\n</span><span class=\"kw\">let </span>uninit = v.spare_capacity_mut();\nuninit[<span class=\"number\">0</span>].write(<span class=\"number\">0</span>);\nuninit[<span class=\"number\">1</span>].write(<span class=\"number\">1</span>);\nuninit[<span class=\"number\">2</span>].write(<span class=\"number\">2</span>);\n\n<span class=\"comment\">// Mark the first 3 elements of the vector as being initialized.\n</span><span class=\"kw\">unsafe </span>{\n    v.set_len(<span class=\"number\">3</span>);\n}\n\n<span class=\"macro\">assert_eq!</span>(<span class=\"kw-2\">&amp;</span>v, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">0</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>]);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.split_at_spare_mut\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#2970\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#tymethod.split_at_spare_mut\" class=\"fn\">split_at_spare_mut</a>(&amp;mut self) -&gt; (&amp;mut <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a>, &amp;mut [<a class=\"union\" href=\"https://doc.rust-lang.org/nightly/core/mem/maybe_uninit/union.MaybeUninit.html\" title=\"union core::mem::maybe_uninit::MaybeUninit\">MaybeUninit</a>&lt;T&gt;])</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>vec_split_at_spare</code>)</span></div></span></summary><div class=\"docblock\"><p>Returns vector content as a slice of <code>T</code>, along with the remaining spare\ncapacity of the vector as a slice of <code>MaybeUninit&lt;T&gt;</code>.</p>\n<p>The returned spare capacity slice can be used to fill the vector with data\n(e.g. by reading from a file) before marking the data as initialized using\nthe <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.set_len\" title=\"method alloc::vec::Vec::set_len\"><code>set_len</code></a> method.</p>\n<p>Note that this is a low-level API, which should be used with care for\noptimization purposes. If you need to append data to a <code>Vec</code>\nyou can use <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.push\" title=\"method alloc::vec::Vec::push\"><code>push</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.extend\" title=\"method alloc::vec::Vec::extend\"><code>extend</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.extend_from_slice\" title=\"method alloc::vec::Vec::extend_from_slice\"><code>extend_from_slice</code></a>,\n<a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.extend_from_within\" title=\"method alloc::vec::Vec::extend_from_within\"><code>extend_from_within</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.insert\" title=\"method alloc::vec::Vec::insert\"><code>insert</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.append\" title=\"method alloc::vec::Vec::append\"><code>append</code></a>, <a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.resize\" title=\"method alloc::vec::Vec::resize\"><code>resize</code></a> or\n<a href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html#method.resize_with\" title=\"method alloc::vec::Vec::resize_with\"><code>resize_with</code></a>, depending on your exact needs.</p>\n<h5 id=\"examples-43\"><a class=\"doc-anchor\" href=\"#examples-43\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(vec_split_at_spare)]\n\n</span><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>];\n\n<span class=\"comment\">// Reserve additional space big enough for 10 elements.\n</span>v.reserve(<span class=\"number\">10</span>);\n\n<span class=\"kw\">let </span>(init, uninit) = v.split_at_spare_mut();\n<span class=\"kw\">let </span>sum = init.iter().copied().sum::&lt;u32&gt;();\n\n<span class=\"comment\">// Fill in the next 4 elements.\n</span>uninit[<span class=\"number\">0</span>].write(sum);\nuninit[<span class=\"number\">1</span>].write(sum * <span class=\"number\">2</span>);\nuninit[<span class=\"number\">2</span>].write(sum * <span class=\"number\">3</span>);\nuninit[<span class=\"number\">3</span>].write(sum * <span class=\"number\">4</span>);\n\n<span class=\"comment\">// Mark the 4 elements of the vector as being initialized.\n</span><span class=\"kw\">unsafe </span>{\n    <span class=\"kw\">let </span>len = v.len();\n    v.set_len(len + <span class=\"number\">4</span>);\n}\n\n<span class=\"macro\">assert_eq!</span>(<span class=\"kw-2\">&amp;</span>v, <span class=\"kw-2\">&amp;</span>[<span class=\"number\">1</span>, <span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">4</span>, <span class=\"number\">8</span>, <span class=\"number\">12</span>, <span class=\"number\">16</span>]);</code></pre></div>\n</div></details></div></details>",0,"solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Zeroize-for-Vec%3CZ%3E\" class=\"impl\"><a href=\"#impl-Zeroize-for-Vec%3CZ%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;Z&gt; Zeroize for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;Z&gt;<div class=\"where\">where\n    Z: Zeroize,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.zeroize\" class=\"method trait-impl\"><a href=\"#method.zeroize\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">zeroize</a>(&amp;mut self)</h4></section></summary><div class=\"docblock\"><p>“Best effort” zeroization for <code>Vec</code>.</p>\n<p>Ensures the entire capacity of the <code>Vec</code> is zeroed. Cannot ensure that\nprevious reallocations did not leave values on the heap.</p>\n</div></details></div></details>","Zeroize","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-__Deref-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3294\">Source</a></span><a href=\"#impl-__Deref-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\">Deref</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle\" open><summary><section id=\"associatedtype.Target\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3295\">Source</a><a href=\"#associatedtype.Target\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.Deref.html#associatedtype.Target\" class=\"associatedtype\">Target</a> = <a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>The resulting type after dereferencing.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.deref\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3298\">Source</a><a href=\"#method.deref\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.Deref.html#tymethod.deref\" class=\"fn\">deref</a>(&amp;self) -&gt; &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.slice.html\">[T]</a></h4></section></summary><div class='docblock'>Dereferences the value.</div></details></div></details>","Deref","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<section id=\"impl-DerefPure-for-Vec%3CT,+A%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3312\">Source</a><a href=\"#impl-DerefPure-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.DerefPure.html\" title=\"trait core::ops::deref::DerefPure\">DerefPure</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section>","DerefPure","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<section id=\"impl-Eq-for-Vec%3CT,+A%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/nightly/src/alloc/vec/mod.rs.html#3786\">Source</a></span><a href=\"#impl-Eq-for-Vec%3CT,+A%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Eq.html\" title=\"trait core::cmp::Eq\">Eq</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;T, A&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/cmp/trait.Eq.html\" title=\"trait core::cmp::Eq\">Eq</a>,\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></h3></section>","Eq","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"],["<section id=\"impl-ZeroizeOnDrop-for-Vec%3CZ%3E\" class=\"impl\"><a href=\"#impl-ZeroizeOnDrop-for-Vec%3CZ%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;Z&gt; ZeroizeOnDrop for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;Z&gt;<div class=\"where\">where\n    Z: ZeroizeOnDrop,</div></h3></section>","ZeroizeOnDrop","solana_message::inner_instruction::InnerInstructions","solana_message::inner_instruction::InnerInstructionsList"]]]]);
    if (window.register_type_impls) {
        window.register_type_impls(type_impls);
    } else {
        window.pending_type_impls = type_impls;
    }
})()
//{"start":55,"fragment_lengths":[301799]}