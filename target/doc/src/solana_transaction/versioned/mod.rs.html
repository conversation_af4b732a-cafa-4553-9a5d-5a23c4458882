<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-transaction-2.2.2/src/versioned/mod.rs`."><title>mod.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_transaction/versioned/</div>mod.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="doccomment">//! Defines a transaction which supports multiple versions of messages.
<a href=#2 id=2 data-nosnippet>2</a>
<a href=#3 id=3 data-nosnippet>3</a></span><span class="kw">use </span>{
<a href=#4 id=4 data-nosnippet>4</a>    <span class="kw">crate</span>::Transaction, solana_message::VersionedMessage, solana_sanitize::SanitizeError,
<a href=#5 id=5 data-nosnippet>5</a>    solana_signature::Signature, std::cmp::Ordering,
<a href=#6 id=6 data-nosnippet>6</a>};
<a href=#7 id=7 data-nosnippet>7</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">use </span>{
<a href=#9 id=9 data-nosnippet>9</a>    serde_derive::{Deserialize, Serialize},
<a href=#10 id=10 data-nosnippet>10</a>    solana_short_vec <span class="kw">as </span>short_vec,
<a href=#11 id=11 data-nosnippet>11</a>};
<a href=#12 id=12 data-nosnippet>12</a><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#13 id=13 data-nosnippet>13</a></span><span class="kw">use </span>{
<a href=#14 id=14 data-nosnippet>14</a>    solana_bincode::limited_deserialize,
<a href=#15 id=15 data-nosnippet>15</a>    solana_sdk_ids::system_program,
<a href=#16 id=16 data-nosnippet>16</a>    solana_signer::{signers::Signers, SignerError},
<a href=#17 id=17 data-nosnippet>17</a>    solana_system_interface::instruction::SystemInstruction,
<a href=#18 id=18 data-nosnippet>18</a>};
<a href=#19 id=19 data-nosnippet>19</a>
<a href=#20 id=20 data-nosnippet>20</a><span class="kw">pub mod </span>sanitized;
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="doccomment">/// Type that serializes to the string "legacy"
<a href=#23 id=23 data-nosnippet>23</a></span><span class="attr">#[cfg_attr(
<a href=#24 id=24 data-nosnippet>24</a>    feature = <span class="string">"serde"</span>,
<a href=#25 id=25 data-nosnippet>25</a>    derive(Deserialize, Serialize),
<a href=#26 id=26 data-nosnippet>26</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#27 id=27 data-nosnippet>27</a>)]
<a href=#28 id=28 data-nosnippet>28</a>#[derive(Clone, Debug, PartialEq, Eq)]
<a href=#29 id=29 data-nosnippet>29</a></span><span class="kw">pub enum </span>Legacy {
<a href=#30 id=30 data-nosnippet>30</a>    Legacy,
<a href=#31 id=31 data-nosnippet>31</a>}
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a><span class="attr">#[cfg_attr(
<a href=#34 id=34 data-nosnippet>34</a>    feature = <span class="string">"serde"</span>,
<a href=#35 id=35 data-nosnippet>35</a>    derive(Deserialize, Serialize),
<a href=#36 id=36 data-nosnippet>36</a>    serde(rename_all = <span class="string">"camelCase"</span>, untagged)
<a href=#37 id=37 data-nosnippet>37</a>)]
<a href=#38 id=38 data-nosnippet>38</a>#[derive(Clone, Debug, PartialEq, Eq)]
<a href=#39 id=39 data-nosnippet>39</a></span><span class="kw">pub enum </span>TransactionVersion {
<a href=#40 id=40 data-nosnippet>40</a>    Legacy(Legacy),
<a href=#41 id=41 data-nosnippet>41</a>    Number(u8),
<a href=#42 id=42 data-nosnippet>42</a>}
<a href=#43 id=43 data-nosnippet>43</a>
<a href=#44 id=44 data-nosnippet>44</a><span class="kw">impl </span>TransactionVersion {
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">pub const </span>LEGACY: <span class="self">Self </span>= <span class="self">Self</span>::Legacy(Legacy::Legacy);
<a href=#46 id=46 data-nosnippet>46</a>}
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a><span class="comment">// NOTE: Serialization-related changes must be paired with the direct read at sigverify.
<a href=#49 id=49 data-nosnippet>49</a></span><span class="doccomment">/// An atomic transaction
<a href=#50 id=50 data-nosnippet>50</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"frozen-abi"</span>, derive(solana_frozen_abi_macro::AbiExample))]
<a href=#51 id=51 data-nosnippet>51</a>#[cfg_attr(feature = <span class="string">"serde"</span>, derive(Deserialize, Serialize))]
<a href=#52 id=52 data-nosnippet>52</a>#[derive(Debug, PartialEq, Default, Eq, Clone)]
<a href=#53 id=53 data-nosnippet>53</a></span><span class="kw">pub struct </span>VersionedTransaction {
<a href=#54 id=54 data-nosnippet>54</a>    <span class="doccomment">/// List of signatures
<a href=#55 id=55 data-nosnippet>55</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"short_vec"</span>))]
<a href=#56 id=56 data-nosnippet>56</a>    </span><span class="kw">pub </span>signatures: Vec&lt;Signature&gt;,
<a href=#57 id=57 data-nosnippet>57</a>    <span class="doccomment">/// Message to sign.
<a href=#58 id=58 data-nosnippet>58</a>    </span><span class="kw">pub </span>message: VersionedMessage,
<a href=#59 id=59 data-nosnippet>59</a>}
<a href=#60 id=60 data-nosnippet>60</a>
<a href=#61 id=61 data-nosnippet>61</a><span class="kw">impl </span>From&lt;Transaction&gt; <span class="kw">for </span>VersionedTransaction {
<a href=#62 id=62 data-nosnippet>62</a>    <span class="kw">fn </span>from(transaction: Transaction) -&gt; <span class="self">Self </span>{
<a href=#63 id=63 data-nosnippet>63</a>        <span class="self">Self </span>{
<a href=#64 id=64 data-nosnippet>64</a>            signatures: transaction.signatures,
<a href=#65 id=65 data-nosnippet>65</a>            message: VersionedMessage::Legacy(transaction.message),
<a href=#66 id=66 data-nosnippet>66</a>        }
<a href=#67 id=67 data-nosnippet>67</a>    }
<a href=#68 id=68 data-nosnippet>68</a>}
<a href=#69 id=69 data-nosnippet>69</a>
<a href=#70 id=70 data-nosnippet>70</a><span class="kw">impl </span>VersionedTransaction {
<a href=#71 id=71 data-nosnippet>71</a>    <span class="doccomment">/// Signs a versioned message and if successful, returns a signed
<a href=#72 id=72 data-nosnippet>72</a>    /// transaction.
<a href=#73 id=73 data-nosnippet>73</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#74 id=74 data-nosnippet>74</a>    </span><span class="kw">pub fn </span>try_new&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#75 id=75 data-nosnippet>75</a>        message: VersionedMessage,
<a href=#76 id=76 data-nosnippet>76</a>        keypairs: <span class="kw-2">&amp;</span>T,
<a href=#77 id=77 data-nosnippet>77</a>    ) -&gt; std::result::Result&lt;<span class="self">Self</span>, SignerError&gt; {
<a href=#78 id=78 data-nosnippet>78</a>        <span class="kw">let </span>static_account_keys = message.static_account_keys();
<a href=#79 id=79 data-nosnippet>79</a>        <span class="kw">if </span>static_account_keys.len() &lt; message.header().num_required_signatures <span class="kw">as </span>usize {
<a href=#80 id=80 data-nosnippet>80</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SignerError::InvalidInput(<span class="string">"invalid message"</span>.to_string()));
<a href=#81 id=81 data-nosnippet>81</a>        }
<a href=#82 id=82 data-nosnippet>82</a>
<a href=#83 id=83 data-nosnippet>83</a>        <span class="kw">let </span>signer_keys = keypairs.try_pubkeys()<span class="question-mark">?</span>;
<a href=#84 id=84 data-nosnippet>84</a>        <span class="kw">let </span>expected_signer_keys =
<a href=#85 id=85 data-nosnippet>85</a>            <span class="kw-2">&amp;</span>static_account_keys[<span class="number">0</span>..message.header().num_required_signatures <span class="kw">as </span>usize];
<a href=#86 id=86 data-nosnippet>86</a>
<a href=#87 id=87 data-nosnippet>87</a>        <span class="kw">match </span>signer_keys.len().cmp(<span class="kw-2">&amp;</span>expected_signer_keys.len()) {
<a href=#88 id=88 data-nosnippet>88</a>            Ordering::Greater =&gt; <span class="prelude-val">Err</span>(SignerError::TooManySigners),
<a href=#89 id=89 data-nosnippet>89</a>            Ordering::Less =&gt; <span class="prelude-val">Err</span>(SignerError::NotEnoughSigners),
<a href=#90 id=90 data-nosnippet>90</a>            Ordering::Equal =&gt; <span class="prelude-val">Ok</span>(()),
<a href=#91 id=91 data-nosnippet>91</a>        }<span class="question-mark">?</span>;
<a href=#92 id=92 data-nosnippet>92</a>
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">let </span>message_data = message.serialize();
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">let </span>signature_indexes: Vec&lt;usize&gt; = expected_signer_keys
<a href=#95 id=95 data-nosnippet>95</a>            .iter()
<a href=#96 id=96 data-nosnippet>96</a>            .map(|signer_key| {
<a href=#97 id=97 data-nosnippet>97</a>                signer_keys
<a href=#98 id=98 data-nosnippet>98</a>                    .iter()
<a href=#99 id=99 data-nosnippet>99</a>                    .position(|key| key == signer_key)
<a href=#100 id=100 data-nosnippet>100</a>                    .ok_or(SignerError::KeypairPubkeyMismatch)
<a href=#101 id=101 data-nosnippet>101</a>            })
<a href=#102 id=102 data-nosnippet>102</a>            .collect::&lt;std::result::Result&lt;<span class="kw">_</span>, SignerError&gt;&gt;()<span class="question-mark">?</span>;
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a>        <span class="kw">let </span>unordered_signatures = keypairs.try_sign_message(<span class="kw-2">&amp;</span>message_data)<span class="question-mark">?</span>;
<a href=#105 id=105 data-nosnippet>105</a>        <span class="kw">let </span>signatures: Vec&lt;Signature&gt; = signature_indexes
<a href=#106 id=106 data-nosnippet>106</a>            .into_iter()
<a href=#107 id=107 data-nosnippet>107</a>            .map(|index| {
<a href=#108 id=108 data-nosnippet>108</a>                unordered_signatures
<a href=#109 id=109 data-nosnippet>109</a>                    .get(index)
<a href=#110 id=110 data-nosnippet>110</a>                    .copied()
<a href=#111 id=111 data-nosnippet>111</a>                    .ok_or_else(|| SignerError::InvalidInput(<span class="string">"invalid keypairs"</span>.to_string()))
<a href=#112 id=112 data-nosnippet>112</a>            })
<a href=#113 id=113 data-nosnippet>113</a>            .collect::&lt;std::result::Result&lt;<span class="kw">_</span>, SignerError&gt;&gt;()<span class="question-mark">?</span>;
<a href=#114 id=114 data-nosnippet>114</a>
<a href=#115 id=115 data-nosnippet>115</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#116 id=116 data-nosnippet>116</a>            signatures,
<a href=#117 id=117 data-nosnippet>117</a>            message,
<a href=#118 id=118 data-nosnippet>118</a>        })
<a href=#119 id=119 data-nosnippet>119</a>    }
<a href=#120 id=120 data-nosnippet>120</a>
<a href=#121 id=121 data-nosnippet>121</a>    <span class="kw">pub fn </span>sanitize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; std::result::Result&lt;(), SanitizeError&gt; {
<a href=#122 id=122 data-nosnippet>122</a>        <span class="self">self</span>.message.sanitize()<span class="question-mark">?</span>;
<a href=#123 id=123 data-nosnippet>123</a>        <span class="self">self</span>.sanitize_signatures()<span class="question-mark">?</span>;
<a href=#124 id=124 data-nosnippet>124</a>        <span class="prelude-val">Ok</span>(())
<a href=#125 id=125 data-nosnippet>125</a>    }
<a href=#126 id=126 data-nosnippet>126</a>
<a href=#127 id=127 data-nosnippet>127</a>    <span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">fn </span>sanitize_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; std::result::Result&lt;(), SanitizeError&gt; {
<a href=#128 id=128 data-nosnippet>128</a>        <span class="self">Self</span>::sanitize_signatures_inner(
<a href=#129 id=129 data-nosnippet>129</a>            usize::from(<span class="self">self</span>.message.header().num_required_signatures),
<a href=#130 id=130 data-nosnippet>130</a>            <span class="self">self</span>.message.static_account_keys().len(),
<a href=#131 id=131 data-nosnippet>131</a>            <span class="self">self</span>.signatures.len(),
<a href=#132 id=132 data-nosnippet>132</a>        )
<a href=#133 id=133 data-nosnippet>133</a>    }
<a href=#134 id=134 data-nosnippet>134</a>
<a href=#135 id=135 data-nosnippet>135</a>    <span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">fn </span>sanitize_signatures_inner(
<a href=#136 id=136 data-nosnippet>136</a>        num_required_signatures: usize,
<a href=#137 id=137 data-nosnippet>137</a>        num_static_account_keys: usize,
<a href=#138 id=138 data-nosnippet>138</a>        num_signatures: usize,
<a href=#139 id=139 data-nosnippet>139</a>    ) -&gt; std::result::Result&lt;(), SanitizeError&gt; {
<a href=#140 id=140 data-nosnippet>140</a>        <span class="kw">match </span>num_required_signatures.cmp(<span class="kw-2">&amp;</span>num_signatures) {
<a href=#141 id=141 data-nosnippet>141</a>            Ordering::Greater =&gt; <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds),
<a href=#142 id=142 data-nosnippet>142</a>            Ordering::Less =&gt; <span class="prelude-val">Err</span>(SanitizeError::InvalidValue),
<a href=#143 id=143 data-nosnippet>143</a>            Ordering::Equal =&gt; <span class="prelude-val">Ok</span>(()),
<a href=#144 id=144 data-nosnippet>144</a>        }<span class="question-mark">?</span>;
<a href=#145 id=145 data-nosnippet>145</a>
<a href=#146 id=146 data-nosnippet>146</a>        <span class="comment">// Signatures are verified before message keys are loaded so all signers
<a href=#147 id=147 data-nosnippet>147</a>        // must correspond to static account keys.
<a href=#148 id=148 data-nosnippet>148</a>        </span><span class="kw">if </span>num_signatures &gt; num_static_account_keys {
<a href=#149 id=149 data-nosnippet>149</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#150 id=150 data-nosnippet>150</a>        }
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>        <span class="prelude-val">Ok</span>(())
<a href=#153 id=153 data-nosnippet>153</a>    }
<a href=#154 id=154 data-nosnippet>154</a>
<a href=#155 id=155 data-nosnippet>155</a>    <span class="doccomment">/// Returns the version of the transaction
<a href=#156 id=156 data-nosnippet>156</a>    </span><span class="kw">pub fn </span>version(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; TransactionVersion {
<a href=#157 id=157 data-nosnippet>157</a>        <span class="kw">match </span><span class="self">self</span>.message {
<a href=#158 id=158 data-nosnippet>158</a>            VersionedMessage::Legacy(<span class="kw">_</span>) =&gt; TransactionVersion::LEGACY,
<a href=#159 id=159 data-nosnippet>159</a>            VersionedMessage::V0(<span class="kw">_</span>) =&gt; TransactionVersion::Number(<span class="number">0</span>),
<a href=#160 id=160 data-nosnippet>160</a>        }
<a href=#161 id=161 data-nosnippet>161</a>    }
<a href=#162 id=162 data-nosnippet>162</a>
<a href=#163 id=163 data-nosnippet>163</a>    <span class="doccomment">/// Returns a legacy transaction if the transaction message is legacy.
<a href=#164 id=164 data-nosnippet>164</a>    </span><span class="kw">pub fn </span>into_legacy_transaction(<span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;Transaction&gt; {
<a href=#165 id=165 data-nosnippet>165</a>        <span class="kw">match </span><span class="self">self</span>.message {
<a href=#166 id=166 data-nosnippet>166</a>            VersionedMessage::Legacy(message) =&gt; <span class="prelude-val">Some</span>(Transaction {
<a href=#167 id=167 data-nosnippet>167</a>                signatures: <span class="self">self</span>.signatures,
<a href=#168 id=168 data-nosnippet>168</a>                message,
<a href=#169 id=169 data-nosnippet>169</a>            }),
<a href=#170 id=170 data-nosnippet>170</a>            <span class="kw">_ </span>=&gt; <span class="prelude-val">None</span>,
<a href=#171 id=171 data-nosnippet>171</a>        }
<a href=#172 id=172 data-nosnippet>172</a>    }
<a href=#173 id=173 data-nosnippet>173</a>
<a href=#174 id=174 data-nosnippet>174</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#175 id=175 data-nosnippet>175</a>    </span><span class="doccomment">/// Verify the transaction and hash its message
<a href=#176 id=176 data-nosnippet>176</a>    </span><span class="kw">pub fn </span>verify_and_hash_message(
<a href=#177 id=177 data-nosnippet>177</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#178 id=178 data-nosnippet>178</a>    ) -&gt; solana_transaction_error::TransactionResult&lt;solana_hash::Hash&gt; {
<a href=#179 id=179 data-nosnippet>179</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.message.serialize();
<a href=#180 id=180 data-nosnippet>180</a>        <span class="kw">if </span>!<span class="self">self
<a href=#181 id=181 data-nosnippet>181</a>            </span>._verify_with_results(<span class="kw-2">&amp;</span>message_bytes)
<a href=#182 id=182 data-nosnippet>182</a>            .iter()
<a href=#183 id=183 data-nosnippet>183</a>            .all(|verify_result| <span class="kw-2">*</span>verify_result)
<a href=#184 id=184 data-nosnippet>184</a>        {
<a href=#185 id=185 data-nosnippet>185</a>            <span class="prelude-val">Err</span>(solana_transaction_error::TransactionError::SignatureFailure)
<a href=#186 id=186 data-nosnippet>186</a>        } <span class="kw">else </span>{
<a href=#187 id=187 data-nosnippet>187</a>            <span class="prelude-val">Ok</span>(VersionedMessage::hash_raw_message(<span class="kw-2">&amp;</span>message_bytes))
<a href=#188 id=188 data-nosnippet>188</a>        }
<a href=#189 id=189 data-nosnippet>189</a>    }
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#192 id=192 data-nosnippet>192</a>    </span><span class="doccomment">/// Verify the transaction and return a list of verification results
<a href=#193 id=193 data-nosnippet>193</a>    </span><span class="kw">pub fn </span>verify_with_results(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;bool&gt; {
<a href=#194 id=194 data-nosnippet>194</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.message.serialize();
<a href=#195 id=195 data-nosnippet>195</a>        <span class="self">self</span>._verify_with_results(<span class="kw-2">&amp;</span>message_bytes)
<a href=#196 id=196 data-nosnippet>196</a>    }
<a href=#197 id=197 data-nosnippet>197</a>
<a href=#198 id=198 data-nosnippet>198</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#199 id=199 data-nosnippet>199</a>    </span><span class="kw">fn </span>_verify_with_results(<span class="kw-2">&amp;</span><span class="self">self</span>, message_bytes: <span class="kw-2">&amp;</span>[u8]) -&gt; Vec&lt;bool&gt; {
<a href=#200 id=200 data-nosnippet>200</a>        <span class="self">self</span>.signatures
<a href=#201 id=201 data-nosnippet>201</a>            .iter()
<a href=#202 id=202 data-nosnippet>202</a>            .zip(<span class="self">self</span>.message.static_account_keys().iter())
<a href=#203 id=203 data-nosnippet>203</a>            .map(|(signature, pubkey)| signature.verify(pubkey.as_ref(), message_bytes))
<a href=#204 id=204 data-nosnippet>204</a>            .collect()
<a href=#205 id=205 data-nosnippet>205</a>    }
<a href=#206 id=206 data-nosnippet>206</a>
<a href=#207 id=207 data-nosnippet>207</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#208 id=208 data-nosnippet>208</a>    </span><span class="doccomment">/// Returns true if transaction begins with an advance nonce instruction.
<a href=#209 id=209 data-nosnippet>209</a>    </span><span class="kw">pub fn </span>uses_durable_nonce(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#210 id=210 data-nosnippet>210</a>        <span class="kw">let </span>message = <span class="kw-2">&amp;</span><span class="self">self</span>.message;
<a href=#211 id=211 data-nosnippet>211</a>        message
<a href=#212 id=212 data-nosnippet>212</a>            .instructions()
<a href=#213 id=213 data-nosnippet>213</a>            .get(<span class="kw">crate</span>::NONCED_TX_MARKER_IX_INDEX <span class="kw">as </span>usize)
<a href=#214 id=214 data-nosnippet>214</a>            .filter(|instruction| {
<a href=#215 id=215 data-nosnippet>215</a>                <span class="comment">// Is system program
<a href=#216 id=216 data-nosnippet>216</a>                </span><span class="macro">matches!</span>(
<a href=#217 id=217 data-nosnippet>217</a>                    message.static_account_keys().get(instruction.program_id_index <span class="kw">as </span>usize),
<a href=#218 id=218 data-nosnippet>218</a>                    <span class="prelude-val">Some</span>(program_id) <span class="kw">if </span>system_program::check_id(program_id)
<a href=#219 id=219 data-nosnippet>219</a>                )
<a href=#220 id=220 data-nosnippet>220</a>                <span class="comment">// Is a nonce advance instruction
<a href=#221 id=221 data-nosnippet>221</a>                </span>&amp;&amp; <span class="macro">matches!</span>(
<a href=#222 id=222 data-nosnippet>222</a>                    limited_deserialize(<span class="kw-2">&amp;</span>instruction.data, <span class="kw">crate</span>::PACKET_DATA_SIZE <span class="kw">as </span>u64,),
<a href=#223 id=223 data-nosnippet>223</a>                    <span class="prelude-val">Ok</span>(SystemInstruction::AdvanceNonceAccount)
<a href=#224 id=224 data-nosnippet>224</a>                )
<a href=#225 id=225 data-nosnippet>225</a>            })
<a href=#226 id=226 data-nosnippet>226</a>            .is_some()
<a href=#227 id=227 data-nosnippet>227</a>    }
<a href=#228 id=228 data-nosnippet>228</a>}
<a href=#229 id=229 data-nosnippet>229</a>
<a href=#230 id=230 data-nosnippet>230</a><span class="attr">#[cfg(test)]
<a href=#231 id=231 data-nosnippet>231</a></span><span class="kw">mod </span>tests {
<a href=#232 id=232 data-nosnippet>232</a>    <span class="kw">use </span>{
<a href=#233 id=233 data-nosnippet>233</a>        <span class="kw">super</span>::<span class="kw-2">*</span>,
<a href=#234 id=234 data-nosnippet>234</a>        solana_hash::Hash,
<a href=#235 id=235 data-nosnippet>235</a>        solana_instruction::{AccountMeta, Instruction},
<a href=#236 id=236 data-nosnippet>236</a>        solana_keypair::Keypair,
<a href=#237 id=237 data-nosnippet>237</a>        solana_message::Message <span class="kw">as </span>LegacyMessage,
<a href=#238 id=238 data-nosnippet>238</a>        solana_pubkey::Pubkey,
<a href=#239 id=239 data-nosnippet>239</a>        solana_signer::Signer,
<a href=#240 id=240 data-nosnippet>240</a>        solana_system_interface::instruction <span class="kw">as </span>system_instruction,
<a href=#241 id=241 data-nosnippet>241</a>    };
<a href=#242 id=242 data-nosnippet>242</a>
<a href=#243 id=243 data-nosnippet>243</a>    <span class="attr">#[test]
<a href=#244 id=244 data-nosnippet>244</a>    </span><span class="kw">fn </span>test_try_new() {
<a href=#245 id=245 data-nosnippet>245</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#246 id=246 data-nosnippet>246</a>        <span class="kw">let </span>keypair1 = Keypair::new();
<a href=#247 id=247 data-nosnippet>247</a>        <span class="kw">let </span>keypair2 = Keypair::new();
<a href=#248 id=248 data-nosnippet>248</a>
<a href=#249 id=249 data-nosnippet>249</a>        <span class="kw">let </span>message = VersionedMessage::Legacy(LegacyMessage::new(
<a href=#250 id=250 data-nosnippet>250</a>            <span class="kw-2">&amp;</span>[Instruction::new_with_bytes(
<a href=#251 id=251 data-nosnippet>251</a>                Pubkey::new_unique(),
<a href=#252 id=252 data-nosnippet>252</a>                <span class="kw-2">&amp;</span>[],
<a href=#253 id=253 data-nosnippet>253</a>                <span class="macro">vec!</span>[
<a href=#254 id=254 data-nosnippet>254</a>                    AccountMeta::new_readonly(keypair1.pubkey(), <span class="bool-val">true</span>),
<a href=#255 id=255 data-nosnippet>255</a>                    AccountMeta::new_readonly(keypair2.pubkey(), <span class="bool-val">false</span>),
<a href=#256 id=256 data-nosnippet>256</a>                ],
<a href=#257 id=257 data-nosnippet>257</a>            )],
<a href=#258 id=258 data-nosnippet>258</a>            <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keypair0.pubkey()),
<a href=#259 id=259 data-nosnippet>259</a>        ));
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>        <span class="macro">assert_eq!</span>(
<a href=#262 id=262 data-nosnippet>262</a>            VersionedTransaction::try_new(message.clone(), <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0]),
<a href=#263 id=263 data-nosnippet>263</a>            <span class="prelude-val">Err</span>(SignerError::NotEnoughSigners)
<a href=#264 id=264 data-nosnippet>264</a>        );
<a href=#265 id=265 data-nosnippet>265</a>
<a href=#266 id=266 data-nosnippet>266</a>        <span class="macro">assert_eq!</span>(
<a href=#267 id=267 data-nosnippet>267</a>            VersionedTransaction::try_new(message.clone(), <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0, <span class="kw-2">&amp;</span>keypair0]),
<a href=#268 id=268 data-nosnippet>268</a>            <span class="prelude-val">Err</span>(SignerError::KeypairPubkeyMismatch)
<a href=#269 id=269 data-nosnippet>269</a>        );
<a href=#270 id=270 data-nosnippet>270</a>
<a href=#271 id=271 data-nosnippet>271</a>        <span class="macro">assert_eq!</span>(
<a href=#272 id=272 data-nosnippet>272</a>            VersionedTransaction::try_new(message.clone(), <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair1, <span class="kw-2">&amp;</span>keypair2]),
<a href=#273 id=273 data-nosnippet>273</a>            <span class="prelude-val">Err</span>(SignerError::KeypairPubkeyMismatch)
<a href=#274 id=274 data-nosnippet>274</a>        );
<a href=#275 id=275 data-nosnippet>275</a>
<a href=#276 id=276 data-nosnippet>276</a>        <span class="kw">match </span>VersionedTransaction::try_new(message.clone(), <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0, <span class="kw-2">&amp;</span>keypair1]) {
<a href=#277 id=277 data-nosnippet>277</a>            <span class="prelude-val">Ok</span>(tx) =&gt; <span class="macro">assert_eq!</span>(tx.verify_with_results(), <span class="macro">vec!</span>[<span class="bool-val">true</span>; <span class="number">2</span>]),
<a href=#278 id=278 data-nosnippet>278</a>            <span class="prelude-val">Err</span>(err) =&gt; <span class="macro">assert_eq!</span>(<span class="prelude-val">Some</span>(err), <span class="prelude-val">None</span>),
<a href=#279 id=279 data-nosnippet>279</a>        }
<a href=#280 id=280 data-nosnippet>280</a>
<a href=#281 id=281 data-nosnippet>281</a>        <span class="kw">match </span>VersionedTransaction::try_new(message, <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair1, <span class="kw-2">&amp;</span>keypair0]) {
<a href=#282 id=282 data-nosnippet>282</a>            <span class="prelude-val">Ok</span>(tx) =&gt; <span class="macro">assert_eq!</span>(tx.verify_with_results(), <span class="macro">vec!</span>[<span class="bool-val">true</span>; <span class="number">2</span>]),
<a href=#283 id=283 data-nosnippet>283</a>            <span class="prelude-val">Err</span>(err) =&gt; <span class="macro">assert_eq!</span>(<span class="prelude-val">Some</span>(err), <span class="prelude-val">None</span>),
<a href=#284 id=284 data-nosnippet>284</a>        }
<a href=#285 id=285 data-nosnippet>285</a>    }
<a href=#286 id=286 data-nosnippet>286</a>
<a href=#287 id=287 data-nosnippet>287</a>    <span class="kw">fn </span>nonced_transfer_tx() -&gt; (Pubkey, Pubkey, VersionedTransaction) {
<a href=#288 id=288 data-nosnippet>288</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#289 id=289 data-nosnippet>289</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#290 id=290 data-nosnippet>290</a>        <span class="kw">let </span>nonce_keypair = Keypair::new();
<a href=#291 id=291 data-nosnippet>291</a>        <span class="kw">let </span>nonce_pubkey = nonce_keypair.pubkey();
<a href=#292 id=292 data-nosnippet>292</a>        <span class="kw">let </span>instructions = [
<a href=#293 id=293 data-nosnippet>293</a>            system_instruction::advance_nonce_account(<span class="kw-2">&amp;</span>nonce_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey),
<a href=#294 id=294 data-nosnippet>294</a>            system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey, <span class="number">42</span>),
<a href=#295 id=295 data-nosnippet>295</a>        ];
<a href=#296 id=296 data-nosnippet>296</a>        <span class="kw">let </span>message = LegacyMessage::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>nonce_pubkey));
<a href=#297 id=297 data-nosnippet>297</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>nonce_keypair], message, Hash::default());
<a href=#298 id=298 data-nosnippet>298</a>        (from_pubkey, nonce_pubkey, tx.into())
<a href=#299 id=299 data-nosnippet>299</a>    }
<a href=#300 id=300 data-nosnippet>300</a>
<a href=#301 id=301 data-nosnippet>301</a>    <span class="attr">#[test]
<a href=#302 id=302 data-nosnippet>302</a>    </span><span class="kw">fn </span>tx_uses_nonce_ok() {
<a href=#303 id=303 data-nosnippet>303</a>        <span class="kw">let </span>(<span class="kw">_</span>, <span class="kw">_</span>, tx) = nonced_transfer_tx();
<a href=#304 id=304 data-nosnippet>304</a>        <span class="macro">assert!</span>(tx.uses_durable_nonce());
<a href=#305 id=305 data-nosnippet>305</a>    }
<a href=#306 id=306 data-nosnippet>306</a>
<a href=#307 id=307 data-nosnippet>307</a>    <span class="attr">#[test]
<a href=#308 id=308 data-nosnippet>308</a>    </span><span class="kw">fn </span>tx_uses_nonce_empty_ix_fail() {
<a href=#309 id=309 data-nosnippet>309</a>        <span class="macro">assert!</span>(!VersionedTransaction::default().uses_durable_nonce());
<a href=#310 id=310 data-nosnippet>310</a>    }
<a href=#311 id=311 data-nosnippet>311</a>
<a href=#312 id=312 data-nosnippet>312</a>    <span class="attr">#[test]
<a href=#313 id=313 data-nosnippet>313</a>    </span><span class="kw">fn </span>tx_uses_nonce_bad_prog_id_idx_fail() {
<a href=#314 id=314 data-nosnippet>314</a>        <span class="kw">let </span>(<span class="kw">_</span>, <span class="kw">_</span>, <span class="kw-2">mut </span>tx) = nonced_transfer_tx();
<a href=#315 id=315 data-nosnippet>315</a>        <span class="kw">match </span><span class="kw-2">&amp;mut </span>tx.message {
<a href=#316 id=316 data-nosnippet>316</a>            VersionedMessage::Legacy(message) =&gt; {
<a href=#317 id=317 data-nosnippet>317</a>                message.instructions.get_mut(<span class="number">0</span>).unwrap().program_id_index = <span class="number">255u8</span>;
<a href=#318 id=318 data-nosnippet>318</a>            }
<a href=#319 id=319 data-nosnippet>319</a>            VersionedMessage::V0(<span class="kw">_</span>) =&gt; <span class="macro">unreachable!</span>(),
<a href=#320 id=320 data-nosnippet>320</a>        };
<a href=#321 id=321 data-nosnippet>321</a>        <span class="macro">assert!</span>(!tx.uses_durable_nonce());
<a href=#322 id=322 data-nosnippet>322</a>    }
<a href=#323 id=323 data-nosnippet>323</a>
<a href=#324 id=324 data-nosnippet>324</a>    <span class="attr">#[test]
<a href=#325 id=325 data-nosnippet>325</a>    </span><span class="kw">fn </span>tx_uses_nonce_first_prog_id_not_nonce_fail() {
<a href=#326 id=326 data-nosnippet>326</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#327 id=327 data-nosnippet>327</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#328 id=328 data-nosnippet>328</a>        <span class="kw">let </span>nonce_keypair = Keypair::new();
<a href=#329 id=329 data-nosnippet>329</a>        <span class="kw">let </span>nonce_pubkey = nonce_keypair.pubkey();
<a href=#330 id=330 data-nosnippet>330</a>        <span class="kw">let </span>instructions = [
<a href=#331 id=331 data-nosnippet>331</a>            system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey, <span class="number">42</span>),
<a href=#332 id=332 data-nosnippet>332</a>            system_instruction::advance_nonce_account(<span class="kw-2">&amp;</span>nonce_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey),
<a href=#333 id=333 data-nosnippet>333</a>        ];
<a href=#334 id=334 data-nosnippet>334</a>        <span class="kw">let </span>message = LegacyMessage::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>from_pubkey));
<a href=#335 id=335 data-nosnippet>335</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>nonce_keypair], message, Hash::default());
<a href=#336 id=336 data-nosnippet>336</a>        <span class="kw">let </span>tx = VersionedTransaction::from(tx);
<a href=#337 id=337 data-nosnippet>337</a>        <span class="macro">assert!</span>(!tx.uses_durable_nonce());
<a href=#338 id=338 data-nosnippet>338</a>    }
<a href=#339 id=339 data-nosnippet>339</a>
<a href=#340 id=340 data-nosnippet>340</a>    <span class="attr">#[test]
<a href=#341 id=341 data-nosnippet>341</a>    </span><span class="kw">fn </span>tx_uses_nonce_wrong_first_nonce_ix_fail() {
<a href=#342 id=342 data-nosnippet>342</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#343 id=343 data-nosnippet>343</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#344 id=344 data-nosnippet>344</a>        <span class="kw">let </span>nonce_keypair = Keypair::new();
<a href=#345 id=345 data-nosnippet>345</a>        <span class="kw">let </span>nonce_pubkey = nonce_keypair.pubkey();
<a href=#346 id=346 data-nosnippet>346</a>        <span class="kw">let </span>instructions = [
<a href=#347 id=347 data-nosnippet>347</a>            system_instruction::withdraw_nonce_account(
<a href=#348 id=348 data-nosnippet>348</a>                <span class="kw-2">&amp;</span>nonce_pubkey,
<a href=#349 id=349 data-nosnippet>349</a>                <span class="kw-2">&amp;</span>nonce_pubkey,
<a href=#350 id=350 data-nosnippet>350</a>                <span class="kw-2">&amp;</span>from_pubkey,
<a href=#351 id=351 data-nosnippet>351</a>                <span class="number">42</span>,
<a href=#352 id=352 data-nosnippet>352</a>            ),
<a href=#353 id=353 data-nosnippet>353</a>            system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey, <span class="number">42</span>),
<a href=#354 id=354 data-nosnippet>354</a>        ];
<a href=#355 id=355 data-nosnippet>355</a>        <span class="kw">let </span>message = LegacyMessage::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>nonce_pubkey));
<a href=#356 id=356 data-nosnippet>356</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>nonce_keypair], message, Hash::default());
<a href=#357 id=357 data-nosnippet>357</a>        <span class="kw">let </span>tx = VersionedTransaction::from(tx);
<a href=#358 id=358 data-nosnippet>358</a>        <span class="macro">assert!</span>(!tx.uses_durable_nonce());
<a href=#359 id=359 data-nosnippet>359</a>    }
<a href=#360 id=360 data-nosnippet>360</a>
<a href=#361 id=361 data-nosnippet>361</a>    <span class="attr">#[test]
<a href=#362 id=362 data-nosnippet>362</a>    </span><span class="kw">fn </span>test_sanitize_signatures_inner() {
<a href=#363 id=363 data-nosnippet>363</a>        <span class="macro">assert_eq!</span>(
<a href=#364 id=364 data-nosnippet>364</a>            VersionedTransaction::sanitize_signatures_inner(<span class="number">1</span>, <span class="number">1</span>, <span class="number">0</span>),
<a href=#365 id=365 data-nosnippet>365</a>            <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds)
<a href=#366 id=366 data-nosnippet>366</a>        );
<a href=#367 id=367 data-nosnippet>367</a>        <span class="macro">assert_eq!</span>(
<a href=#368 id=368 data-nosnippet>368</a>            VersionedTransaction::sanitize_signatures_inner(<span class="number">1</span>, <span class="number">1</span>, <span class="number">2</span>),
<a href=#369 id=369 data-nosnippet>369</a>            <span class="prelude-val">Err</span>(SanitizeError::InvalidValue)
<a href=#370 id=370 data-nosnippet>370</a>        );
<a href=#371 id=371 data-nosnippet>371</a>        <span class="macro">assert_eq!</span>(
<a href=#372 id=372 data-nosnippet>372</a>            VersionedTransaction::sanitize_signatures_inner(<span class="number">2</span>, <span class="number">1</span>, <span class="number">2</span>),
<a href=#373 id=373 data-nosnippet>373</a>            <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds)
<a href=#374 id=374 data-nosnippet>374</a>        );
<a href=#375 id=375 data-nosnippet>375</a>        <span class="macro">assert_eq!</span>(
<a href=#376 id=376 data-nosnippet>376</a>            VersionedTransaction::sanitize_signatures_inner(<span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>),
<a href=#377 id=377 data-nosnippet>377</a>            <span class="prelude-val">Ok</span>(())
<a href=#378 id=378 data-nosnippet>378</a>        );
<a href=#379 id=379 data-nosnippet>379</a>    }
<a href=#380 id=380 data-nosnippet>380</a>}</code></pre></div></section></main></body></html>