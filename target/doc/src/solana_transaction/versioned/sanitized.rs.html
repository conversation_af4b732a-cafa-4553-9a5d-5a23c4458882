<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-transaction-2.2.2/src/versioned/sanitized.rs`."><title>sanitized.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_transaction/versioned/</div>sanitized.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>{
<a href=#2 id=2 data-nosnippet>2</a>    <span class="kw">crate</span>::versioned::VersionedTransaction, solana_message::SanitizedVersionedMessage,
<a href=#3 id=3 data-nosnippet>3</a>    solana_sanitize::SanitizeError, solana_signature::Signature,
<a href=#4 id=4 data-nosnippet>4</a>};
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="doccomment">/// Wraps a sanitized `VersionedTransaction` to provide a safe API
<a href=#7 id=7 data-nosnippet>7</a></span><span class="attr">#[derive(Clone, Debug, PartialEq, Eq)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">pub struct </span>SanitizedVersionedTransaction {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="doccomment">/// List of signatures
<a href=#10 id=10 data-nosnippet>10</a>    </span><span class="kw">pub</span>(<span class="kw">crate</span>) signatures: Vec&lt;Signature&gt;,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="doccomment">/// Message to sign.
<a href=#12 id=12 data-nosnippet>12</a>    </span><span class="kw">pub</span>(<span class="kw">crate</span>) message: SanitizedVersionedMessage,
<a href=#13 id=13 data-nosnippet>13</a>}
<a href=#14 id=14 data-nosnippet>14</a>
<a href=#15 id=15 data-nosnippet>15</a><span class="kw">impl </span>TryFrom&lt;VersionedTransaction&gt; <span class="kw">for </span>SanitizedVersionedTransaction {
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">type </span>Error = SanitizeError;
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">fn </span>try_from(tx: VersionedTransaction) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, <span class="self">Self</span>::Error&gt; {
<a href=#18 id=18 data-nosnippet>18</a>        <span class="self">Self</span>::try_new(tx)
<a href=#19 id=19 data-nosnippet>19</a>    }
<a href=#20 id=20 data-nosnippet>20</a>}
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="kw">impl </span>SanitizedVersionedTransaction {
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub fn </span>try_new(tx: VersionedTransaction) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, SanitizeError&gt; {
<a href=#24 id=24 data-nosnippet>24</a>        tx.sanitize_signatures()<span class="question-mark">?</span>;
<a href=#25 id=25 data-nosnippet>25</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#26 id=26 data-nosnippet>26</a>            signatures: tx.signatures,
<a href=#27 id=27 data-nosnippet>27</a>            message: SanitizedVersionedMessage::try_from(tx.message)<span class="question-mark">?</span>,
<a href=#28 id=28 data-nosnippet>28</a>        })
<a href=#29 id=29 data-nosnippet>29</a>    }
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub fn </span>get_message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>SanitizedVersionedMessage {
<a href=#32 id=32 data-nosnippet>32</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.message
<a href=#33 id=33 data-nosnippet>33</a>    }
<a href=#34 id=34 data-nosnippet>34</a>
<a href=#35 id=35 data-nosnippet>35</a>    <span class="doccomment">/// Consumes the SanitizedVersionedTransaction, returning the fields individually.
<a href=#36 id=36 data-nosnippet>36</a>    </span><span class="kw">pub fn </span>destruct(<span class="self">self</span>) -&gt; (Vec&lt;Signature&gt;, SanitizedVersionedMessage) {
<a href=#37 id=37 data-nosnippet>37</a>        (<span class="self">self</span>.signatures, <span class="self">self</span>.message)
<a href=#38 id=38 data-nosnippet>38</a>    }
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="attr">#[cfg(test)]
<a href=#42 id=42 data-nosnippet>42</a></span><span class="kw">mod </span>tests {
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">use </span>{
<a href=#44 id=44 data-nosnippet>44</a>        <span class="kw">super</span>::<span class="kw-2">*</span>,
<a href=#45 id=45 data-nosnippet>45</a>        solana_hash::Hash,
<a href=#46 id=46 data-nosnippet>46</a>        solana_message::{v0, VersionedMessage},
<a href=#47 id=47 data-nosnippet>47</a>        solana_pubkey::Pubkey,
<a href=#48 id=48 data-nosnippet>48</a>    };
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a>    <span class="attr">#[test]
<a href=#51 id=51 data-nosnippet>51</a>    </span><span class="kw">fn </span>test_try_new_with_invalid_signatures() {
<a href=#52 id=52 data-nosnippet>52</a>        <span class="kw">let </span>tx = VersionedTransaction {
<a href=#53 id=53 data-nosnippet>53</a>            signatures: <span class="macro">vec!</span>[],
<a href=#54 id=54 data-nosnippet>54</a>            message: VersionedMessage::V0(
<a href=#55 id=55 data-nosnippet>55</a>                v0::Message::try_compile(<span class="kw-2">&amp;</span>Pubkey::new_unique(), <span class="kw-2">&amp;</span>[], <span class="kw-2">&amp;</span>[], Hash::default()).unwrap(),
<a href=#56 id=56 data-nosnippet>56</a>            ),
<a href=#57 id=57 data-nosnippet>57</a>        };
<a href=#58 id=58 data-nosnippet>58</a>
<a href=#59 id=59 data-nosnippet>59</a>        <span class="macro">assert_eq!</span>(
<a href=#60 id=60 data-nosnippet>60</a>            SanitizedVersionedTransaction::try_new(tx),
<a href=#61 id=61 data-nosnippet>61</a>            <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds)
<a href=#62 id=62 data-nosnippet>62</a>        );
<a href=#63 id=63 data-nosnippet>63</a>    }
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a>    <span class="attr">#[test]
<a href=#66 id=66 data-nosnippet>66</a>    </span><span class="kw">fn </span>test_try_new() {
<a href=#67 id=67 data-nosnippet>67</a>        <span class="kw">let </span><span class="kw-2">mut </span>message =
<a href=#68 id=68 data-nosnippet>68</a>            v0::Message::try_compile(<span class="kw-2">&amp;</span>Pubkey::new_unique(), <span class="kw-2">&amp;</span>[], <span class="kw-2">&amp;</span>[], Hash::default()).unwrap();
<a href=#69 id=69 data-nosnippet>69</a>        message.header.num_readonly_signed_accounts += <span class="number">1</span>;
<a href=#70 id=70 data-nosnippet>70</a>
<a href=#71 id=71 data-nosnippet>71</a>        <span class="kw">let </span>tx = VersionedTransaction {
<a href=#72 id=72 data-nosnippet>72</a>            signatures: <span class="macro">vec!</span>[Signature::default()],
<a href=#73 id=73 data-nosnippet>73</a>            message: VersionedMessage::V0(message),
<a href=#74 id=74 data-nosnippet>74</a>        };
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>        <span class="macro">assert_eq!</span>(
<a href=#77 id=77 data-nosnippet>77</a>            SanitizedVersionedTransaction::try_new(tx),
<a href=#78 id=78 data-nosnippet>78</a>            <span class="prelude-val">Err</span>(SanitizeError::InvalidValue)
<a href=#79 id=79 data-nosnippet>79</a>        );
<a href=#80 id=80 data-nosnippet>80</a>    }
<a href=#81 id=81 data-nosnippet>81</a>}</code></pre></div></section></main></body></html>