<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-transaction-2.2.2/src/lib.rs`."><title>lib.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_transaction/</div>lib.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-4"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#![cfg_attr(feature = <span class="string">"frozen-abi"</span>, feature(min_specialization))]
<a href=#2 id=2 data-nosnippet>2</a>#![cfg_attr(docsrs, feature(doc_auto_cfg))]
<a href=#3 id=3 data-nosnippet>3</a></span><span class="doccomment">//! Atomically-committed sequences of instructions.
<a href=#4 id=4 data-nosnippet>4</a>//!
<a href=#5 id=5 data-nosnippet>5</a>//! While [`Instruction`]s are the basic unit of computation in Solana, they are
<a href=#6 id=6 data-nosnippet>6</a>//! submitted by clients in [`Transaction`]s containing one or more
<a href=#7 id=7 data-nosnippet>7</a>//! instructions, and signed by one or more [`Signer`]s. Solana executes the
<a href=#8 id=8 data-nosnippet>8</a>//! instructions in a transaction in order, and only commits any changes if all
<a href=#9 id=9 data-nosnippet>9</a>//! instructions terminate without producing an error or exception.
<a href=#10 id=10 data-nosnippet>10</a>//!
<a href=#11 id=11 data-nosnippet>11</a>//! Transactions do not directly contain their instructions but instead include
<a href=#12 id=12 data-nosnippet>12</a>//! a [`Message`], a precompiled representation of a sequence of instructions.
<a href=#13 id=13 data-nosnippet>13</a>//! `Message`'s constructors handle the complex task of reordering the
<a href=#14 id=14 data-nosnippet>14</a>//! individual lists of accounts required by each instruction into a single flat
<a href=#15 id=15 data-nosnippet>15</a>//! list of deduplicated accounts required by the Solana runtime. The
<a href=#16 id=16 data-nosnippet>16</a>//! `Transaction` type has constructors that build the `Message` so that clients
<a href=#17 id=17 data-nosnippet>17</a>//! don't need to interact with them directly.
<a href=#18 id=18 data-nosnippet>18</a>//!
<a href=#19 id=19 data-nosnippet>19</a>//! Prior to submission to the network, transactions must be signed by one or
<a href=#20 id=20 data-nosnippet>20</a>//! more keypairs, and this signing is typically performed by an abstract
<a href=#21 id=21 data-nosnippet>21</a>//! [`Signer`], which may be a [`Keypair`] but may also be other types of
<a href=#22 id=22 data-nosnippet>22</a>//! signers including remote wallets, such as Ledger devices, as represented by
<a href=#23 id=23 data-nosnippet>23</a>//! the [`RemoteKeypair`] type in the [`solana-remote-wallet`] crate.
<a href=#24 id=24 data-nosnippet>24</a>//!
<a href=#25 id=25 data-nosnippet>25</a>//! [`Signer`]: https://docs.rs/solana-signer/latest/solana_signer/trait.Signer.html
<a href=#26 id=26 data-nosnippet>26</a>//! [`Keypair`]: https://docs.rs/solana-keypair/latest/solana_keypair/struct.Keypair.html
<a href=#27 id=27 data-nosnippet>27</a>//! [`solana-remote-wallet`]: https://docs.rs/solana-remote-wallet/latest/
<a href=#28 id=28 data-nosnippet>28</a>//! [`RemoteKeypair`]: https://docs.rs/solana-remote-wallet/latest/solana_remote_wallet/remote_keypair/struct.RemoteKeypair.html
<a href=#29 id=29 data-nosnippet>29</a>//!
<a href=#30 id=30 data-nosnippet>30</a>//! Every transaction must be signed by a fee-paying account, the account from
<a href=#31 id=31 data-nosnippet>31</a>//! which the cost of executing the transaction is withdrawn. Other required
<a href=#32 id=32 data-nosnippet>32</a>//! signatures are determined by the requirements of the programs being executed
<a href=#33 id=33 data-nosnippet>33</a>//! by each instruction, and are conventionally specified by that program's
<a href=#34 id=34 data-nosnippet>34</a>//! documentation.
<a href=#35 id=35 data-nosnippet>35</a>//!
<a href=#36 id=36 data-nosnippet>36</a>//! When signing a transaction, a recent blockhash must be provided (which can
<a href=#37 id=37 data-nosnippet>37</a>//! be retrieved with [`RpcClient::get_latest_blockhash`]). This allows
<a href=#38 id=38 data-nosnippet>38</a>//! validators to drop old but unexecuted transactions; and to distinguish
<a href=#39 id=39 data-nosnippet>39</a>//! between accidentally duplicated transactions and intentionally duplicated
<a href=#40 id=40 data-nosnippet>40</a>//! transactions &amp;mdash; any identical transactions will not be executed more
<a href=#41 id=41 data-nosnippet>41</a>//! than once, so updating the blockhash between submitting otherwise identical
<a href=#42 id=42 data-nosnippet>42</a>//! transactions makes them unique. If a client must sign a transaction long
<a href=#43 id=43 data-nosnippet>43</a>//! before submitting it to the network, then it can use the _[durable
<a href=#44 id=44 data-nosnippet>44</a>//! transaction nonce]_ mechanism instead of a recent blockhash to ensure unique
<a href=#45 id=45 data-nosnippet>45</a>//! transactions.
<a href=#46 id=46 data-nosnippet>46</a>//!
<a href=#47 id=47 data-nosnippet>47</a>//! [`RpcClient::get_latest_blockhash`]: https://docs.rs/solana-rpc-client/latest/solana_rpc_client/rpc_client/struct.RpcClient.html#method.get_latest_blockhash
<a href=#48 id=48 data-nosnippet>48</a>//! [durable transaction nonce]: https://docs.solanalabs.com/implemented-proposals/durable-tx-nonces
<a href=#49 id=49 data-nosnippet>49</a>//!
<a href=#50 id=50 data-nosnippet>50</a>//! # Examples
<a href=#51 id=51 data-nosnippet>51</a>//!
<a href=#52 id=52 data-nosnippet>52</a>//! This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#53 id=53 data-nosnippet>53</a>//!
<a href=#54 id=54 data-nosnippet>54</a>//! [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#55 id=55 data-nosnippet>55</a>//! [`anyhow`]: https://docs.rs/anyhow
<a href=#56 id=56 data-nosnippet>56</a>//!
<a href=#57 id=57 data-nosnippet>57</a>//! ```
<a href=#58 id=58 data-nosnippet>58</a>//! # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#59 id=59 data-nosnippet>59</a>//! use anyhow::Result;
<a href=#60 id=60 data-nosnippet>60</a>//! use borsh::{BorshSerialize, BorshDeserialize};
<a href=#61 id=61 data-nosnippet>61</a>//! use solana_instruction::Instruction;
<a href=#62 id=62 data-nosnippet>62</a>//! use solana_keypair::Keypair;
<a href=#63 id=63 data-nosnippet>63</a>//! use solana_message::Message;
<a href=#64 id=64 data-nosnippet>64</a>//! use solana_pubkey::Pubkey;
<a href=#65 id=65 data-nosnippet>65</a>//! use solana_rpc_client::rpc_client::RpcClient;
<a href=#66 id=66 data-nosnippet>66</a>//! use solana_signer::Signer;
<a href=#67 id=67 data-nosnippet>67</a>//! use solana_transaction::Transaction;
<a href=#68 id=68 data-nosnippet>68</a>//!
<a href=#69 id=69 data-nosnippet>69</a>//! // A custom program instruction. This would typically be defined in
<a href=#70 id=70 data-nosnippet>70</a>//! // another crate so it can be shared between the on-chain program and
<a href=#71 id=71 data-nosnippet>71</a>//! // the client.
<a href=#72 id=72 data-nosnippet>72</a>//! #[derive(BorshSerialize, BorshDeserialize)]
<a href=#73 id=73 data-nosnippet>73</a>//! enum BankInstruction {
<a href=#74 id=74 data-nosnippet>74</a>//!     Initialize,
<a href=#75 id=75 data-nosnippet>75</a>//!     Deposit { lamports: u64 },
<a href=#76 id=76 data-nosnippet>76</a>//!     Withdraw { lamports: u64 },
<a href=#77 id=77 data-nosnippet>77</a>//! }
<a href=#78 id=78 data-nosnippet>78</a>//!
<a href=#79 id=79 data-nosnippet>79</a>//! fn send_initialize_tx(
<a href=#80 id=80 data-nosnippet>80</a>//!     client: &amp;RpcClient,
<a href=#81 id=81 data-nosnippet>81</a>//!     program_id: Pubkey,
<a href=#82 id=82 data-nosnippet>82</a>//!     payer: &amp;Keypair
<a href=#83 id=83 data-nosnippet>83</a>//! ) -&gt; Result&lt;()&gt; {
<a href=#84 id=84 data-nosnippet>84</a>//!
<a href=#85 id=85 data-nosnippet>85</a>//!     let bank_instruction = BankInstruction::Initialize;
<a href=#86 id=86 data-nosnippet>86</a>//!
<a href=#87 id=87 data-nosnippet>87</a>//!     let instruction = Instruction::new_with_borsh(
<a href=#88 id=88 data-nosnippet>88</a>//!         program_id,
<a href=#89 id=89 data-nosnippet>89</a>//!         &amp;bank_instruction,
<a href=#90 id=90 data-nosnippet>90</a>//!         vec![],
<a href=#91 id=91 data-nosnippet>91</a>//!     );
<a href=#92 id=92 data-nosnippet>92</a>//!
<a href=#93 id=93 data-nosnippet>93</a>//!     let blockhash = client.get_latest_blockhash()?;
<a href=#94 id=94 data-nosnippet>94</a>//!     let mut tx = Transaction::new_signed_with_payer(
<a href=#95 id=95 data-nosnippet>95</a>//!         &amp;[instruction],
<a href=#96 id=96 data-nosnippet>96</a>//!         Some(&amp;payer.pubkey()),
<a href=#97 id=97 data-nosnippet>97</a>//!         &amp;[payer],
<a href=#98 id=98 data-nosnippet>98</a>//!         blockhash,
<a href=#99 id=99 data-nosnippet>99</a>//!     );
<a href=#100 id=100 data-nosnippet>100</a>//!     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#101 id=101 data-nosnippet>101</a>//!
<a href=#102 id=102 data-nosnippet>102</a>//!     Ok(())
<a href=#103 id=103 data-nosnippet>103</a>//! }
<a href=#104 id=104 data-nosnippet>104</a>//! #
<a href=#105 id=105 data-nosnippet>105</a>//! # let client = RpcClient::new(String::new());
<a href=#106 id=106 data-nosnippet>106</a>//! # let program_id = Pubkey::new_unique();
<a href=#107 id=107 data-nosnippet>107</a>//! # let payer = Keypair::new();
<a href=#108 id=108 data-nosnippet>108</a>//! # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#109 id=109 data-nosnippet>109</a>//! #
<a href=#110 id=110 data-nosnippet>110</a>//! # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#111 id=111 data-nosnippet>111</a>//! ```
<a href=#112 id=112 data-nosnippet>112</a>
<a href=#113 id=113 data-nosnippet>113</a></span><span class="attr">#[cfg(target_arch = <span class="string">"wasm32"</span>)]
<a href=#114 id=114 data-nosnippet>114</a></span><span class="kw">use </span>wasm_bindgen::prelude::wasm_bindgen;
<a href=#115 id=115 data-nosnippet>115</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#116 id=116 data-nosnippet>116</a></span><span class="kw">use </span>{
<a href=#117 id=117 data-nosnippet>117</a>    serde_derive::{Deserialize, Serialize},
<a href=#118 id=118 data-nosnippet>118</a>    solana_short_vec <span class="kw">as </span>short_vec,
<a href=#119 id=119 data-nosnippet>119</a>};
<a href=#120 id=120 data-nosnippet>120</a><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#121 id=121 data-nosnippet>121</a></span><span class="kw">use </span>{
<a href=#122 id=122 data-nosnippet>122</a>    solana_bincode::limited_deserialize,
<a href=#123 id=123 data-nosnippet>123</a>    solana_hash::Hash,
<a href=#124 id=124 data-nosnippet>124</a>    solana_message::compiled_instruction::CompiledInstruction,
<a href=#125 id=125 data-nosnippet>125</a>    solana_sdk_ids::system_program,
<a href=#126 id=126 data-nosnippet>126</a>    solana_signer::{signers::Signers, SignerError},
<a href=#127 id=127 data-nosnippet>127</a>    solana_system_interface::instruction::SystemInstruction,
<a href=#128 id=128 data-nosnippet>128</a>};
<a href=#129 id=129 data-nosnippet>129</a><span class="kw">use </span>{
<a href=#130 id=130 data-nosnippet>130</a>    solana_instruction::Instruction,
<a href=#131 id=131 data-nosnippet>131</a>    solana_message::Message,
<a href=#132 id=132 data-nosnippet>132</a>    solana_pubkey::Pubkey,
<a href=#133 id=133 data-nosnippet>133</a>    solana_sanitize::{Sanitize, SanitizeError},
<a href=#134 id=134 data-nosnippet>134</a>    solana_signature::Signature,
<a href=#135 id=135 data-nosnippet>135</a>    solana_transaction_error::{TransactionError, TransactionResult <span class="kw">as </span><span class="prelude-ty">Result</span>},
<a href=#136 id=136 data-nosnippet>136</a>    std::result,
<a href=#137 id=137 data-nosnippet>137</a>};
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a><span class="kw">pub mod </span>sanitized;
<a href=#140 id=140 data-nosnippet>140</a><span class="kw">pub mod </span>simple_vote_transaction_checker;
<a href=#141 id=141 data-nosnippet>141</a><span class="kw">pub mod </span>versioned;
<a href=#142 id=142 data-nosnippet>142</a><span class="kw">mod </span>wasm;
<a href=#143 id=143 data-nosnippet>143</a>
<a href=#144 id=144 data-nosnippet>144</a><span class="attr">#[derive(PartialEq, Eq, Clone, Copy, Debug)]
<a href=#145 id=145 data-nosnippet>145</a></span><span class="kw">pub enum </span>TransactionVerificationMode {
<a href=#146 id=146 data-nosnippet>146</a>    HashOnly,
<a href=#147 id=147 data-nosnippet>147</a>    HashAndVerifyPrecompiles,
<a href=#148 id=148 data-nosnippet>148</a>    FullVerification,
<a href=#149 id=149 data-nosnippet>149</a>}
<a href=#150 id=150 data-nosnippet>150</a>
<a href=#151 id=151 data-nosnippet>151</a><span class="comment">// inlined to avoid solana-nonce dep
<a href=#152 id=152 data-nosnippet>152</a></span><span class="attr">#[cfg(test)]
<a href=#153 id=153 data-nosnippet>153</a></span><span class="macro">static_assertions::const_assert_eq!</span>(
<a href=#154 id=154 data-nosnippet>154</a>    NONCED_TX_MARKER_IX_INDEX,
<a href=#155 id=155 data-nosnippet>155</a>    solana_nonce::NONCED_TX_MARKER_IX_INDEX
<a href=#156 id=156 data-nosnippet>156</a>);
<a href=#157 id=157 data-nosnippet>157</a><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#158 id=158 data-nosnippet>158</a></span><span class="kw">const </span>NONCED_TX_MARKER_IX_INDEX: u8 = <span class="number">0</span>;
<a href=#159 id=159 data-nosnippet>159</a><span class="comment">// inlined to avoid solana-packet dep
<a href=#160 id=160 data-nosnippet>160</a></span><span class="attr">#[cfg(test)]
<a href=#161 id=161 data-nosnippet>161</a></span><span class="macro">static_assertions::const_assert_eq!</span>(PACKET_DATA_SIZE, solana_packet::PACKET_DATA_SIZE);
<a href=#162 id=162 data-nosnippet>162</a><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#163 id=163 data-nosnippet>163</a></span><span class="kw">const </span>PACKET_DATA_SIZE: usize = <span class="number">1280 </span>- <span class="number">40 </span>- <span class="number">8</span>;
<a href=#164 id=164 data-nosnippet>164</a>
<a href=#165 id=165 data-nosnippet>165</a><span class="doccomment">/// An atomically-committed sequence of instructions.
<a href=#166 id=166 data-nosnippet>166</a>///
<a href=#167 id=167 data-nosnippet>167</a>/// While [`Instruction`]s are the basic unit of computation in Solana,
<a href=#168 id=168 data-nosnippet>168</a>/// they are submitted by clients in [`Transaction`]s containing one or
<a href=#169 id=169 data-nosnippet>169</a>/// more instructions, and signed by one or more [`Signer`]s.
<a href=#170 id=170 data-nosnippet>170</a>///
<a href=#171 id=171 data-nosnippet>171</a>/// [`Signer`]: https://docs.rs/solana-signer/latest/solana_signer/trait.Signer.html
<a href=#172 id=172 data-nosnippet>172</a>///
<a href=#173 id=173 data-nosnippet>173</a>/// See the [module documentation] for more details about transactions.
<a href=#174 id=174 data-nosnippet>174</a>///
<a href=#175 id=175 data-nosnippet>175</a>/// [module documentation]: self
<a href=#176 id=176 data-nosnippet>176</a>///
<a href=#177 id=177 data-nosnippet>177</a>/// Some constructors accept an optional `payer`, the account responsible for
<a href=#178 id=178 data-nosnippet>178</a>/// paying the cost of executing a transaction. In most cases, callers should
<a href=#179 id=179 data-nosnippet>179</a>/// specify the payer explicitly in these constructors. In some cases though,
<a href=#180 id=180 data-nosnippet>180</a>/// the caller is not _required_ to specify the payer, but is still allowed to:
<a href=#181 id=181 data-nosnippet>181</a>/// in the [`Message`] structure, the first account is always the fee-payer, so
<a href=#182 id=182 data-nosnippet>182</a>/// if the caller has knowledge that the first account of the constructed
<a href=#183 id=183 data-nosnippet>183</a>/// transaction's `Message` is both a signer and the expected fee-payer, then
<a href=#184 id=184 data-nosnippet>184</a>/// redundantly specifying the fee-payer is not strictly required.
<a href=#185 id=185 data-nosnippet>185</a></span><span class="attr">#[cfg(not(target_arch = <span class="string">"wasm32"</span>))]
<a href=#186 id=186 data-nosnippet>186</a>#[cfg_attr(
<a href=#187 id=187 data-nosnippet>187</a>    feature = <span class="string">"frozen-abi"</span>,
<a href=#188 id=188 data-nosnippet>188</a>    derive(solana_frozen_abi_macro::AbiExample),
<a href=#189 id=189 data-nosnippet>189</a>    solana_frozen_abi_macro::frozen_abi(digest = <span class="string">"76BDTr3Xm3VP7h4eSiw6pZHKc5yYewDufyia3Yedh6GG"</span>)
<a href=#190 id=190 data-nosnippet>190</a>)]
<a href=#191 id=191 data-nosnippet>191</a>#[cfg_attr(feature = <span class="string">"serde"</span>, derive(Deserialize, Serialize))]
<a href=#192 id=192 data-nosnippet>192</a>#[derive(Debug, PartialEq, Default, Eq, Clone)]
<a href=#193 id=193 data-nosnippet>193</a></span><span class="kw">pub struct </span>Transaction {
<a href=#194 id=194 data-nosnippet>194</a>    <span class="doccomment">/// A set of signatures of a serialized [`Message`], signed by the first
<a href=#195 id=195 data-nosnippet>195</a>    /// keys of the `Message`'s [`account_keys`], where the number of signatures
<a href=#196 id=196 data-nosnippet>196</a>    /// is equal to [`num_required_signatures`] of the `Message`'s
<a href=#197 id=197 data-nosnippet>197</a>    /// [`MessageHeader`].
<a href=#198 id=198 data-nosnippet>198</a>    ///
<a href=#199 id=199 data-nosnippet>199</a>    /// [`account_keys`]: https://docs.rs/solana-message/latest/solana_message/legacy/struct.Message.html#structfield.account_keys
<a href=#200 id=200 data-nosnippet>200</a>    /// [`MessageHeader`]: https://docs.rs/solana-message/latest/solana_message/struct.MessageHeader.html
<a href=#201 id=201 data-nosnippet>201</a>    /// [`num_required_signatures`]: https://docs.rs/solana-message/latest/solana_message/struct.MessageHeader.html#structfield.num_required_signatures
<a href=#202 id=202 data-nosnippet>202</a>    </span><span class="comment">// NOTE: Serialization-related changes must be paired with the direct read at sigverify.
<a href=#203 id=203 data-nosnippet>203</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"short_vec"</span>))]
<a href=#204 id=204 data-nosnippet>204</a>    </span><span class="kw">pub </span>signatures: Vec&lt;Signature&gt;,
<a href=#205 id=205 data-nosnippet>205</a>
<a href=#206 id=206 data-nosnippet>206</a>    <span class="doccomment">/// The message to sign.
<a href=#207 id=207 data-nosnippet>207</a>    </span><span class="kw">pub </span>message: Message,
<a href=#208 id=208 data-nosnippet>208</a>}
<a href=#209 id=209 data-nosnippet>209</a>
<a href=#210 id=210 data-nosnippet>210</a><span class="doccomment">/// wasm-bindgen version of the Transaction struct.
<a href=#211 id=211 data-nosnippet>211</a>/// This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
<a href=#212 id=212 data-nosnippet>212</a>/// is fixed. This must not diverge from the regular non-wasm Transaction struct.
<a href=#213 id=213 data-nosnippet>213</a></span><span class="attr">#[cfg(target_arch = <span class="string">"wasm32"</span>)]
<a href=#214 id=214 data-nosnippet>214</a>#[wasm_bindgen]
<a href=#215 id=215 data-nosnippet>215</a>#[cfg_attr(
<a href=#216 id=216 data-nosnippet>216</a>    feature = <span class="string">"frozen-abi"</span>,
<a href=#217 id=217 data-nosnippet>217</a>    derive(AbiExample),
<a href=#218 id=218 data-nosnippet>218</a>    frozen_abi(digest = <span class="string">"H7xQFcd1MtMv9QKZWGatBAXwhg28tpeX59P3s8ZZLAY4"</span>)
<a href=#219 id=219 data-nosnippet>219</a>)]
<a href=#220 id=220 data-nosnippet>220</a>#[cfg_attr(feature = <span class="string">"serde"</span>, derive(Deserialize, Serialize))]
<a href=#221 id=221 data-nosnippet>221</a>#[derive(Debug, PartialEq, Default, Eq, Clone)]
<a href=#222 id=222 data-nosnippet>222</a></span><span class="kw">pub struct </span>Transaction {
<a href=#223 id=223 data-nosnippet>223</a>    <span class="attr">#[wasm_bindgen(skip)]
<a href=#224 id=224 data-nosnippet>224</a>    #[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"short_vec"</span>))]
<a href=#225 id=225 data-nosnippet>225</a>    </span><span class="kw">pub </span>signatures: Vec&lt;Signature&gt;,
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>    <span class="attr">#[wasm_bindgen(skip)]
<a href=#228 id=228 data-nosnippet>228</a>    </span><span class="kw">pub </span>message: Message,
<a href=#229 id=229 data-nosnippet>229</a>}
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a><span class="kw">impl </span>Sanitize <span class="kw">for </span>Transaction {
<a href=#232 id=232 data-nosnippet>232</a>    <span class="kw">fn </span>sanitize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; result::Result&lt;(), SanitizeError&gt; {
<a href=#233 id=233 data-nosnippet>233</a>        <span class="kw">if </span><span class="self">self</span>.message.header.num_required_signatures <span class="kw">as </span>usize &gt; <span class="self">self</span>.signatures.len() {
<a href=#234 id=234 data-nosnippet>234</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#235 id=235 data-nosnippet>235</a>        }
<a href=#236 id=236 data-nosnippet>236</a>        <span class="kw">if </span><span class="self">self</span>.signatures.len() &gt; <span class="self">self</span>.message.account_keys.len() {
<a href=#237 id=237 data-nosnippet>237</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#238 id=238 data-nosnippet>238</a>        }
<a href=#239 id=239 data-nosnippet>239</a>        <span class="self">self</span>.message.sanitize()
<a href=#240 id=240 data-nosnippet>240</a>    }
<a href=#241 id=241 data-nosnippet>241</a>}
<a href=#242 id=242 data-nosnippet>242</a>
<a href=#243 id=243 data-nosnippet>243</a><span class="kw">impl </span>Transaction {
<a href=#244 id=244 data-nosnippet>244</a>    <span class="doccomment">/// Create an unsigned transaction from a [`Message`].
<a href=#245 id=245 data-nosnippet>245</a>    ///
<a href=#246 id=246 data-nosnippet>246</a>    /// # Examples
<a href=#247 id=247 data-nosnippet>247</a>    ///
<a href=#248 id=248 data-nosnippet>248</a>    /// This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#249 id=249 data-nosnippet>249</a>    ///
<a href=#250 id=250 data-nosnippet>250</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#251 id=251 data-nosnippet>251</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#252 id=252 data-nosnippet>252</a>    ///
<a href=#253 id=253 data-nosnippet>253</a>    /// ```
<a href=#254 id=254 data-nosnippet>254</a>    /// # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#255 id=255 data-nosnippet>255</a>    /// use anyhow::Result;
<a href=#256 id=256 data-nosnippet>256</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#257 id=257 data-nosnippet>257</a>    /// use solana_instruction::Instruction;
<a href=#258 id=258 data-nosnippet>258</a>    /// use solana_keypair::Keypair;
<a href=#259 id=259 data-nosnippet>259</a>    /// use solana_message::Message;
<a href=#260 id=260 data-nosnippet>260</a>    /// use solana_pubkey::Pubkey;
<a href=#261 id=261 data-nosnippet>261</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#262 id=262 data-nosnippet>262</a>    /// use solana_signer::Signer;
<a href=#263 id=263 data-nosnippet>263</a>    /// use solana_transaction::Transaction;
<a href=#264 id=264 data-nosnippet>264</a>    ///
<a href=#265 id=265 data-nosnippet>265</a>    /// // A custom program instruction. This would typically be defined in
<a href=#266 id=266 data-nosnippet>266</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#267 id=267 data-nosnippet>267</a>    /// // the client.
<a href=#268 id=268 data-nosnippet>268</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#269 id=269 data-nosnippet>269</a>    /// enum BankInstruction {
<a href=#270 id=270 data-nosnippet>270</a>    ///     Initialize,
<a href=#271 id=271 data-nosnippet>271</a>    ///     Deposit { lamports: u64 },
<a href=#272 id=272 data-nosnippet>272</a>    ///     Withdraw { lamports: u64 },
<a href=#273 id=273 data-nosnippet>273</a>    /// }
<a href=#274 id=274 data-nosnippet>274</a>    ///
<a href=#275 id=275 data-nosnippet>275</a>    /// fn send_initialize_tx(
<a href=#276 id=276 data-nosnippet>276</a>    ///     client: &amp;RpcClient,
<a href=#277 id=277 data-nosnippet>277</a>    ///     program_id: Pubkey,
<a href=#278 id=278 data-nosnippet>278</a>    ///     payer: &amp;Keypair
<a href=#279 id=279 data-nosnippet>279</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#280 id=280 data-nosnippet>280</a>    ///
<a href=#281 id=281 data-nosnippet>281</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#282 id=282 data-nosnippet>282</a>    ///
<a href=#283 id=283 data-nosnippet>283</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#284 id=284 data-nosnippet>284</a>    ///         program_id,
<a href=#285 id=285 data-nosnippet>285</a>    ///         &amp;bank_instruction,
<a href=#286 id=286 data-nosnippet>286</a>    ///         vec![],
<a href=#287 id=287 data-nosnippet>287</a>    ///     );
<a href=#288 id=288 data-nosnippet>288</a>    ///
<a href=#289 id=289 data-nosnippet>289</a>    ///     let message = Message::new(
<a href=#290 id=290 data-nosnippet>290</a>    ///         &amp;[instruction],
<a href=#291 id=291 data-nosnippet>291</a>    ///         Some(&amp;payer.pubkey()),
<a href=#292 id=292 data-nosnippet>292</a>    ///     );
<a href=#293 id=293 data-nosnippet>293</a>    ///
<a href=#294 id=294 data-nosnippet>294</a>    ///     let mut tx = Transaction::new_unsigned(message);
<a href=#295 id=295 data-nosnippet>295</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#296 id=296 data-nosnippet>296</a>    ///     tx.sign(&amp;[payer], blockhash);
<a href=#297 id=297 data-nosnippet>297</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#298 id=298 data-nosnippet>298</a>    ///
<a href=#299 id=299 data-nosnippet>299</a>    ///     Ok(())
<a href=#300 id=300 data-nosnippet>300</a>    /// }
<a href=#301 id=301 data-nosnippet>301</a>    /// #
<a href=#302 id=302 data-nosnippet>302</a>    /// # let client = RpcClient::new(String::new());
<a href=#303 id=303 data-nosnippet>303</a>    /// # let program_id = Pubkey::new_unique();
<a href=#304 id=304 data-nosnippet>304</a>    /// # let payer = Keypair::new();
<a href=#305 id=305 data-nosnippet>305</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#306 id=306 data-nosnippet>306</a>    /// #
<a href=#307 id=307 data-nosnippet>307</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#308 id=308 data-nosnippet>308</a>    /// ```
<a href=#309 id=309 data-nosnippet>309</a>    </span><span class="kw">pub fn </span>new_unsigned(message: Message) -&gt; <span class="self">Self </span>{
<a href=#310 id=310 data-nosnippet>310</a>        <span class="self">Self </span>{
<a href=#311 id=311 data-nosnippet>311</a>            signatures: <span class="macro">vec!</span>[Signature::default(); message.header.num_required_signatures <span class="kw">as </span>usize],
<a href=#312 id=312 data-nosnippet>312</a>            message,
<a href=#313 id=313 data-nosnippet>313</a>        }
<a href=#314 id=314 data-nosnippet>314</a>    }
<a href=#315 id=315 data-nosnippet>315</a>
<a href=#316 id=316 data-nosnippet>316</a>    <span class="doccomment">/// Create a fully-signed transaction from a [`Message`].
<a href=#317 id=317 data-nosnippet>317</a>    ///
<a href=#318 id=318 data-nosnippet>318</a>    /// # Panics
<a href=#319 id=319 data-nosnippet>319</a>    ///
<a href=#320 id=320 data-nosnippet>320</a>    /// Panics when signing fails. See [`Transaction::try_sign`] and
<a href=#321 id=321 data-nosnippet>321</a>    /// [`Transaction::try_partial_sign`] for a full description of failure
<a href=#322 id=322 data-nosnippet>322</a>    /// scenarios.
<a href=#323 id=323 data-nosnippet>323</a>    ///
<a href=#324 id=324 data-nosnippet>324</a>    /// # Examples
<a href=#325 id=325 data-nosnippet>325</a>    ///
<a href=#326 id=326 data-nosnippet>326</a>    /// This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#327 id=327 data-nosnippet>327</a>    ///
<a href=#328 id=328 data-nosnippet>328</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#329 id=329 data-nosnippet>329</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#330 id=330 data-nosnippet>330</a>    ///
<a href=#331 id=331 data-nosnippet>331</a>    /// ```
<a href=#332 id=332 data-nosnippet>332</a>    /// # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#333 id=333 data-nosnippet>333</a>    /// use anyhow::Result;
<a href=#334 id=334 data-nosnippet>334</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#335 id=335 data-nosnippet>335</a>    /// use solana_instruction::Instruction;
<a href=#336 id=336 data-nosnippet>336</a>    /// use solana_keypair::Keypair;
<a href=#337 id=337 data-nosnippet>337</a>    /// use solana_message::Message;
<a href=#338 id=338 data-nosnippet>338</a>    /// use solana_pubkey::Pubkey;
<a href=#339 id=339 data-nosnippet>339</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#340 id=340 data-nosnippet>340</a>    /// use solana_signer::Signer;
<a href=#341 id=341 data-nosnippet>341</a>    /// use solana_transaction::Transaction;
<a href=#342 id=342 data-nosnippet>342</a>    ///
<a href=#343 id=343 data-nosnippet>343</a>    /// // A custom program instruction. This would typically be defined in
<a href=#344 id=344 data-nosnippet>344</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#345 id=345 data-nosnippet>345</a>    /// // the client.
<a href=#346 id=346 data-nosnippet>346</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#347 id=347 data-nosnippet>347</a>    /// enum BankInstruction {
<a href=#348 id=348 data-nosnippet>348</a>    ///     Initialize,
<a href=#349 id=349 data-nosnippet>349</a>    ///     Deposit { lamports: u64 },
<a href=#350 id=350 data-nosnippet>350</a>    ///     Withdraw { lamports: u64 },
<a href=#351 id=351 data-nosnippet>351</a>    /// }
<a href=#352 id=352 data-nosnippet>352</a>    ///
<a href=#353 id=353 data-nosnippet>353</a>    /// fn send_initialize_tx(
<a href=#354 id=354 data-nosnippet>354</a>    ///     client: &amp;RpcClient,
<a href=#355 id=355 data-nosnippet>355</a>    ///     program_id: Pubkey,
<a href=#356 id=356 data-nosnippet>356</a>    ///     payer: &amp;Keypair
<a href=#357 id=357 data-nosnippet>357</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#358 id=358 data-nosnippet>358</a>    ///
<a href=#359 id=359 data-nosnippet>359</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#360 id=360 data-nosnippet>360</a>    ///
<a href=#361 id=361 data-nosnippet>361</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#362 id=362 data-nosnippet>362</a>    ///         program_id,
<a href=#363 id=363 data-nosnippet>363</a>    ///         &amp;bank_instruction,
<a href=#364 id=364 data-nosnippet>364</a>    ///         vec![],
<a href=#365 id=365 data-nosnippet>365</a>    ///     );
<a href=#366 id=366 data-nosnippet>366</a>    ///
<a href=#367 id=367 data-nosnippet>367</a>    ///     let message = Message::new(
<a href=#368 id=368 data-nosnippet>368</a>    ///         &amp;[instruction],
<a href=#369 id=369 data-nosnippet>369</a>    ///         Some(&amp;payer.pubkey()),
<a href=#370 id=370 data-nosnippet>370</a>    ///     );
<a href=#371 id=371 data-nosnippet>371</a>    ///
<a href=#372 id=372 data-nosnippet>372</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#373 id=373 data-nosnippet>373</a>    ///     let mut tx = Transaction::new(&amp;[payer], message, blockhash);
<a href=#374 id=374 data-nosnippet>374</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#375 id=375 data-nosnippet>375</a>    ///
<a href=#376 id=376 data-nosnippet>376</a>    ///     Ok(())
<a href=#377 id=377 data-nosnippet>377</a>    /// }
<a href=#378 id=378 data-nosnippet>378</a>    /// #
<a href=#379 id=379 data-nosnippet>379</a>    /// # let client = RpcClient::new(String::new());
<a href=#380 id=380 data-nosnippet>380</a>    /// # let program_id = Pubkey::new_unique();
<a href=#381 id=381 data-nosnippet>381</a>    /// # let payer = Keypair::new();
<a href=#382 id=382 data-nosnippet>382</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#383 id=383 data-nosnippet>383</a>    /// #
<a href=#384 id=384 data-nosnippet>384</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#385 id=385 data-nosnippet>385</a>    /// ```
<a href=#386 id=386 data-nosnippet>386</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#387 id=387 data-nosnippet>387</a>    </span><span class="kw">pub fn </span>new&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#388 id=388 data-nosnippet>388</a>        from_keypairs: <span class="kw-2">&amp;</span>T,
<a href=#389 id=389 data-nosnippet>389</a>        message: Message,
<a href=#390 id=390 data-nosnippet>390</a>        recent_blockhash: Hash,
<a href=#391 id=391 data-nosnippet>391</a>    ) -&gt; Transaction {
<a href=#392 id=392 data-nosnippet>392</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = <span class="self">Self</span>::new_unsigned(message);
<a href=#393 id=393 data-nosnippet>393</a>        tx.sign(from_keypairs, recent_blockhash);
<a href=#394 id=394 data-nosnippet>394</a>        tx
<a href=#395 id=395 data-nosnippet>395</a>    }
<a href=#396 id=396 data-nosnippet>396</a>
<a href=#397 id=397 data-nosnippet>397</a>    <span class="doccomment">/// Create an unsigned transaction from a list of [`Instruction`]s.
<a href=#398 id=398 data-nosnippet>398</a>    ///
<a href=#399 id=399 data-nosnippet>399</a>    /// `payer` is the account responsible for paying the cost of executing the
<a href=#400 id=400 data-nosnippet>400</a>    /// transaction. It is typically provided, but is optional in some cases.
<a href=#401 id=401 data-nosnippet>401</a>    /// See the [`Transaction`] docs for more.
<a href=#402 id=402 data-nosnippet>402</a>    ///
<a href=#403 id=403 data-nosnippet>403</a>    /// # Examples
<a href=#404 id=404 data-nosnippet>404</a>    ///
<a href=#405 id=405 data-nosnippet>405</a>    /// This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#406 id=406 data-nosnippet>406</a>    ///
<a href=#407 id=407 data-nosnippet>407</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#408 id=408 data-nosnippet>408</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#409 id=409 data-nosnippet>409</a>    ///
<a href=#410 id=410 data-nosnippet>410</a>    /// ```
<a href=#411 id=411 data-nosnippet>411</a>    /// # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#412 id=412 data-nosnippet>412</a>    /// use anyhow::Result;
<a href=#413 id=413 data-nosnippet>413</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#414 id=414 data-nosnippet>414</a>    /// use solana_instruction::Instruction;
<a href=#415 id=415 data-nosnippet>415</a>    /// use solana_keypair::Keypair;
<a href=#416 id=416 data-nosnippet>416</a>    /// use solana_message::Message;
<a href=#417 id=417 data-nosnippet>417</a>    /// use solana_pubkey::Pubkey;
<a href=#418 id=418 data-nosnippet>418</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#419 id=419 data-nosnippet>419</a>    /// use solana_signer::Signer;
<a href=#420 id=420 data-nosnippet>420</a>    /// use solana_transaction::Transaction;
<a href=#421 id=421 data-nosnippet>421</a>    ///
<a href=#422 id=422 data-nosnippet>422</a>    /// // A custom program instruction. This would typically be defined in
<a href=#423 id=423 data-nosnippet>423</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#424 id=424 data-nosnippet>424</a>    /// // the client.
<a href=#425 id=425 data-nosnippet>425</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#426 id=426 data-nosnippet>426</a>    /// enum BankInstruction {
<a href=#427 id=427 data-nosnippet>427</a>    ///     Initialize,
<a href=#428 id=428 data-nosnippet>428</a>    ///     Deposit { lamports: u64 },
<a href=#429 id=429 data-nosnippet>429</a>    ///     Withdraw { lamports: u64 },
<a href=#430 id=430 data-nosnippet>430</a>    /// }
<a href=#431 id=431 data-nosnippet>431</a>    ///
<a href=#432 id=432 data-nosnippet>432</a>    /// fn send_initialize_tx(
<a href=#433 id=433 data-nosnippet>433</a>    ///     client: &amp;RpcClient,
<a href=#434 id=434 data-nosnippet>434</a>    ///     program_id: Pubkey,
<a href=#435 id=435 data-nosnippet>435</a>    ///     payer: &amp;Keypair
<a href=#436 id=436 data-nosnippet>436</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#437 id=437 data-nosnippet>437</a>    ///
<a href=#438 id=438 data-nosnippet>438</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#439 id=439 data-nosnippet>439</a>    ///
<a href=#440 id=440 data-nosnippet>440</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#441 id=441 data-nosnippet>441</a>    ///         program_id,
<a href=#442 id=442 data-nosnippet>442</a>    ///         &amp;bank_instruction,
<a href=#443 id=443 data-nosnippet>443</a>    ///         vec![],
<a href=#444 id=444 data-nosnippet>444</a>    ///     );
<a href=#445 id=445 data-nosnippet>445</a>    ///
<a href=#446 id=446 data-nosnippet>446</a>    ///     let mut tx = Transaction::new_with_payer(&amp;[instruction], Some(&amp;payer.pubkey()));
<a href=#447 id=447 data-nosnippet>447</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#448 id=448 data-nosnippet>448</a>    ///     tx.sign(&amp;[payer], blockhash);
<a href=#449 id=449 data-nosnippet>449</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#450 id=450 data-nosnippet>450</a>    ///
<a href=#451 id=451 data-nosnippet>451</a>    ///     Ok(())
<a href=#452 id=452 data-nosnippet>452</a>    /// }
<a href=#453 id=453 data-nosnippet>453</a>    /// #
<a href=#454 id=454 data-nosnippet>454</a>    /// # let client = RpcClient::new(String::new());
<a href=#455 id=455 data-nosnippet>455</a>    /// # let program_id = Pubkey::new_unique();
<a href=#456 id=456 data-nosnippet>456</a>    /// # let payer = Keypair::new();
<a href=#457 id=457 data-nosnippet>457</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#458 id=458 data-nosnippet>458</a>    /// #
<a href=#459 id=459 data-nosnippet>459</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#460 id=460 data-nosnippet>460</a>    /// ```
<a href=#461 id=461 data-nosnippet>461</a>    </span><span class="kw">pub fn </span>new_with_payer(instructions: <span class="kw-2">&amp;</span>[Instruction], payer: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt;) -&gt; <span class="self">Self </span>{
<a href=#462 id=462 data-nosnippet>462</a>        <span class="kw">let </span>message = Message::new(instructions, payer);
<a href=#463 id=463 data-nosnippet>463</a>        <span class="self">Self</span>::new_unsigned(message)
<a href=#464 id=464 data-nosnippet>464</a>    }
<a href=#465 id=465 data-nosnippet>465</a>
<a href=#466 id=466 data-nosnippet>466</a>    <span class="doccomment">/// Create a fully-signed transaction from a list of [`Instruction`]s.
<a href=#467 id=467 data-nosnippet>467</a>    ///
<a href=#468 id=468 data-nosnippet>468</a>    /// `payer` is the account responsible for paying the cost of executing the
<a href=#469 id=469 data-nosnippet>469</a>    /// transaction. It is typically provided, but is optional in some cases.
<a href=#470 id=470 data-nosnippet>470</a>    /// See the [`Transaction`] docs for more.
<a href=#471 id=471 data-nosnippet>471</a>    ///
<a href=#472 id=472 data-nosnippet>472</a>    /// # Panics
<a href=#473 id=473 data-nosnippet>473</a>    ///
<a href=#474 id=474 data-nosnippet>474</a>    /// Panics when signing fails. See [`Transaction::try_sign`] and
<a href=#475 id=475 data-nosnippet>475</a>    /// [`Transaction::try_partial_sign`] for a full description of failure
<a href=#476 id=476 data-nosnippet>476</a>    /// scenarios.
<a href=#477 id=477 data-nosnippet>477</a>    ///
<a href=#478 id=478 data-nosnippet>478</a>    /// # Examples
<a href=#479 id=479 data-nosnippet>479</a>    ///
<a href=#480 id=480 data-nosnippet>480</a>    /// This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#481 id=481 data-nosnippet>481</a>    ///
<a href=#482 id=482 data-nosnippet>482</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#483 id=483 data-nosnippet>483</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#484 id=484 data-nosnippet>484</a>    ///
<a href=#485 id=485 data-nosnippet>485</a>    /// ```
<a href=#486 id=486 data-nosnippet>486</a>    /// # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#487 id=487 data-nosnippet>487</a>    /// use anyhow::Result;
<a href=#488 id=488 data-nosnippet>488</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#489 id=489 data-nosnippet>489</a>    /// use solana_instruction::Instruction;
<a href=#490 id=490 data-nosnippet>490</a>    /// use solana_keypair::Keypair;
<a href=#491 id=491 data-nosnippet>491</a>    /// use solana_message::Message;
<a href=#492 id=492 data-nosnippet>492</a>    /// use solana_pubkey::Pubkey;
<a href=#493 id=493 data-nosnippet>493</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#494 id=494 data-nosnippet>494</a>    /// use solana_signer::Signer;
<a href=#495 id=495 data-nosnippet>495</a>    /// use solana_transaction::Transaction;
<a href=#496 id=496 data-nosnippet>496</a>    ///
<a href=#497 id=497 data-nosnippet>497</a>    /// // A custom program instruction. This would typically be defined in
<a href=#498 id=498 data-nosnippet>498</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#499 id=499 data-nosnippet>499</a>    /// // the client.
<a href=#500 id=500 data-nosnippet>500</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#501 id=501 data-nosnippet>501</a>    /// enum BankInstruction {
<a href=#502 id=502 data-nosnippet>502</a>    ///     Initialize,
<a href=#503 id=503 data-nosnippet>503</a>    ///     Deposit { lamports: u64 },
<a href=#504 id=504 data-nosnippet>504</a>    ///     Withdraw { lamports: u64 },
<a href=#505 id=505 data-nosnippet>505</a>    /// }
<a href=#506 id=506 data-nosnippet>506</a>    ///
<a href=#507 id=507 data-nosnippet>507</a>    /// fn send_initialize_tx(
<a href=#508 id=508 data-nosnippet>508</a>    ///     client: &amp;RpcClient,
<a href=#509 id=509 data-nosnippet>509</a>    ///     program_id: Pubkey,
<a href=#510 id=510 data-nosnippet>510</a>    ///     payer: &amp;Keypair
<a href=#511 id=511 data-nosnippet>511</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#512 id=512 data-nosnippet>512</a>    ///
<a href=#513 id=513 data-nosnippet>513</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#514 id=514 data-nosnippet>514</a>    ///
<a href=#515 id=515 data-nosnippet>515</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#516 id=516 data-nosnippet>516</a>    ///         program_id,
<a href=#517 id=517 data-nosnippet>517</a>    ///         &amp;bank_instruction,
<a href=#518 id=518 data-nosnippet>518</a>    ///         vec![],
<a href=#519 id=519 data-nosnippet>519</a>    ///     );
<a href=#520 id=520 data-nosnippet>520</a>    ///
<a href=#521 id=521 data-nosnippet>521</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#522 id=522 data-nosnippet>522</a>    ///     let mut tx = Transaction::new_signed_with_payer(
<a href=#523 id=523 data-nosnippet>523</a>    ///         &amp;[instruction],
<a href=#524 id=524 data-nosnippet>524</a>    ///         Some(&amp;payer.pubkey()),
<a href=#525 id=525 data-nosnippet>525</a>    ///         &amp;[payer],
<a href=#526 id=526 data-nosnippet>526</a>    ///         blockhash,
<a href=#527 id=527 data-nosnippet>527</a>    ///     );
<a href=#528 id=528 data-nosnippet>528</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#529 id=529 data-nosnippet>529</a>    ///
<a href=#530 id=530 data-nosnippet>530</a>    ///     Ok(())
<a href=#531 id=531 data-nosnippet>531</a>    /// }
<a href=#532 id=532 data-nosnippet>532</a>    /// #
<a href=#533 id=533 data-nosnippet>533</a>    /// # let client = RpcClient::new(String::new());
<a href=#534 id=534 data-nosnippet>534</a>    /// # let program_id = Pubkey::new_unique();
<a href=#535 id=535 data-nosnippet>535</a>    /// # let payer = Keypair::new();
<a href=#536 id=536 data-nosnippet>536</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#537 id=537 data-nosnippet>537</a>    /// #
<a href=#538 id=538 data-nosnippet>538</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#539 id=539 data-nosnippet>539</a>    /// ```
<a href=#540 id=540 data-nosnippet>540</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#541 id=541 data-nosnippet>541</a>    </span><span class="kw">pub fn </span>new_signed_with_payer&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#542 id=542 data-nosnippet>542</a>        instructions: <span class="kw-2">&amp;</span>[Instruction],
<a href=#543 id=543 data-nosnippet>543</a>        payer: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt;,
<a href=#544 id=544 data-nosnippet>544</a>        signing_keypairs: <span class="kw-2">&amp;</span>T,
<a href=#545 id=545 data-nosnippet>545</a>        recent_blockhash: Hash,
<a href=#546 id=546 data-nosnippet>546</a>    ) -&gt; <span class="self">Self </span>{
<a href=#547 id=547 data-nosnippet>547</a>        <span class="kw">let </span>message = Message::new(instructions, payer);
<a href=#548 id=548 data-nosnippet>548</a>        <span class="self">Self</span>::new(signing_keypairs, message, recent_blockhash)
<a href=#549 id=549 data-nosnippet>549</a>    }
<a href=#550 id=550 data-nosnippet>550</a>
<a href=#551 id=551 data-nosnippet>551</a>    <span class="doccomment">/// Create a fully-signed transaction from pre-compiled instructions.
<a href=#552 id=552 data-nosnippet>552</a>    ///
<a href=#553 id=553 data-nosnippet>553</a>    /// # Arguments
<a href=#554 id=554 data-nosnippet>554</a>    ///
<a href=#555 id=555 data-nosnippet>555</a>    /// * `from_keypairs` - The keys used to sign the transaction.
<a href=#556 id=556 data-nosnippet>556</a>    /// * `keys` - The keys for the transaction.  These are the program state
<a href=#557 id=557 data-nosnippet>557</a>    ///    instances or lamport recipient keys.
<a href=#558 id=558 data-nosnippet>558</a>    /// * `recent_blockhash` - The PoH hash.
<a href=#559 id=559 data-nosnippet>559</a>    /// * `program_ids` - The keys that identify programs used in the `instruction` vector.
<a href=#560 id=560 data-nosnippet>560</a>    /// * `instructions` - Instructions that will be executed atomically.
<a href=#561 id=561 data-nosnippet>561</a>    ///
<a href=#562 id=562 data-nosnippet>562</a>    /// # Panics
<a href=#563 id=563 data-nosnippet>563</a>    ///
<a href=#564 id=564 data-nosnippet>564</a>    /// Panics when signing fails. See [`Transaction::try_sign`] and for a full
<a href=#565 id=565 data-nosnippet>565</a>    /// description of failure conditions.
<a href=#566 id=566 data-nosnippet>566</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#567 id=567 data-nosnippet>567</a>    </span><span class="kw">pub fn </span>new_with_compiled_instructions&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#568 id=568 data-nosnippet>568</a>        from_keypairs: <span class="kw-2">&amp;</span>T,
<a href=#569 id=569 data-nosnippet>569</a>        keys: <span class="kw-2">&amp;</span>[Pubkey],
<a href=#570 id=570 data-nosnippet>570</a>        recent_blockhash: Hash,
<a href=#571 id=571 data-nosnippet>571</a>        program_ids: Vec&lt;Pubkey&gt;,
<a href=#572 id=572 data-nosnippet>572</a>        instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#573 id=573 data-nosnippet>573</a>    ) -&gt; <span class="self">Self </span>{
<a href=#574 id=574 data-nosnippet>574</a>        <span class="kw">let </span><span class="kw-2">mut </span>account_keys = from_keypairs.pubkeys();
<a href=#575 id=575 data-nosnippet>575</a>        <span class="kw">let </span>from_keypairs_len = account_keys.len();
<a href=#576 id=576 data-nosnippet>576</a>        account_keys.extend_from_slice(keys);
<a href=#577 id=577 data-nosnippet>577</a>        account_keys.extend(<span class="kw-2">&amp;</span>program_ids);
<a href=#578 id=578 data-nosnippet>578</a>        <span class="kw">let </span>message = Message::new_with_compiled_instructions(
<a href=#579 id=579 data-nosnippet>579</a>            from_keypairs_len <span class="kw">as </span>u8,
<a href=#580 id=580 data-nosnippet>580</a>            <span class="number">0</span>,
<a href=#581 id=581 data-nosnippet>581</a>            program_ids.len() <span class="kw">as </span>u8,
<a href=#582 id=582 data-nosnippet>582</a>            account_keys,
<a href=#583 id=583 data-nosnippet>583</a>            Hash::default(),
<a href=#584 id=584 data-nosnippet>584</a>            instructions,
<a href=#585 id=585 data-nosnippet>585</a>        );
<a href=#586 id=586 data-nosnippet>586</a>        Transaction::new(from_keypairs, message, recent_blockhash)
<a href=#587 id=587 data-nosnippet>587</a>    }
<a href=#588 id=588 data-nosnippet>588</a>
<a href=#589 id=589 data-nosnippet>589</a>    <span class="doccomment">/// Get the data for an instruction at the given index.
<a href=#590 id=590 data-nosnippet>590</a>    ///
<a href=#591 id=591 data-nosnippet>591</a>    /// The `instruction_index` corresponds to the [`instructions`] vector of
<a href=#592 id=592 data-nosnippet>592</a>    /// the `Transaction`'s [`Message`] value.
<a href=#593 id=593 data-nosnippet>593</a>    ///
<a href=#594 id=594 data-nosnippet>594</a>    /// [`instructions`]: Message::instructions
<a href=#595 id=595 data-nosnippet>595</a>    ///
<a href=#596 id=596 data-nosnippet>596</a>    /// # Panics
<a href=#597 id=597 data-nosnippet>597</a>    ///
<a href=#598 id=598 data-nosnippet>598</a>    /// Panics if `instruction_index` is greater than or equal to the number of
<a href=#599 id=599 data-nosnippet>599</a>    /// instructions in the transaction.
<a href=#600 id=600 data-nosnippet>600</a>    </span><span class="kw">pub fn </span>data(<span class="kw-2">&amp;</span><span class="self">self</span>, instruction_index: usize) -&gt; <span class="kw-2">&amp;</span>[u8] {
<a href=#601 id=601 data-nosnippet>601</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.message.instructions[instruction_index].data
<a href=#602 id=602 data-nosnippet>602</a>    }
<a href=#603 id=603 data-nosnippet>603</a>
<a href=#604 id=604 data-nosnippet>604</a>    <span class="kw">fn </span>key_index(<span class="kw-2">&amp;</span><span class="self">self</span>, instruction_index: usize, accounts_index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;usize&gt; {
<a href=#605 id=605 data-nosnippet>605</a>        <span class="self">self</span>.message
<a href=#606 id=606 data-nosnippet>606</a>            .instructions
<a href=#607 id=607 data-nosnippet>607</a>            .get(instruction_index)
<a href=#608 id=608 data-nosnippet>608</a>            .and_then(|instruction| instruction.accounts.get(accounts_index))
<a href=#609 id=609 data-nosnippet>609</a>            .map(|<span class="kw-2">&amp;</span>account_keys_index| account_keys_index <span class="kw">as </span>usize)
<a href=#610 id=610 data-nosnippet>610</a>    }
<a href=#611 id=611 data-nosnippet>611</a>
<a href=#612 id=612 data-nosnippet>612</a>    <span class="doccomment">/// Get the `Pubkey` of an account required by one of the instructions in
<a href=#613 id=613 data-nosnippet>613</a>    /// the transaction.
<a href=#614 id=614 data-nosnippet>614</a>    ///
<a href=#615 id=615 data-nosnippet>615</a>    /// The `instruction_index` corresponds to the [`instructions`] vector of
<a href=#616 id=616 data-nosnippet>616</a>    /// the `Transaction`'s [`Message`] value; and the `account_index` to the
<a href=#617 id=617 data-nosnippet>617</a>    /// [`accounts`] vector of the message's [`CompiledInstruction`]s.
<a href=#618 id=618 data-nosnippet>618</a>    ///
<a href=#619 id=619 data-nosnippet>619</a>    /// [`instructions`]: Message::instructions
<a href=#620 id=620 data-nosnippet>620</a>    /// [`accounts`]: CompiledInstruction::accounts
<a href=#621 id=621 data-nosnippet>621</a>    /// [`CompiledInstruction`]: CompiledInstruction
<a href=#622 id=622 data-nosnippet>622</a>    ///
<a href=#623 id=623 data-nosnippet>623</a>    /// Returns `None` if `instruction_index` is greater than or equal to the
<a href=#624 id=624 data-nosnippet>624</a>    /// number of instructions in the transaction; or if `accounts_index` is
<a href=#625 id=625 data-nosnippet>625</a>    /// greater than or equal to the number of accounts in the instruction.
<a href=#626 id=626 data-nosnippet>626</a>    </span><span class="kw">pub fn </span>key(<span class="kw-2">&amp;</span><span class="self">self</span>, instruction_index: usize, accounts_index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#627 id=627 data-nosnippet>627</a>        <span class="self">self</span>.key_index(instruction_index, accounts_index)
<a href=#628 id=628 data-nosnippet>628</a>            .and_then(|account_keys_index| <span class="self">self</span>.message.account_keys.get(account_keys_index))
<a href=#629 id=629 data-nosnippet>629</a>    }
<a href=#630 id=630 data-nosnippet>630</a>
<a href=#631 id=631 data-nosnippet>631</a>    <span class="doccomment">/// Get the `Pubkey` of a signing account required by one of the
<a href=#632 id=632 data-nosnippet>632</a>    /// instructions in the transaction.
<a href=#633 id=633 data-nosnippet>633</a>    ///
<a href=#634 id=634 data-nosnippet>634</a>    /// The transaction does not need to be signed for this function to return a
<a href=#635 id=635 data-nosnippet>635</a>    /// signing account's pubkey.
<a href=#636 id=636 data-nosnippet>636</a>    ///
<a href=#637 id=637 data-nosnippet>637</a>    /// Returns `None` if the indexed account is not required to sign the
<a href=#638 id=638 data-nosnippet>638</a>    /// transaction. Returns `None` if the [`signatures`] field does not contain
<a href=#639 id=639 data-nosnippet>639</a>    /// enough elements to hold a signature for the indexed account (this should
<a href=#640 id=640 data-nosnippet>640</a>    /// only be possible if `Transaction` has been manually constructed).
<a href=#641 id=641 data-nosnippet>641</a>    ///
<a href=#642 id=642 data-nosnippet>642</a>    /// [`signatures`]: Transaction::signatures
<a href=#643 id=643 data-nosnippet>643</a>    ///
<a href=#644 id=644 data-nosnippet>644</a>    /// Returns `None` if `instruction_index` is greater than or equal to the
<a href=#645 id=645 data-nosnippet>645</a>    /// number of instructions in the transaction; or if `accounts_index` is
<a href=#646 id=646 data-nosnippet>646</a>    /// greater than or equal to the number of accounts in the instruction.
<a href=#647 id=647 data-nosnippet>647</a>    </span><span class="kw">pub fn </span>signer_key(<span class="kw-2">&amp;</span><span class="self">self</span>, instruction_index: usize, accounts_index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#648 id=648 data-nosnippet>648</a>        <span class="kw">match </span><span class="self">self</span>.key_index(instruction_index, accounts_index) {
<a href=#649 id=649 data-nosnippet>649</a>            <span class="prelude-val">None </span>=&gt; <span class="prelude-val">None</span>,
<a href=#650 id=650 data-nosnippet>650</a>            <span class="prelude-val">Some</span>(signature_index) =&gt; {
<a href=#651 id=651 data-nosnippet>651</a>                <span class="kw">if </span>signature_index &gt;= <span class="self">self</span>.signatures.len() {
<a href=#652 id=652 data-nosnippet>652</a>                    <span class="kw">return </span><span class="prelude-val">None</span>;
<a href=#653 id=653 data-nosnippet>653</a>                }
<a href=#654 id=654 data-nosnippet>654</a>                <span class="self">self</span>.message.account_keys.get(signature_index)
<a href=#655 id=655 data-nosnippet>655</a>            }
<a href=#656 id=656 data-nosnippet>656</a>        }
<a href=#657 id=657 data-nosnippet>657</a>    }
<a href=#658 id=658 data-nosnippet>658</a>
<a href=#659 id=659 data-nosnippet>659</a>    <span class="doccomment">/// Return the message containing all data that should be signed.
<a href=#660 id=660 data-nosnippet>660</a>    </span><span class="kw">pub fn </span>message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Message {
<a href=#661 id=661 data-nosnippet>661</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.message
<a href=#662 id=662 data-nosnippet>662</a>    }
<a href=#663 id=663 data-nosnippet>663</a>
<a href=#664 id=664 data-nosnippet>664</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#665 id=665 data-nosnippet>665</a>    </span><span class="doccomment">/// Return the serialized message data to sign.
<a href=#666 id=666 data-nosnippet>666</a>    </span><span class="kw">pub fn </span>message_data(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;u8&gt; {
<a href=#667 id=667 data-nosnippet>667</a>        <span class="self">self</span>.message().serialize()
<a href=#668 id=668 data-nosnippet>668</a>    }
<a href=#669 id=669 data-nosnippet>669</a>
<a href=#670 id=670 data-nosnippet>670</a>    <span class="doccomment">/// Sign the transaction.
<a href=#671 id=671 data-nosnippet>671</a>    ///
<a href=#672 id=672 data-nosnippet>672</a>    /// This method fully signs a transaction with all required signers, which
<a href=#673 id=673 data-nosnippet>673</a>    /// must be present in the `keypairs` slice. To sign with only some of the
<a href=#674 id=674 data-nosnippet>674</a>    /// required signers, use [`Transaction::partial_sign`].
<a href=#675 id=675 data-nosnippet>675</a>    ///
<a href=#676 id=676 data-nosnippet>676</a>    /// If `recent_blockhash` is different than recorded in the transaction message's
<a href=#677 id=677 data-nosnippet>677</a>    /// [`recent_blockhash`] field, then the message's `recent_blockhash` will be updated
<a href=#678 id=678 data-nosnippet>678</a>    /// to the provided `recent_blockhash`, and any prior signatures will be cleared.
<a href=#679 id=679 data-nosnippet>679</a>    ///
<a href=#680 id=680 data-nosnippet>680</a>    /// [`recent_blockhash`]: Message::recent_blockhash
<a href=#681 id=681 data-nosnippet>681</a>    ///
<a href=#682 id=682 data-nosnippet>682</a>    /// # Panics
<a href=#683 id=683 data-nosnippet>683</a>    ///
<a href=#684 id=684 data-nosnippet>684</a>    /// Panics when signing fails. Use [`Transaction::try_sign`] to handle the
<a href=#685 id=685 data-nosnippet>685</a>    /// error. See the documentation for [`Transaction::try_sign`] for a full description of
<a href=#686 id=686 data-nosnippet>686</a>    /// failure conditions.
<a href=#687 id=687 data-nosnippet>687</a>    ///
<a href=#688 id=688 data-nosnippet>688</a>    /// # Examples
<a href=#689 id=689 data-nosnippet>689</a>    ///
<a href=#690 id=690 data-nosnippet>690</a>    /// This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#691 id=691 data-nosnippet>691</a>    ///
<a href=#692 id=692 data-nosnippet>692</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#693 id=693 data-nosnippet>693</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#694 id=694 data-nosnippet>694</a>    ///
<a href=#695 id=695 data-nosnippet>695</a>    /// ```
<a href=#696 id=696 data-nosnippet>696</a>    /// # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#697 id=697 data-nosnippet>697</a>    /// use anyhow::Result;
<a href=#698 id=698 data-nosnippet>698</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#699 id=699 data-nosnippet>699</a>    /// use solana_instruction::Instruction;
<a href=#700 id=700 data-nosnippet>700</a>    /// use solana_keypair::Keypair;
<a href=#701 id=701 data-nosnippet>701</a>    /// use solana_message::Message;
<a href=#702 id=702 data-nosnippet>702</a>    /// use solana_pubkey::Pubkey;
<a href=#703 id=703 data-nosnippet>703</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#704 id=704 data-nosnippet>704</a>    /// use solana_signer::Signer;
<a href=#705 id=705 data-nosnippet>705</a>    /// use solana_transaction::Transaction;
<a href=#706 id=706 data-nosnippet>706</a>    ///
<a href=#707 id=707 data-nosnippet>707</a>    /// // A custom program instruction. This would typically be defined in
<a href=#708 id=708 data-nosnippet>708</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#709 id=709 data-nosnippet>709</a>    /// // the client.
<a href=#710 id=710 data-nosnippet>710</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#711 id=711 data-nosnippet>711</a>    /// enum BankInstruction {
<a href=#712 id=712 data-nosnippet>712</a>    ///     Initialize,
<a href=#713 id=713 data-nosnippet>713</a>    ///     Deposit { lamports: u64 },
<a href=#714 id=714 data-nosnippet>714</a>    ///     Withdraw { lamports: u64 },
<a href=#715 id=715 data-nosnippet>715</a>    /// }
<a href=#716 id=716 data-nosnippet>716</a>    ///
<a href=#717 id=717 data-nosnippet>717</a>    /// fn send_initialize_tx(
<a href=#718 id=718 data-nosnippet>718</a>    ///     client: &amp;RpcClient,
<a href=#719 id=719 data-nosnippet>719</a>    ///     program_id: Pubkey,
<a href=#720 id=720 data-nosnippet>720</a>    ///     payer: &amp;Keypair
<a href=#721 id=721 data-nosnippet>721</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#722 id=722 data-nosnippet>722</a>    ///
<a href=#723 id=723 data-nosnippet>723</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#724 id=724 data-nosnippet>724</a>    ///
<a href=#725 id=725 data-nosnippet>725</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#726 id=726 data-nosnippet>726</a>    ///         program_id,
<a href=#727 id=727 data-nosnippet>727</a>    ///         &amp;bank_instruction,
<a href=#728 id=728 data-nosnippet>728</a>    ///         vec![],
<a href=#729 id=729 data-nosnippet>729</a>    ///     );
<a href=#730 id=730 data-nosnippet>730</a>    ///
<a href=#731 id=731 data-nosnippet>731</a>    ///     let mut tx = Transaction::new_with_payer(&amp;[instruction], Some(&amp;payer.pubkey()));
<a href=#732 id=732 data-nosnippet>732</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#733 id=733 data-nosnippet>733</a>    ///     tx.sign(&amp;[payer], blockhash);
<a href=#734 id=734 data-nosnippet>734</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#735 id=735 data-nosnippet>735</a>    ///
<a href=#736 id=736 data-nosnippet>736</a>    ///     Ok(())
<a href=#737 id=737 data-nosnippet>737</a>    /// }
<a href=#738 id=738 data-nosnippet>738</a>    /// #
<a href=#739 id=739 data-nosnippet>739</a>    /// # let client = RpcClient::new(String::new());
<a href=#740 id=740 data-nosnippet>740</a>    /// # let program_id = Pubkey::new_unique();
<a href=#741 id=741 data-nosnippet>741</a>    /// # let payer = Keypair::new();
<a href=#742 id=742 data-nosnippet>742</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#743 id=743 data-nosnippet>743</a>    /// #
<a href=#744 id=744 data-nosnippet>744</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#745 id=745 data-nosnippet>745</a>    /// ```
<a href=#746 id=746 data-nosnippet>746</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#747 id=747 data-nosnippet>747</a>    </span><span class="kw">pub fn </span>sign&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(<span class="kw-2">&amp;mut </span><span class="self">self</span>, keypairs: <span class="kw-2">&amp;</span>T, recent_blockhash: Hash) {
<a href=#748 id=748 data-nosnippet>748</a>        <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = <span class="self">self</span>.try_sign(keypairs, recent_blockhash) {
<a href=#749 id=749 data-nosnippet>749</a>            <span class="macro">panic!</span>(<span class="string">"Transaction::sign failed with error {e:?}"</span>);
<a href=#750 id=750 data-nosnippet>750</a>        }
<a href=#751 id=751 data-nosnippet>751</a>    }
<a href=#752 id=752 data-nosnippet>752</a>
<a href=#753 id=753 data-nosnippet>753</a>    <span class="doccomment">/// Sign the transaction with a subset of required keys.
<a href=#754 id=754 data-nosnippet>754</a>    ///
<a href=#755 id=755 data-nosnippet>755</a>    /// Unlike [`Transaction::sign`], this method does not require all keypairs
<a href=#756 id=756 data-nosnippet>756</a>    /// to be provided, allowing a transaction to be signed in multiple steps.
<a href=#757 id=757 data-nosnippet>757</a>    ///
<a href=#758 id=758 data-nosnippet>758</a>    /// It is permitted to sign a transaction with the same keypair multiple
<a href=#759 id=759 data-nosnippet>759</a>    /// times.
<a href=#760 id=760 data-nosnippet>760</a>    ///
<a href=#761 id=761 data-nosnippet>761</a>    /// If `recent_blockhash` is different than recorded in the transaction message's
<a href=#762 id=762 data-nosnippet>762</a>    /// [`recent_blockhash`] field, then the message's `recent_blockhash` will be updated
<a href=#763 id=763 data-nosnippet>763</a>    /// to the provided `recent_blockhash`, and any prior signatures will be cleared.
<a href=#764 id=764 data-nosnippet>764</a>    ///
<a href=#765 id=765 data-nosnippet>765</a>    /// [`recent_blockhash`]: Message::recent_blockhash
<a href=#766 id=766 data-nosnippet>766</a>    ///
<a href=#767 id=767 data-nosnippet>767</a>    /// # Panics
<a href=#768 id=768 data-nosnippet>768</a>    ///
<a href=#769 id=769 data-nosnippet>769</a>    /// Panics when signing fails. Use [`Transaction::try_partial_sign`] to
<a href=#770 id=770 data-nosnippet>770</a>    /// handle the error. See the documentation for
<a href=#771 id=771 data-nosnippet>771</a>    /// [`Transaction::try_partial_sign`] for a full description of failure
<a href=#772 id=772 data-nosnippet>772</a>    /// conditions.
<a href=#773 id=773 data-nosnippet>773</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#774 id=774 data-nosnippet>774</a>    </span><span class="kw">pub fn </span>partial_sign&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(<span class="kw-2">&amp;mut </span><span class="self">self</span>, keypairs: <span class="kw-2">&amp;</span>T, recent_blockhash: Hash) {
<a href=#775 id=775 data-nosnippet>775</a>        <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = <span class="self">self</span>.try_partial_sign(keypairs, recent_blockhash) {
<a href=#776 id=776 data-nosnippet>776</a>            <span class="macro">panic!</span>(<span class="string">"Transaction::partial_sign failed with error {e:?}"</span>);
<a href=#777 id=777 data-nosnippet>777</a>        }
<a href=#778 id=778 data-nosnippet>778</a>    }
<a href=#779 id=779 data-nosnippet>779</a>
<a href=#780 id=780 data-nosnippet>780</a>    <span class="doccomment">/// Sign the transaction with a subset of required keys.
<a href=#781 id=781 data-nosnippet>781</a>    ///
<a href=#782 id=782 data-nosnippet>782</a>    /// This places each of the signatures created from `keypairs` in the
<a href=#783 id=783 data-nosnippet>783</a>    /// corresponding position, as specified in the `positions` vector, in the
<a href=#784 id=784 data-nosnippet>784</a>    /// transactions [`signatures`] field. It does not verify that the signature
<a href=#785 id=785 data-nosnippet>785</a>    /// positions are correct.
<a href=#786 id=786 data-nosnippet>786</a>    ///
<a href=#787 id=787 data-nosnippet>787</a>    /// [`signatures`]: Transaction::signatures
<a href=#788 id=788 data-nosnippet>788</a>    ///
<a href=#789 id=789 data-nosnippet>789</a>    /// # Panics
<a href=#790 id=790 data-nosnippet>790</a>    ///
<a href=#791 id=791 data-nosnippet>791</a>    /// Panics if signing fails. Use [`Transaction::try_partial_sign_unchecked`]
<a href=#792 id=792 data-nosnippet>792</a>    /// to handle the error.
<a href=#793 id=793 data-nosnippet>793</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#794 id=794 data-nosnippet>794</a>    </span><span class="kw">pub fn </span>partial_sign_unchecked&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#795 id=795 data-nosnippet>795</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#796 id=796 data-nosnippet>796</a>        keypairs: <span class="kw-2">&amp;</span>T,
<a href=#797 id=797 data-nosnippet>797</a>        positions: Vec&lt;usize&gt;,
<a href=#798 id=798 data-nosnippet>798</a>        recent_blockhash: Hash,
<a href=#799 id=799 data-nosnippet>799</a>    ) {
<a href=#800 id=800 data-nosnippet>800</a>        <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = <span class="self">self</span>.try_partial_sign_unchecked(keypairs, positions, recent_blockhash) {
<a href=#801 id=801 data-nosnippet>801</a>            <span class="macro">panic!</span>(<span class="string">"Transaction::partial_sign_unchecked failed with error {e:?}"</span>);
<a href=#802 id=802 data-nosnippet>802</a>        }
<a href=#803 id=803 data-nosnippet>803</a>    }
<a href=#804 id=804 data-nosnippet>804</a>
<a href=#805 id=805 data-nosnippet>805</a>    <span class="doccomment">/// Sign the transaction, returning any errors.
<a href=#806 id=806 data-nosnippet>806</a>    ///
<a href=#807 id=807 data-nosnippet>807</a>    /// This method fully signs a transaction with all required signers, which
<a href=#808 id=808 data-nosnippet>808</a>    /// must be present in the `keypairs` slice. To sign with only some of the
<a href=#809 id=809 data-nosnippet>809</a>    /// required signers, use [`Transaction::try_partial_sign`].
<a href=#810 id=810 data-nosnippet>810</a>    ///
<a href=#811 id=811 data-nosnippet>811</a>    /// If `recent_blockhash` is different than recorded in the transaction message's
<a href=#812 id=812 data-nosnippet>812</a>    /// [`recent_blockhash`] field, then the message's `recent_blockhash` will be updated
<a href=#813 id=813 data-nosnippet>813</a>    /// to the provided `recent_blockhash`, and any prior signatures will be cleared.
<a href=#814 id=814 data-nosnippet>814</a>    ///
<a href=#815 id=815 data-nosnippet>815</a>    /// [`recent_blockhash`]: Message::recent_blockhash
<a href=#816 id=816 data-nosnippet>816</a>    ///
<a href=#817 id=817 data-nosnippet>817</a>    /// # Errors
<a href=#818 id=818 data-nosnippet>818</a>    ///
<a href=#819 id=819 data-nosnippet>819</a>    /// Signing will fail if some required signers are not provided in
<a href=#820 id=820 data-nosnippet>820</a>    /// `keypairs`; or, if the transaction has previously been partially signed,
<a href=#821 id=821 data-nosnippet>821</a>    /// some of the remaining required signers are not provided in `keypairs`.
<a href=#822 id=822 data-nosnippet>822</a>    /// In other words, the transaction must be fully signed as a result of
<a href=#823 id=823 data-nosnippet>823</a>    /// calling this function. The error is [`SignerError::NotEnoughSigners`].
<a href=#824 id=824 data-nosnippet>824</a>    ///
<a href=#825 id=825 data-nosnippet>825</a>    /// Signing will fail for any of the reasons described in the documentation
<a href=#826 id=826 data-nosnippet>826</a>    /// for [`Transaction::try_partial_sign`].
<a href=#827 id=827 data-nosnippet>827</a>    ///
<a href=#828 id=828 data-nosnippet>828</a>    /// # Examples
<a href=#829 id=829 data-nosnippet>829</a>    ///
<a href=#830 id=830 data-nosnippet>830</a>    /// This example uses the [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#831 id=831 data-nosnippet>831</a>    ///
<a href=#832 id=832 data-nosnippet>832</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#833 id=833 data-nosnippet>833</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#834 id=834 data-nosnippet>834</a>    ///
<a href=#835 id=835 data-nosnippet>835</a>    /// ```
<a href=#836 id=836 data-nosnippet>836</a>    /// # use solana_sdk::example_mocks::solana_rpc_client;
<a href=#837 id=837 data-nosnippet>837</a>    /// use anyhow::Result;
<a href=#838 id=838 data-nosnippet>838</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#839 id=839 data-nosnippet>839</a>    /// use solana_instruction::Instruction;
<a href=#840 id=840 data-nosnippet>840</a>    /// use solana_keypair::Keypair;
<a href=#841 id=841 data-nosnippet>841</a>    /// use solana_message::Message;
<a href=#842 id=842 data-nosnippet>842</a>    /// use solana_pubkey::Pubkey;
<a href=#843 id=843 data-nosnippet>843</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#844 id=844 data-nosnippet>844</a>    /// use solana_signer::Signer;
<a href=#845 id=845 data-nosnippet>845</a>    /// use solana_transaction::Transaction;
<a href=#846 id=846 data-nosnippet>846</a>    ///
<a href=#847 id=847 data-nosnippet>847</a>    /// // A custom program instruction. This would typically be defined in
<a href=#848 id=848 data-nosnippet>848</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#849 id=849 data-nosnippet>849</a>    /// // the client.
<a href=#850 id=850 data-nosnippet>850</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#851 id=851 data-nosnippet>851</a>    /// enum BankInstruction {
<a href=#852 id=852 data-nosnippet>852</a>    ///     Initialize,
<a href=#853 id=853 data-nosnippet>853</a>    ///     Deposit { lamports: u64 },
<a href=#854 id=854 data-nosnippet>854</a>    ///     Withdraw { lamports: u64 },
<a href=#855 id=855 data-nosnippet>855</a>    /// }
<a href=#856 id=856 data-nosnippet>856</a>    ///
<a href=#857 id=857 data-nosnippet>857</a>    /// fn send_initialize_tx(
<a href=#858 id=858 data-nosnippet>858</a>    ///     client: &amp;RpcClient,
<a href=#859 id=859 data-nosnippet>859</a>    ///     program_id: Pubkey,
<a href=#860 id=860 data-nosnippet>860</a>    ///     payer: &amp;Keypair
<a href=#861 id=861 data-nosnippet>861</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#862 id=862 data-nosnippet>862</a>    ///
<a href=#863 id=863 data-nosnippet>863</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#864 id=864 data-nosnippet>864</a>    ///
<a href=#865 id=865 data-nosnippet>865</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#866 id=866 data-nosnippet>866</a>    ///         program_id,
<a href=#867 id=867 data-nosnippet>867</a>    ///         &amp;bank_instruction,
<a href=#868 id=868 data-nosnippet>868</a>    ///         vec![],
<a href=#869 id=869 data-nosnippet>869</a>    ///     );
<a href=#870 id=870 data-nosnippet>870</a>    ///
<a href=#871 id=871 data-nosnippet>871</a>    ///     let mut tx = Transaction::new_with_payer(&amp;[instruction], Some(&amp;payer.pubkey()));
<a href=#872 id=872 data-nosnippet>872</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#873 id=873 data-nosnippet>873</a>    ///     tx.try_sign(&amp;[payer], blockhash)?;
<a href=#874 id=874 data-nosnippet>874</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#875 id=875 data-nosnippet>875</a>    ///
<a href=#876 id=876 data-nosnippet>876</a>    ///     Ok(())
<a href=#877 id=877 data-nosnippet>877</a>    /// }
<a href=#878 id=878 data-nosnippet>878</a>    /// #
<a href=#879 id=879 data-nosnippet>879</a>    /// # let client = RpcClient::new(String::new());
<a href=#880 id=880 data-nosnippet>880</a>    /// # let program_id = Pubkey::new_unique();
<a href=#881 id=881 data-nosnippet>881</a>    /// # let payer = Keypair::new();
<a href=#882 id=882 data-nosnippet>882</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#883 id=883 data-nosnippet>883</a>    /// #
<a href=#884 id=884 data-nosnippet>884</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#885 id=885 data-nosnippet>885</a>    /// ```
<a href=#886 id=886 data-nosnippet>886</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#887 id=887 data-nosnippet>887</a>    </span><span class="kw">pub fn </span>try_sign&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#888 id=888 data-nosnippet>888</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#889 id=889 data-nosnippet>889</a>        keypairs: <span class="kw-2">&amp;</span>T,
<a href=#890 id=890 data-nosnippet>890</a>        recent_blockhash: Hash,
<a href=#891 id=891 data-nosnippet>891</a>    ) -&gt; result::Result&lt;(), SignerError&gt; {
<a href=#892 id=892 data-nosnippet>892</a>        <span class="self">self</span>.try_partial_sign(keypairs, recent_blockhash)<span class="question-mark">?</span>;
<a href=#893 id=893 data-nosnippet>893</a>
<a href=#894 id=894 data-nosnippet>894</a>        <span class="kw">if </span>!<span class="self">self</span>.is_signed() {
<a href=#895 id=895 data-nosnippet>895</a>            <span class="prelude-val">Err</span>(SignerError::NotEnoughSigners)
<a href=#896 id=896 data-nosnippet>896</a>        } <span class="kw">else </span>{
<a href=#897 id=897 data-nosnippet>897</a>            <span class="prelude-val">Ok</span>(())
<a href=#898 id=898 data-nosnippet>898</a>        }
<a href=#899 id=899 data-nosnippet>899</a>    }
<a href=#900 id=900 data-nosnippet>900</a>
<a href=#901 id=901 data-nosnippet>901</a>    <span class="doccomment">/// Sign the transaction with a subset of required keys, returning any errors.
<a href=#902 id=902 data-nosnippet>902</a>    ///
<a href=#903 id=903 data-nosnippet>903</a>    /// Unlike [`Transaction::try_sign`], this method does not require all
<a href=#904 id=904 data-nosnippet>904</a>    /// keypairs to be provided, allowing a transaction to be signed in multiple
<a href=#905 id=905 data-nosnippet>905</a>    /// steps.
<a href=#906 id=906 data-nosnippet>906</a>    ///
<a href=#907 id=907 data-nosnippet>907</a>    /// It is permitted to sign a transaction with the same keypair multiple
<a href=#908 id=908 data-nosnippet>908</a>    /// times.
<a href=#909 id=909 data-nosnippet>909</a>    ///
<a href=#910 id=910 data-nosnippet>910</a>    /// If `recent_blockhash` is different than recorded in the transaction message's
<a href=#911 id=911 data-nosnippet>911</a>    /// [`recent_blockhash`] field, then the message's `recent_blockhash` will be updated
<a href=#912 id=912 data-nosnippet>912</a>    /// to the provided `recent_blockhash`, and any prior signatures will be cleared.
<a href=#913 id=913 data-nosnippet>913</a>    ///
<a href=#914 id=914 data-nosnippet>914</a>    /// [`recent_blockhash`]: Message::recent_blockhash
<a href=#915 id=915 data-nosnippet>915</a>    ///
<a href=#916 id=916 data-nosnippet>916</a>    /// # Errors
<a href=#917 id=917 data-nosnippet>917</a>    ///
<a href=#918 id=918 data-nosnippet>918</a>    /// Signing will fail if
<a href=#919 id=919 data-nosnippet>919</a>    ///
<a href=#920 id=920 data-nosnippet>920</a>    /// - The transaction's [`Message`] is malformed such that the number of
<a href=#921 id=921 data-nosnippet>921</a>    ///   required signatures recorded in its header
<a href=#922 id=922 data-nosnippet>922</a>    ///   ([`num_required_signatures`]) is greater than the length of its
<a href=#923 id=923 data-nosnippet>923</a>    ///   account keys ([`account_keys`]). The error is
<a href=#924 id=924 data-nosnippet>924</a>    ///   [`SignerError::TransactionError`] where the interior
<a href=#925 id=925 data-nosnippet>925</a>    ///   [`TransactionError`] is [`TransactionError::InvalidAccountIndex`].
<a href=#926 id=926 data-nosnippet>926</a>    /// - Any of the provided signers in `keypairs` is not a required signer of
<a href=#927 id=927 data-nosnippet>927</a>    ///   the message. The error is [`SignerError::KeypairPubkeyMismatch`].
<a href=#928 id=928 data-nosnippet>928</a>    /// - Any of the signers is a [`Presigner`], and its provided signature is
<a href=#929 id=929 data-nosnippet>929</a>    ///   incorrect. The error is [`SignerError::PresignerError`] where the
<a href=#930 id=930 data-nosnippet>930</a>    ///   interior [`PresignerError`] is
<a href=#931 id=931 data-nosnippet>931</a>    ///   [`PresignerError::VerificationFailure`].
<a href=#932 id=932 data-nosnippet>932</a>    /// - The signer is a [`RemoteKeypair`] and
<a href=#933 id=933 data-nosnippet>933</a>    ///   - It does not understand the input provided ([`SignerError::InvalidInput`]).
<a href=#934 id=934 data-nosnippet>934</a>    ///   - The device cannot be found ([`SignerError::NoDeviceFound`]).
<a href=#935 id=935 data-nosnippet>935</a>    ///   - The user cancels the signing ([`SignerError::UserCancel`]).
<a href=#936 id=936 data-nosnippet>936</a>    ///   - An error was encountered connecting ([`SignerError::Connection`]).
<a href=#937 id=937 data-nosnippet>937</a>    ///   - Some device-specific protocol error occurs ([`SignerError::Protocol`]).
<a href=#938 id=938 data-nosnippet>938</a>    ///   - Some other error occurs ([`SignerError::Custom`]).
<a href=#939 id=939 data-nosnippet>939</a>    ///
<a href=#940 id=940 data-nosnippet>940</a>    /// See the documentation for the [`solana-remote-wallet`] crate for details
<a href=#941 id=941 data-nosnippet>941</a>    /// on the operation of [`RemoteKeypair`] signers.
<a href=#942 id=942 data-nosnippet>942</a>    ///
<a href=#943 id=943 data-nosnippet>943</a>    /// [`num_required_signatures`]: https://docs.rs/solana-message/latest/solana_message/struct.MessageHeader.html#structfield.num_required_signatures
<a href=#944 id=944 data-nosnippet>944</a>    /// [`account_keys`]: https://docs.rs/solana-message/latest/solana_message/legacy/struct.Message.html#structfield.account_keys
<a href=#945 id=945 data-nosnippet>945</a>    /// [`Presigner`]: https://docs.rs/solana-presigner/latest/solana_presigner/struct.Presigner.html
<a href=#946 id=946 data-nosnippet>946</a>    /// [`PresignerError`]: https://docs.rs/solana-signer/latest/solana_signer/enum.PresignerError.html
<a href=#947 id=947 data-nosnippet>947</a>    /// [`PresignerError::VerificationFailure`]: https://docs.rs/solana-signer/latest/solana_signer/enum.PresignerError.html#variant.WrongSize
<a href=#948 id=948 data-nosnippet>948</a>    /// [`solana-remote-wallet`]: https://docs.rs/solana-remote-wallet/latest/
<a href=#949 id=949 data-nosnippet>949</a>    /// [`RemoteKeypair`]: https://docs.rs/solana-remote-wallet/latest/solana_remote_wallet/remote_keypair/struct.RemoteKeypair.html
<a href=#950 id=950 data-nosnippet>950</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#951 id=951 data-nosnippet>951</a>    </span><span class="kw">pub fn </span>try_partial_sign&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#952 id=952 data-nosnippet>952</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#953 id=953 data-nosnippet>953</a>        keypairs: <span class="kw-2">&amp;</span>T,
<a href=#954 id=954 data-nosnippet>954</a>        recent_blockhash: Hash,
<a href=#955 id=955 data-nosnippet>955</a>    ) -&gt; result::Result&lt;(), SignerError&gt; {
<a href=#956 id=956 data-nosnippet>956</a>        <span class="kw">let </span>positions: Vec&lt;usize&gt; = <span class="self">self
<a href=#957 id=957 data-nosnippet>957</a>            </span>.get_signing_keypair_positions(<span class="kw-2">&amp;</span>keypairs.pubkeys())<span class="question-mark">?
<a href=#958 id=958 data-nosnippet>958</a>            </span>.into_iter()
<a href=#959 id=959 data-nosnippet>959</a>            .collect::&lt;<span class="prelude-ty">Option</span>&lt;<span class="kw">_</span>&gt;&gt;()
<a href=#960 id=960 data-nosnippet>960</a>            .ok_or(SignerError::KeypairPubkeyMismatch)<span class="question-mark">?</span>;
<a href=#961 id=961 data-nosnippet>961</a>        <span class="self">self</span>.try_partial_sign_unchecked(keypairs, positions, recent_blockhash)
<a href=#962 id=962 data-nosnippet>962</a>    }
<a href=#963 id=963 data-nosnippet>963</a>
<a href=#964 id=964 data-nosnippet>964</a>    <span class="doccomment">/// Sign the transaction with a subset of required keys, returning any
<a href=#965 id=965 data-nosnippet>965</a>    /// errors.
<a href=#966 id=966 data-nosnippet>966</a>    ///
<a href=#967 id=967 data-nosnippet>967</a>    /// This places each of the signatures created from `keypairs` in the
<a href=#968 id=968 data-nosnippet>968</a>    /// corresponding position, as specified in the `positions` vector, in the
<a href=#969 id=969 data-nosnippet>969</a>    /// transactions [`signatures`] field. It does not verify that the signature
<a href=#970 id=970 data-nosnippet>970</a>    /// positions are correct.
<a href=#971 id=971 data-nosnippet>971</a>    ///
<a href=#972 id=972 data-nosnippet>972</a>    /// [`signatures`]: Transaction::signatures
<a href=#973 id=973 data-nosnippet>973</a>    ///
<a href=#974 id=974 data-nosnippet>974</a>    /// # Errors
<a href=#975 id=975 data-nosnippet>975</a>    ///
<a href=#976 id=976 data-nosnippet>976</a>    /// Returns an error if signing fails.
<a href=#977 id=977 data-nosnippet>977</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#978 id=978 data-nosnippet>978</a>    </span><span class="kw">pub fn </span>try_partial_sign_unchecked&lt;T: Signers + <span class="question-mark">?</span>Sized&gt;(
<a href=#979 id=979 data-nosnippet>979</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#980 id=980 data-nosnippet>980</a>        keypairs: <span class="kw-2">&amp;</span>T,
<a href=#981 id=981 data-nosnippet>981</a>        positions: Vec&lt;usize&gt;,
<a href=#982 id=982 data-nosnippet>982</a>        recent_blockhash: Hash,
<a href=#983 id=983 data-nosnippet>983</a>    ) -&gt; result::Result&lt;(), SignerError&gt; {
<a href=#984 id=984 data-nosnippet>984</a>        <span class="comment">// if you change the blockhash, you're re-signing...
<a href=#985 id=985 data-nosnippet>985</a>        </span><span class="kw">if </span>recent_blockhash != <span class="self">self</span>.message.recent_blockhash {
<a href=#986 id=986 data-nosnippet>986</a>            <span class="self">self</span>.message.recent_blockhash = recent_blockhash;
<a href=#987 id=987 data-nosnippet>987</a>            <span class="self">self</span>.signatures
<a href=#988 id=988 data-nosnippet>988</a>                .iter_mut()
<a href=#989 id=989 data-nosnippet>989</a>                .for_each(|signature| <span class="kw-2">*</span>signature = Signature::default());
<a href=#990 id=990 data-nosnippet>990</a>        }
<a href=#991 id=991 data-nosnippet>991</a>
<a href=#992 id=992 data-nosnippet>992</a>        <span class="kw">let </span>signatures = keypairs.try_sign_message(<span class="kw-2">&amp;</span><span class="self">self</span>.message_data())<span class="question-mark">?</span>;
<a href=#993 id=993 data-nosnippet>993</a>        <span class="kw">for </span>i <span class="kw">in </span><span class="number">0</span>..positions.len() {
<a href=#994 id=994 data-nosnippet>994</a>            <span class="self">self</span>.signatures[positions[i]] = signatures[i];
<a href=#995 id=995 data-nosnippet>995</a>        }
<a href=#996 id=996 data-nosnippet>996</a>        <span class="prelude-val">Ok</span>(())
<a href=#997 id=997 data-nosnippet>997</a>    }
<a href=#998 id=998 data-nosnippet>998</a>
<a href=#999 id=999 data-nosnippet>999</a>    <span class="doccomment">/// Returns a signature that is not valid for signing this transaction.
<a href=#1000 id=1000 data-nosnippet>1000</a>    </span><span class="kw">pub fn </span>get_invalid_signature() -&gt; Signature {
<a href=#1001 id=1001 data-nosnippet>1001</a>        Signature::default()
<a href=#1002 id=1002 data-nosnippet>1002</a>    }
<a href=#1003 id=1003 data-nosnippet>1003</a>
<a href=#1004 id=1004 data-nosnippet>1004</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#1005 id=1005 data-nosnippet>1005</a>    </span><span class="doccomment">/// Verifies that all signers have signed the message.
<a href=#1006 id=1006 data-nosnippet>1006</a>    ///
<a href=#1007 id=1007 data-nosnippet>1007</a>    /// # Errors
<a href=#1008 id=1008 data-nosnippet>1008</a>    ///
<a href=#1009 id=1009 data-nosnippet>1009</a>    /// Returns [`TransactionError::SignatureFailure`] on error.
<a href=#1010 id=1010 data-nosnippet>1010</a>    </span><span class="kw">pub fn </span>verify(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#1011 id=1011 data-nosnippet>1011</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.message_data();
<a href=#1012 id=1012 data-nosnippet>1012</a>        <span class="kw">if </span>!<span class="self">self
<a href=#1013 id=1013 data-nosnippet>1013</a>            </span>._verify_with_results(<span class="kw-2">&amp;</span>message_bytes)
<a href=#1014 id=1014 data-nosnippet>1014</a>            .iter()
<a href=#1015 id=1015 data-nosnippet>1015</a>            .all(|verify_result| <span class="kw-2">*</span>verify_result)
<a href=#1016 id=1016 data-nosnippet>1016</a>        {
<a href=#1017 id=1017 data-nosnippet>1017</a>            <span class="prelude-val">Err</span>(TransactionError::SignatureFailure)
<a href=#1018 id=1018 data-nosnippet>1018</a>        } <span class="kw">else </span>{
<a href=#1019 id=1019 data-nosnippet>1019</a>            <span class="prelude-val">Ok</span>(())
<a href=#1020 id=1020 data-nosnippet>1020</a>        }
<a href=#1021 id=1021 data-nosnippet>1021</a>    }
<a href=#1022 id=1022 data-nosnippet>1022</a>
<a href=#1023 id=1023 data-nosnippet>1023</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#1024 id=1024 data-nosnippet>1024</a>    </span><span class="doccomment">/// Verify the transaction and hash its message.
<a href=#1025 id=1025 data-nosnippet>1025</a>    ///
<a href=#1026 id=1026 data-nosnippet>1026</a>    /// # Errors
<a href=#1027 id=1027 data-nosnippet>1027</a>    ///
<a href=#1028 id=1028 data-nosnippet>1028</a>    /// Returns [`TransactionError::SignatureFailure`] on error.
<a href=#1029 id=1029 data-nosnippet>1029</a>    </span><span class="kw">pub fn </span>verify_and_hash_message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Hash&gt; {
<a href=#1030 id=1030 data-nosnippet>1030</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.message_data();
<a href=#1031 id=1031 data-nosnippet>1031</a>        <span class="kw">if </span>!<span class="self">self
<a href=#1032 id=1032 data-nosnippet>1032</a>            </span>._verify_with_results(<span class="kw-2">&amp;</span>message_bytes)
<a href=#1033 id=1033 data-nosnippet>1033</a>            .iter()
<a href=#1034 id=1034 data-nosnippet>1034</a>            .all(|verify_result| <span class="kw-2">*</span>verify_result)
<a href=#1035 id=1035 data-nosnippet>1035</a>        {
<a href=#1036 id=1036 data-nosnippet>1036</a>            <span class="prelude-val">Err</span>(TransactionError::SignatureFailure)
<a href=#1037 id=1037 data-nosnippet>1037</a>        } <span class="kw">else </span>{
<a href=#1038 id=1038 data-nosnippet>1038</a>            <span class="prelude-val">Ok</span>(Message::hash_raw_message(<span class="kw-2">&amp;</span>message_bytes))
<a href=#1039 id=1039 data-nosnippet>1039</a>        }
<a href=#1040 id=1040 data-nosnippet>1040</a>    }
<a href=#1041 id=1041 data-nosnippet>1041</a>
<a href=#1042 id=1042 data-nosnippet>1042</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#1043 id=1043 data-nosnippet>1043</a>    </span><span class="doccomment">/// Verifies that all signers have signed the message.
<a href=#1044 id=1044 data-nosnippet>1044</a>    ///
<a href=#1045 id=1045 data-nosnippet>1045</a>    /// Returns a vector with the length of required signatures, where each
<a href=#1046 id=1046 data-nosnippet>1046</a>    /// element is either `true` if that signer has signed, or `false` if not.
<a href=#1047 id=1047 data-nosnippet>1047</a>    </span><span class="kw">pub fn </span>verify_with_results(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;bool&gt; {
<a href=#1048 id=1048 data-nosnippet>1048</a>        <span class="self">self</span>._verify_with_results(<span class="kw-2">&amp;</span><span class="self">self</span>.message_data())
<a href=#1049 id=1049 data-nosnippet>1049</a>    }
<a href=#1050 id=1050 data-nosnippet>1050</a>
<a href=#1051 id=1051 data-nosnippet>1051</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#1052 id=1052 data-nosnippet>1052</a>    </span><span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">fn </span>_verify_with_results(<span class="kw-2">&amp;</span><span class="self">self</span>, message_bytes: <span class="kw-2">&amp;</span>[u8]) -&gt; Vec&lt;bool&gt; {
<a href=#1053 id=1053 data-nosnippet>1053</a>        <span class="self">self</span>.signatures
<a href=#1054 id=1054 data-nosnippet>1054</a>            .iter()
<a href=#1055 id=1055 data-nosnippet>1055</a>            .zip(<span class="kw-2">&amp;</span><span class="self">self</span>.message.account_keys)
<a href=#1056 id=1056 data-nosnippet>1056</a>            .map(|(signature, pubkey)| signature.verify(pubkey.as_ref(), message_bytes))
<a href=#1057 id=1057 data-nosnippet>1057</a>            .collect()
<a href=#1058 id=1058 data-nosnippet>1058</a>    }
<a href=#1059 id=1059 data-nosnippet>1059</a>
<a href=#1060 id=1060 data-nosnippet>1060</a>    <span class="attr">#[cfg(feature = <span class="string">"precompiles"</span>)]
<a href=#1061 id=1061 data-nosnippet>1061</a>    </span><span class="doccomment">/// Verify the precompiled programs in this transaction.
<a href=#1062 id=1062 data-nosnippet>1062</a>    </span><span class="kw">pub fn </span>verify_precompiles(<span class="kw-2">&amp;</span><span class="self">self</span>, feature_set: <span class="kw-2">&amp;</span>solana_feature_set::FeatureSet) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#1063 id=1063 data-nosnippet>1063</a>        <span class="kw">for </span>instruction <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.message().instructions {
<a href=#1064 id=1064 data-nosnippet>1064</a>            <span class="comment">// The Transaction may not be sanitized at this point
<a href=#1065 id=1065 data-nosnippet>1065</a>            </span><span class="kw">if </span>instruction.program_id_index <span class="kw">as </span>usize &gt;= <span class="self">self</span>.message().account_keys.len() {
<a href=#1066 id=1066 data-nosnippet>1066</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(TransactionError::AccountNotFound);
<a href=#1067 id=1067 data-nosnippet>1067</a>            }
<a href=#1068 id=1068 data-nosnippet>1068</a>            <span class="kw">let </span>program_id = <span class="kw-2">&amp;</span><span class="self">self</span>.message().account_keys[instruction.program_id_index <span class="kw">as </span>usize];
<a href=#1069 id=1069 data-nosnippet>1069</a>
<a href=#1070 id=1070 data-nosnippet>1070</a>            solana_precompiles::verify_if_precompile(
<a href=#1071 id=1071 data-nosnippet>1071</a>                program_id,
<a href=#1072 id=1072 data-nosnippet>1072</a>                instruction,
<a href=#1073 id=1073 data-nosnippet>1073</a>                <span class="kw-2">&amp;</span><span class="self">self</span>.message().instructions,
<a href=#1074 id=1074 data-nosnippet>1074</a>                feature_set,
<a href=#1075 id=1075 data-nosnippet>1075</a>            )
<a href=#1076 id=1076 data-nosnippet>1076</a>            .map_err(|<span class="kw">_</span>| TransactionError::InvalidAccountIndex)<span class="question-mark">?</span>;
<a href=#1077 id=1077 data-nosnippet>1077</a>        }
<a href=#1078 id=1078 data-nosnippet>1078</a>        <span class="prelude-val">Ok</span>(())
<a href=#1079 id=1079 data-nosnippet>1079</a>    }
<a href=#1080 id=1080 data-nosnippet>1080</a>
<a href=#1081 id=1081 data-nosnippet>1081</a>    <span class="doccomment">/// Get the positions of the pubkeys in `account_keys` associated with signing keypairs.
<a href=#1082 id=1082 data-nosnippet>1082</a>    ///
<a href=#1083 id=1083 data-nosnippet>1083</a>    /// [`account_keys`]: Message::account_keys
<a href=#1084 id=1084 data-nosnippet>1084</a>    </span><span class="kw">pub fn </span>get_signing_keypair_positions(<span class="kw-2">&amp;</span><span class="self">self</span>, pubkeys: <span class="kw-2">&amp;</span>[Pubkey]) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;<span class="prelude-ty">Option</span>&lt;usize&gt;&gt;&gt; {
<a href=#1085 id=1085 data-nosnippet>1085</a>        <span class="kw">if </span><span class="self">self</span>.message.account_keys.len() &lt; <span class="self">self</span>.message.header.num_required_signatures <span class="kw">as </span>usize {
<a href=#1086 id=1086 data-nosnippet>1086</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(TransactionError::InvalidAccountIndex);
<a href=#1087 id=1087 data-nosnippet>1087</a>        }
<a href=#1088 id=1088 data-nosnippet>1088</a>        <span class="kw">let </span>signed_keys =
<a href=#1089 id=1089 data-nosnippet>1089</a>            <span class="kw-2">&amp;</span><span class="self">self</span>.message.account_keys[<span class="number">0</span>..<span class="self">self</span>.message.header.num_required_signatures <span class="kw">as </span>usize];
<a href=#1090 id=1090 data-nosnippet>1090</a>
<a href=#1091 id=1091 data-nosnippet>1091</a>        <span class="prelude-val">Ok</span>(pubkeys
<a href=#1092 id=1092 data-nosnippet>1092</a>            .iter()
<a href=#1093 id=1093 data-nosnippet>1093</a>            .map(|pubkey| signed_keys.iter().position(|x| x == pubkey))
<a href=#1094 id=1094 data-nosnippet>1094</a>            .collect())
<a href=#1095 id=1095 data-nosnippet>1095</a>    }
<a href=#1096 id=1096 data-nosnippet>1096</a>
<a href=#1097 id=1097 data-nosnippet>1097</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#1098 id=1098 data-nosnippet>1098</a>    </span><span class="doccomment">/// Replace all the signatures and pubkeys.
<a href=#1099 id=1099 data-nosnippet>1099</a>    </span><span class="kw">pub fn </span>replace_signatures(<span class="kw-2">&amp;mut </span><span class="self">self</span>, signers: <span class="kw-2">&amp;</span>[(Pubkey, Signature)]) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#1100 id=1100 data-nosnippet>1100</a>        <span class="kw">let </span>num_required_signatures = <span class="self">self</span>.message.header.num_required_signatures <span class="kw">as </span>usize;
<a href=#1101 id=1101 data-nosnippet>1101</a>        <span class="kw">if </span>signers.len() != num_required_signatures
<a href=#1102 id=1102 data-nosnippet>1102</a>            || <span class="self">self</span>.signatures.len() != num_required_signatures
<a href=#1103 id=1103 data-nosnippet>1103</a>            || <span class="self">self</span>.message.account_keys.len() &lt; num_required_signatures
<a href=#1104 id=1104 data-nosnippet>1104</a>        {
<a href=#1105 id=1105 data-nosnippet>1105</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(TransactionError::InvalidAccountIndex);
<a href=#1106 id=1106 data-nosnippet>1106</a>        }
<a href=#1107 id=1107 data-nosnippet>1107</a>
<a href=#1108 id=1108 data-nosnippet>1108</a>        <span class="kw">for </span>(index, account_key) <span class="kw">in </span><span class="self">self
<a href=#1109 id=1109 data-nosnippet>1109</a>            </span>.message
<a href=#1110 id=1110 data-nosnippet>1110</a>            .account_keys
<a href=#1111 id=1111 data-nosnippet>1111</a>            .iter()
<a href=#1112 id=1112 data-nosnippet>1112</a>            .enumerate()
<a href=#1113 id=1113 data-nosnippet>1113</a>            .take(num_required_signatures)
<a href=#1114 id=1114 data-nosnippet>1114</a>        {
<a href=#1115 id=1115 data-nosnippet>1115</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>((_pubkey, signature)) =
<a href=#1116 id=1116 data-nosnippet>1116</a>                signers.iter().find(|(key, _signature)| account_key == key)
<a href=#1117 id=1117 data-nosnippet>1117</a>            {
<a href=#1118 id=1118 data-nosnippet>1118</a>                <span class="self">self</span>.signatures[index] = <span class="kw-2">*</span>signature
<a href=#1119 id=1119 data-nosnippet>1119</a>            } <span class="kw">else </span>{
<a href=#1120 id=1120 data-nosnippet>1120</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(TransactionError::InvalidAccountIndex);
<a href=#1121 id=1121 data-nosnippet>1121</a>            }
<a href=#1122 id=1122 data-nosnippet>1122</a>        }
<a href=#1123 id=1123 data-nosnippet>1123</a>
<a href=#1124 id=1124 data-nosnippet>1124</a>        <span class="self">self</span>.verify()
<a href=#1125 id=1125 data-nosnippet>1125</a>    }
<a href=#1126 id=1126 data-nosnippet>1126</a>
<a href=#1127 id=1127 data-nosnippet>1127</a>    <span class="kw">pub fn </span>is_signed(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#1128 id=1128 data-nosnippet>1128</a>        <span class="self">self</span>.signatures
<a href=#1129 id=1129 data-nosnippet>1129</a>            .iter()
<a href=#1130 id=1130 data-nosnippet>1130</a>            .all(|signature| <span class="kw-2">*</span>signature != Signature::default())
<a href=#1131 id=1131 data-nosnippet>1131</a>    }
<a href=#1132 id=1132 data-nosnippet>1132</a>}
<a href=#1133 id=1133 data-nosnippet>1133</a>
<a href=#1134 id=1134 data-nosnippet>1134</a><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#1135 id=1135 data-nosnippet>1135</a></span><span class="doccomment">/// Returns true if transaction begins with an advance nonce instruction.
<a href=#1136 id=1136 data-nosnippet>1136</a></span><span class="kw">pub fn </span>uses_durable_nonce(tx: <span class="kw-2">&amp;</span>Transaction) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>CompiledInstruction&gt; {
<a href=#1137 id=1137 data-nosnippet>1137</a>    <span class="kw">let </span>message = tx.message();
<a href=#1138 id=1138 data-nosnippet>1138</a>    message
<a href=#1139 id=1139 data-nosnippet>1139</a>        .instructions
<a href=#1140 id=1140 data-nosnippet>1140</a>        .get(NONCED_TX_MARKER_IX_INDEX <span class="kw">as </span>usize)
<a href=#1141 id=1141 data-nosnippet>1141</a>        .filter(|instruction| {
<a href=#1142 id=1142 data-nosnippet>1142</a>            <span class="comment">// Is system program
<a href=#1143 id=1143 data-nosnippet>1143</a>            </span><span class="macro">matches!</span>(
<a href=#1144 id=1144 data-nosnippet>1144</a>                message.account_keys.get(instruction.program_id_index <span class="kw">as </span>usize),
<a href=#1145 id=1145 data-nosnippet>1145</a>                <span class="prelude-val">Some</span>(program_id) <span class="kw">if </span>system_program::check_id(program_id)
<a href=#1146 id=1146 data-nosnippet>1146</a>            )
<a href=#1147 id=1147 data-nosnippet>1147</a>            <span class="comment">// Is a nonce advance instruction
<a href=#1148 id=1148 data-nosnippet>1148</a>            </span>&amp;&amp; <span class="macro">matches!</span>(
<a href=#1149 id=1149 data-nosnippet>1149</a>                limited_deserialize(<span class="kw-2">&amp;</span>instruction.data, PACKET_DATA_SIZE <span class="kw">as </span>u64),
<a href=#1150 id=1150 data-nosnippet>1150</a>                <span class="prelude-val">Ok</span>(SystemInstruction::AdvanceNonceAccount)
<a href=#1151 id=1151 data-nosnippet>1151</a>            )
<a href=#1152 id=1152 data-nosnippet>1152</a>        })
<a href=#1153 id=1153 data-nosnippet>1153</a>}
<a href=#1154 id=1154 data-nosnippet>1154</a>
<a href=#1155 id=1155 data-nosnippet>1155</a><span class="attr">#[cfg(test)]
<a href=#1156 id=1156 data-nosnippet>1156</a></span><span class="kw">mod </span>tests {
<a href=#1157 id=1157 data-nosnippet>1157</a>    <span class="attr">#![allow(deprecated)]
<a href=#1158 id=1158 data-nosnippet>1158</a>
<a href=#1159 id=1159 data-nosnippet>1159</a>    </span><span class="kw">use </span>{
<a href=#1160 id=1160 data-nosnippet>1160</a>        <span class="kw">super</span>::<span class="kw-2">*</span>,
<a href=#1161 id=1161 data-nosnippet>1161</a>        bincode::{deserialize, serialize, serialized_size},
<a href=#1162 id=1162 data-nosnippet>1162</a>        solana_instruction::AccountMeta,
<a href=#1163 id=1163 data-nosnippet>1163</a>        solana_keypair::Keypair,
<a href=#1164 id=1164 data-nosnippet>1164</a>        solana_presigner::Presigner,
<a href=#1165 id=1165 data-nosnippet>1165</a>        solana_sha256_hasher::hash,
<a href=#1166 id=1166 data-nosnippet>1166</a>        solana_signer::Signer,
<a href=#1167 id=1167 data-nosnippet>1167</a>        solana_system_interface::instruction <span class="kw">as </span>system_instruction,
<a href=#1168 id=1168 data-nosnippet>1168</a>        std::mem::size_of,
<a href=#1169 id=1169 data-nosnippet>1169</a>    };
<a href=#1170 id=1170 data-nosnippet>1170</a>
<a href=#1171 id=1171 data-nosnippet>1171</a>    <span class="kw">fn </span>get_program_id(tx: <span class="kw-2">&amp;</span>Transaction, instruction_index: usize) -&gt; <span class="kw-2">&amp;</span>Pubkey {
<a href=#1172 id=1172 data-nosnippet>1172</a>        <span class="kw">let </span>message = tx.message();
<a href=#1173 id=1173 data-nosnippet>1173</a>        <span class="kw">let </span>instruction = <span class="kw-2">&amp;</span>message.instructions[instruction_index];
<a href=#1174 id=1174 data-nosnippet>1174</a>        instruction.program_id(<span class="kw-2">&amp;</span>message.account_keys)
<a href=#1175 id=1175 data-nosnippet>1175</a>    }
<a href=#1176 id=1176 data-nosnippet>1176</a>
<a href=#1177 id=1177 data-nosnippet>1177</a>    <span class="attr">#[test]
<a href=#1178 id=1178 data-nosnippet>1178</a>    </span><span class="kw">fn </span>test_refs() {
<a href=#1179 id=1179 data-nosnippet>1179</a>        <span class="kw">let </span>key = Keypair::new();
<a href=#1180 id=1180 data-nosnippet>1180</a>        <span class="kw">let </span>key1 = solana_pubkey::new_rand();
<a href=#1181 id=1181 data-nosnippet>1181</a>        <span class="kw">let </span>key2 = solana_pubkey::new_rand();
<a href=#1182 id=1182 data-nosnippet>1182</a>        <span class="kw">let </span>prog1 = solana_pubkey::new_rand();
<a href=#1183 id=1183 data-nosnippet>1183</a>        <span class="kw">let </span>prog2 = solana_pubkey::new_rand();
<a href=#1184 id=1184 data-nosnippet>1184</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[
<a href=#1185 id=1185 data-nosnippet>1185</a>            CompiledInstruction::new(<span class="number">3</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>]),
<a href=#1186 id=1186 data-nosnippet>1186</a>            CompiledInstruction::new(<span class="number">4</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">2</span>]),
<a href=#1187 id=1187 data-nosnippet>1187</a>        ];
<a href=#1188 id=1188 data-nosnippet>1188</a>        <span class="kw">let </span>tx = Transaction::new_with_compiled_instructions(
<a href=#1189 id=1189 data-nosnippet>1189</a>            <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>key],
<a href=#1190 id=1190 data-nosnippet>1190</a>            <span class="kw-2">&amp;</span>[key1, key2],
<a href=#1191 id=1191 data-nosnippet>1191</a>            Hash::default(),
<a href=#1192 id=1192 data-nosnippet>1192</a>            <span class="macro">vec!</span>[prog1, prog2],
<a href=#1193 id=1193 data-nosnippet>1193</a>            instructions,
<a href=#1194 id=1194 data-nosnippet>1194</a>        );
<a href=#1195 id=1195 data-nosnippet>1195</a>        <span class="macro">assert!</span>(tx.sanitize().is_ok());
<a href=#1196 id=1196 data-nosnippet>1196</a>
<a href=#1197 id=1197 data-nosnippet>1197</a>        <span class="macro">assert_eq!</span>(tx.key(<span class="number">0</span>, <span class="number">0</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key.pubkey()));
<a href=#1198 id=1198 data-nosnippet>1198</a>        <span class="macro">assert_eq!</span>(tx.signer_key(<span class="number">0</span>, <span class="number">0</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key.pubkey()));
<a href=#1199 id=1199 data-nosnippet>1199</a>
<a href=#1200 id=1200 data-nosnippet>1200</a>        <span class="macro">assert_eq!</span>(tx.key(<span class="number">1</span>, <span class="number">0</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key.pubkey()));
<a href=#1201 id=1201 data-nosnippet>1201</a>        <span class="macro">assert_eq!</span>(tx.signer_key(<span class="number">1</span>, <span class="number">0</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key.pubkey()));
<a href=#1202 id=1202 data-nosnippet>1202</a>
<a href=#1203 id=1203 data-nosnippet>1203</a>        <span class="macro">assert_eq!</span>(tx.key(<span class="number">0</span>, <span class="number">1</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key1));
<a href=#1204 id=1204 data-nosnippet>1204</a>        <span class="macro">assert_eq!</span>(tx.signer_key(<span class="number">0</span>, <span class="number">1</span>), <span class="prelude-val">None</span>);
<a href=#1205 id=1205 data-nosnippet>1205</a>
<a href=#1206 id=1206 data-nosnippet>1206</a>        <span class="macro">assert_eq!</span>(tx.key(<span class="number">1</span>, <span class="number">1</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key2));
<a href=#1207 id=1207 data-nosnippet>1207</a>        <span class="macro">assert_eq!</span>(tx.signer_key(<span class="number">1</span>, <span class="number">1</span>), <span class="prelude-val">None</span>);
<a href=#1208 id=1208 data-nosnippet>1208</a>
<a href=#1209 id=1209 data-nosnippet>1209</a>        <span class="macro">assert_eq!</span>(tx.key(<span class="number">2</span>, <span class="number">0</span>), <span class="prelude-val">None</span>);
<a href=#1210 id=1210 data-nosnippet>1210</a>        <span class="macro">assert_eq!</span>(tx.signer_key(<span class="number">2</span>, <span class="number">0</span>), <span class="prelude-val">None</span>);
<a href=#1211 id=1211 data-nosnippet>1211</a>
<a href=#1212 id=1212 data-nosnippet>1212</a>        <span class="macro">assert_eq!</span>(tx.key(<span class="number">0</span>, <span class="number">2</span>), <span class="prelude-val">None</span>);
<a href=#1213 id=1213 data-nosnippet>1213</a>        <span class="macro">assert_eq!</span>(tx.signer_key(<span class="number">0</span>, <span class="number">2</span>), <span class="prelude-val">None</span>);
<a href=#1214 id=1214 data-nosnippet>1214</a>
<a href=#1215 id=1215 data-nosnippet>1215</a>        <span class="macro">assert_eq!</span>(<span class="kw-2">*</span>get_program_id(<span class="kw-2">&amp;</span>tx, <span class="number">0</span>), prog1);
<a href=#1216 id=1216 data-nosnippet>1216</a>        <span class="macro">assert_eq!</span>(<span class="kw-2">*</span>get_program_id(<span class="kw-2">&amp;</span>tx, <span class="number">1</span>), prog2);
<a href=#1217 id=1217 data-nosnippet>1217</a>    }
<a href=#1218 id=1218 data-nosnippet>1218</a>
<a href=#1219 id=1219 data-nosnippet>1219</a>    <span class="attr">#[test]
<a href=#1220 id=1220 data-nosnippet>1220</a>    </span><span class="kw">fn </span>test_refs_invalid_program_id() {
<a href=#1221 id=1221 data-nosnippet>1221</a>        <span class="kw">let </span>key = Keypair::new();
<a href=#1222 id=1222 data-nosnippet>1222</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[CompiledInstruction::new(<span class="number">1</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[])];
<a href=#1223 id=1223 data-nosnippet>1223</a>        <span class="kw">let </span>tx = Transaction::new_with_compiled_instructions(
<a href=#1224 id=1224 data-nosnippet>1224</a>            <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>key],
<a href=#1225 id=1225 data-nosnippet>1225</a>            <span class="kw-2">&amp;</span>[],
<a href=#1226 id=1226 data-nosnippet>1226</a>            Hash::default(),
<a href=#1227 id=1227 data-nosnippet>1227</a>            <span class="macro">vec!</span>[],
<a href=#1228 id=1228 data-nosnippet>1228</a>            instructions,
<a href=#1229 id=1229 data-nosnippet>1229</a>        );
<a href=#1230 id=1230 data-nosnippet>1230</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1231 id=1231 data-nosnippet>1231</a>    }
<a href=#1232 id=1232 data-nosnippet>1232</a>    <span class="attr">#[test]
<a href=#1233 id=1233 data-nosnippet>1233</a>    </span><span class="kw">fn </span>test_refs_invalid_account() {
<a href=#1234 id=1234 data-nosnippet>1234</a>        <span class="kw">let </span>key = Keypair::new();
<a href=#1235 id=1235 data-nosnippet>1235</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[CompiledInstruction::new(<span class="number">1</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">2</span>])];
<a href=#1236 id=1236 data-nosnippet>1236</a>        <span class="kw">let </span>tx = Transaction::new_with_compiled_instructions(
<a href=#1237 id=1237 data-nosnippet>1237</a>            <span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>key],
<a href=#1238 id=1238 data-nosnippet>1238</a>            <span class="kw-2">&amp;</span>[],
<a href=#1239 id=1239 data-nosnippet>1239</a>            Hash::default(),
<a href=#1240 id=1240 data-nosnippet>1240</a>            <span class="macro">vec!</span>[Pubkey::default()],
<a href=#1241 id=1241 data-nosnippet>1241</a>            instructions,
<a href=#1242 id=1242 data-nosnippet>1242</a>        );
<a href=#1243 id=1243 data-nosnippet>1243</a>        <span class="macro">assert_eq!</span>(<span class="kw-2">*</span>get_program_id(<span class="kw-2">&amp;</span>tx, <span class="number">0</span>), Pubkey::default());
<a href=#1244 id=1244 data-nosnippet>1244</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1245 id=1245 data-nosnippet>1245</a>    }
<a href=#1246 id=1246 data-nosnippet>1246</a>
<a href=#1247 id=1247 data-nosnippet>1247</a>    <span class="attr">#[test]
<a href=#1248 id=1248 data-nosnippet>1248</a>    </span><span class="kw">fn </span>test_sanitize_txs() {
<a href=#1249 id=1249 data-nosnippet>1249</a>        <span class="kw">let </span>key = Keypair::new();
<a href=#1250 id=1250 data-nosnippet>1250</a>        <span class="kw">let </span>id0 = Pubkey::default();
<a href=#1251 id=1251 data-nosnippet>1251</a>        <span class="kw">let </span>program_id = solana_pubkey::new_rand();
<a href=#1252 id=1252 data-nosnippet>1252</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1253 id=1253 data-nosnippet>1253</a>            program_id,
<a href=#1254 id=1254 data-nosnippet>1254</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1255 id=1255 data-nosnippet>1255</a>            <span class="macro">vec!</span>[
<a href=#1256 id=1256 data-nosnippet>1256</a>                AccountMeta::new(key.pubkey(), <span class="bool-val">true</span>),
<a href=#1257 id=1257 data-nosnippet>1257</a>                AccountMeta::new(id0, <span class="bool-val">true</span>),
<a href=#1258 id=1258 data-nosnippet>1258</a>            ],
<a href=#1259 id=1259 data-nosnippet>1259</a>        );
<a href=#1260 id=1260 data-nosnippet>1260</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_with_payer(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key.pubkey()));
<a href=#1261 id=1261 data-nosnippet>1261</a>        <span class="kw">let </span>o = tx.clone();
<a href=#1262 id=1262 data-nosnippet>1262</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Ok</span>(()));
<a href=#1263 id=1263 data-nosnippet>1263</a>        <span class="macro">assert_eq!</span>(tx.message.account_keys.len(), <span class="number">3</span>);
<a href=#1264 id=1264 data-nosnippet>1264</a>
<a href=#1265 id=1265 data-nosnippet>1265</a>        tx = o.clone();
<a href=#1266 id=1266 data-nosnippet>1266</a>        tx.message.header.num_required_signatures = <span class="number">3</span>;
<a href=#1267 id=1267 data-nosnippet>1267</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1268 id=1268 data-nosnippet>1268</a>
<a href=#1269 id=1269 data-nosnippet>1269</a>        tx = o.clone();
<a href=#1270 id=1270 data-nosnippet>1270</a>        tx.message.header.num_readonly_signed_accounts = <span class="number">4</span>;
<a href=#1271 id=1271 data-nosnippet>1271</a>        tx.message.header.num_readonly_unsigned_accounts = <span class="number">0</span>;
<a href=#1272 id=1272 data-nosnippet>1272</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1273 id=1273 data-nosnippet>1273</a>
<a href=#1274 id=1274 data-nosnippet>1274</a>        tx = o.clone();
<a href=#1275 id=1275 data-nosnippet>1275</a>        tx.message.header.num_readonly_signed_accounts = <span class="number">2</span>;
<a href=#1276 id=1276 data-nosnippet>1276</a>        tx.message.header.num_readonly_unsigned_accounts = <span class="number">2</span>;
<a href=#1277 id=1277 data-nosnippet>1277</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1278 id=1278 data-nosnippet>1278</a>
<a href=#1279 id=1279 data-nosnippet>1279</a>        tx = o.clone();
<a href=#1280 id=1280 data-nosnippet>1280</a>        tx.message.header.num_readonly_signed_accounts = <span class="number">0</span>;
<a href=#1281 id=1281 data-nosnippet>1281</a>        tx.message.header.num_readonly_unsigned_accounts = <span class="number">4</span>;
<a href=#1282 id=1282 data-nosnippet>1282</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1283 id=1283 data-nosnippet>1283</a>
<a href=#1284 id=1284 data-nosnippet>1284</a>        tx = o.clone();
<a href=#1285 id=1285 data-nosnippet>1285</a>        tx.message.instructions[<span class="number">0</span>].program_id_index = <span class="number">3</span>;
<a href=#1286 id=1286 data-nosnippet>1286</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1287 id=1287 data-nosnippet>1287</a>
<a href=#1288 id=1288 data-nosnippet>1288</a>        tx = o.clone();
<a href=#1289 id=1289 data-nosnippet>1289</a>        tx.message.instructions[<span class="number">0</span>].accounts[<span class="number">0</span>] = <span class="number">3</span>;
<a href=#1290 id=1290 data-nosnippet>1290</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1291 id=1291 data-nosnippet>1291</a>
<a href=#1292 id=1292 data-nosnippet>1292</a>        tx = o.clone();
<a href=#1293 id=1293 data-nosnippet>1293</a>        tx.message.instructions[<span class="number">0</span>].program_id_index = <span class="number">0</span>;
<a href=#1294 id=1294 data-nosnippet>1294</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1295 id=1295 data-nosnippet>1295</a>
<a href=#1296 id=1296 data-nosnippet>1296</a>        tx = o.clone();
<a href=#1297 id=1297 data-nosnippet>1297</a>        tx.message.header.num_readonly_signed_accounts = <span class="number">2</span>;
<a href=#1298 id=1298 data-nosnippet>1298</a>        tx.message.header.num_readonly_unsigned_accounts = <span class="number">3</span>;
<a href=#1299 id=1299 data-nosnippet>1299</a>        tx.message.account_keys.resize(<span class="number">4</span>, Pubkey::default());
<a href=#1300 id=1300 data-nosnippet>1300</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1301 id=1301 data-nosnippet>1301</a>
<a href=#1302 id=1302 data-nosnippet>1302</a>        tx = o;
<a href=#1303 id=1303 data-nosnippet>1303</a>        tx.message.header.num_readonly_signed_accounts = <span class="number">2</span>;
<a href=#1304 id=1304 data-nosnippet>1304</a>        tx.message.header.num_required_signatures = <span class="number">1</span>;
<a href=#1305 id=1305 data-nosnippet>1305</a>        <span class="macro">assert_eq!</span>(tx.sanitize(), <span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds));
<a href=#1306 id=1306 data-nosnippet>1306</a>    }
<a href=#1307 id=1307 data-nosnippet>1307</a>
<a href=#1308 id=1308 data-nosnippet>1308</a>    <span class="kw">fn </span>create_sample_transaction() -&gt; Transaction {
<a href=#1309 id=1309 data-nosnippet>1309</a>        <span class="kw">let </span>keypair = Keypair::from_bytes(<span class="kw-2">&amp;</span>[
<a href=#1310 id=1310 data-nosnippet>1310</a>            <span class="number">255</span>, <span class="number">101</span>, <span class="number">36</span>, <span class="number">24</span>, <span class="number">124</span>, <span class="number">23</span>, <span class="number">167</span>, <span class="number">21</span>, <span class="number">132</span>, <span class="number">204</span>, <span class="number">155</span>, <span class="number">5</span>, <span class="number">185</span>, <span class="number">58</span>, <span class="number">121</span>, <span class="number">75</span>, <span class="number">156</span>, <span class="number">227</span>, <span class="number">116</span>,
<a href=#1311 id=1311 data-nosnippet>1311</a>            <span class="number">193</span>, <span class="number">215</span>, <span class="number">38</span>, <span class="number">142</span>, <span class="number">22</span>, <span class="number">8</span>, <span class="number">14</span>, <span class="number">229</span>, <span class="number">239</span>, <span class="number">119</span>, <span class="number">93</span>, <span class="number">5</span>, <span class="number">218</span>, <span class="number">36</span>, <span class="number">100</span>, <span class="number">158</span>, <span class="number">252</span>, <span class="number">33</span>, <span class="number">161</span>,
<a href=#1312 id=1312 data-nosnippet>1312</a>            <span class="number">97</span>, <span class="number">185</span>, <span class="number">62</span>, <span class="number">89</span>, <span class="number">99</span>, <span class="number">195</span>, <span class="number">250</span>, <span class="number">249</span>, <span class="number">187</span>, <span class="number">189</span>, <span class="number">171</span>, <span class="number">118</span>, <span class="number">241</span>, <span class="number">90</span>, <span class="number">248</span>, <span class="number">14</span>, <span class="number">68</span>, <span class="number">219</span>, <span class="number">231</span>,
<a href=#1313 id=1313 data-nosnippet>1313</a>            <span class="number">62</span>, <span class="number">157</span>, <span class="number">5</span>, <span class="number">142</span>, <span class="number">27</span>, <span class="number">210</span>, <span class="number">117</span>,
<a href=#1314 id=1314 data-nosnippet>1314</a>        ])
<a href=#1315 id=1315 data-nosnippet>1315</a>        .unwrap();
<a href=#1316 id=1316 data-nosnippet>1316</a>        <span class="kw">let </span>to = Pubkey::from([
<a href=#1317 id=1317 data-nosnippet>1317</a>            <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">8</span>, <span class="number">7</span>, <span class="number">6</span>, <span class="number">5</span>, <span class="number">4</span>,
<a href=#1318 id=1318 data-nosnippet>1318</a>            <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>,
<a href=#1319 id=1319 data-nosnippet>1319</a>        ]);
<a href=#1320 id=1320 data-nosnippet>1320</a>
<a href=#1321 id=1321 data-nosnippet>1321</a>        <span class="kw">let </span>program_id = Pubkey::from([
<a href=#1322 id=1322 data-nosnippet>1322</a>            <span class="number">2</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">9</span>, <span class="number">8</span>, <span class="number">7</span>, <span class="number">6</span>, <span class="number">5</span>, <span class="number">4</span>,
<a href=#1323 id=1323 data-nosnippet>1323</a>            <span class="number">2</span>, <span class="number">2</span>, <span class="number">2</span>,
<a href=#1324 id=1324 data-nosnippet>1324</a>        ]);
<a href=#1325 id=1325 data-nosnippet>1325</a>        <span class="kw">let </span>account_metas = <span class="macro">vec!</span>[
<a href=#1326 id=1326 data-nosnippet>1326</a>            AccountMeta::new(keypair.pubkey(), <span class="bool-val">true</span>),
<a href=#1327 id=1327 data-nosnippet>1327</a>            AccountMeta::new(to, <span class="bool-val">false</span>),
<a href=#1328 id=1328 data-nosnippet>1328</a>        ];
<a href=#1329 id=1329 data-nosnippet>1329</a>        <span class="kw">let </span>instruction =
<a href=#1330 id=1330 data-nosnippet>1330</a>            Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span>(<span class="number">1u8</span>, <span class="number">2u8</span>, <span class="number">3u8</span>), account_metas);
<a href=#1331 id=1331 data-nosnippet>1331</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[instruction], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keypair.pubkey()));
<a href=#1332 id=1332 data-nosnippet>1332</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair], message, Hash::default());
<a href=#1333 id=1333 data-nosnippet>1333</a>        tx.verify().expect(<span class="string">"valid sample transaction signatures"</span>);
<a href=#1334 id=1334 data-nosnippet>1334</a>        tx
<a href=#1335 id=1335 data-nosnippet>1335</a>    }
<a href=#1336 id=1336 data-nosnippet>1336</a>
<a href=#1337 id=1337 data-nosnippet>1337</a>    <span class="attr">#[test]
<a href=#1338 id=1338 data-nosnippet>1338</a>    </span><span class="kw">fn </span>test_transaction_serialize() {
<a href=#1339 id=1339 data-nosnippet>1339</a>        <span class="kw">let </span>tx = create_sample_transaction();
<a href=#1340 id=1340 data-nosnippet>1340</a>        <span class="kw">let </span>ser = serialize(<span class="kw-2">&amp;</span>tx).unwrap();
<a href=#1341 id=1341 data-nosnippet>1341</a>        <span class="kw">let </span>deser = deserialize(<span class="kw-2">&amp;</span>ser).unwrap();
<a href=#1342 id=1342 data-nosnippet>1342</a>        <span class="macro">assert_eq!</span>(tx, deser);
<a href=#1343 id=1343 data-nosnippet>1343</a>    }
<a href=#1344 id=1344 data-nosnippet>1344</a>
<a href=#1345 id=1345 data-nosnippet>1345</a>    <span class="doccomment">/// Detect changes to the serialized size of payment transactions, which affects TPS.
<a href=#1346 id=1346 data-nosnippet>1346</a>    </span><span class="attr">#[test]
<a href=#1347 id=1347 data-nosnippet>1347</a>    </span><span class="kw">fn </span>test_transaction_minimum_serialized_size() {
<a href=#1348 id=1348 data-nosnippet>1348</a>        <span class="kw">let </span>alice_keypair = Keypair::new();
<a href=#1349 id=1349 data-nosnippet>1349</a>        <span class="kw">let </span>alice_pubkey = alice_keypair.pubkey();
<a href=#1350 id=1350 data-nosnippet>1350</a>        <span class="kw">let </span>bob_pubkey = solana_pubkey::new_rand();
<a href=#1351 id=1351 data-nosnippet>1351</a>        <span class="kw">let </span>ix = system_instruction::transfer(<span class="kw-2">&amp;</span>alice_pubkey, <span class="kw-2">&amp;</span>bob_pubkey, <span class="number">42</span>);
<a href=#1352 id=1352 data-nosnippet>1352</a>
<a href=#1353 id=1353 data-nosnippet>1353</a>        <span class="kw">let </span>expected_data_size = size_of::&lt;u32&gt;() + size_of::&lt;u64&gt;();
<a href=#1354 id=1354 data-nosnippet>1354</a>        <span class="macro">assert_eq!</span>(expected_data_size, <span class="number">12</span>);
<a href=#1355 id=1355 data-nosnippet>1355</a>        <span class="macro">assert_eq!</span>(
<a href=#1356 id=1356 data-nosnippet>1356</a>            ix.data.len(),
<a href=#1357 id=1357 data-nosnippet>1357</a>            expected_data_size,
<a href=#1358 id=1358 data-nosnippet>1358</a>            <span class="string">"unexpected system instruction size"
<a href=#1359 id=1359 data-nosnippet>1359</a>        </span>);
<a href=#1360 id=1360 data-nosnippet>1360</a>
<a href=#1361 id=1361 data-nosnippet>1361</a>        <span class="kw">let </span>expected_instruction_size = <span class="number">1 </span>+ <span class="number">1 </span>+ ix.accounts.len() + <span class="number">1 </span>+ expected_data_size;
<a href=#1362 id=1362 data-nosnippet>1362</a>        <span class="macro">assert_eq!</span>(expected_instruction_size, <span class="number">17</span>);
<a href=#1363 id=1363 data-nosnippet>1363</a>
<a href=#1364 id=1364 data-nosnippet>1364</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>alice_pubkey));
<a href=#1365 id=1365 data-nosnippet>1365</a>        <span class="macro">assert_eq!</span>(
<a href=#1366 id=1366 data-nosnippet>1366</a>            serialized_size(<span class="kw-2">&amp;</span>message.instructions[<span class="number">0</span>]).unwrap() <span class="kw">as </span>usize,
<a href=#1367 id=1367 data-nosnippet>1367</a>            expected_instruction_size,
<a href=#1368 id=1368 data-nosnippet>1368</a>            <span class="string">"unexpected Instruction::serialized_size"
<a href=#1369 id=1369 data-nosnippet>1369</a>        </span>);
<a href=#1370 id=1370 data-nosnippet>1370</a>
<a href=#1371 id=1371 data-nosnippet>1371</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>alice_keypair], message, Hash::default());
<a href=#1372 id=1372 data-nosnippet>1372</a>
<a href=#1373 id=1373 data-nosnippet>1373</a>        <span class="kw">let </span>len_size = <span class="number">1</span>;
<a href=#1374 id=1374 data-nosnippet>1374</a>        <span class="kw">let </span>num_required_sigs_size = <span class="number">1</span>;
<a href=#1375 id=1375 data-nosnippet>1375</a>        <span class="kw">let </span>num_readonly_accounts_size = <span class="number">2</span>;
<a href=#1376 id=1376 data-nosnippet>1376</a>        <span class="kw">let </span>blockhash_size = size_of::&lt;Hash&gt;();
<a href=#1377 id=1377 data-nosnippet>1377</a>        <span class="kw">let </span>expected_transaction_size = len_size
<a href=#1378 id=1378 data-nosnippet>1378</a>            + (tx.signatures.len() * size_of::&lt;Signature&gt;())
<a href=#1379 id=1379 data-nosnippet>1379</a>            + num_required_sigs_size
<a href=#1380 id=1380 data-nosnippet>1380</a>            + num_readonly_accounts_size
<a href=#1381 id=1381 data-nosnippet>1381</a>            + len_size
<a href=#1382 id=1382 data-nosnippet>1382</a>            + (tx.message.account_keys.len() * size_of::&lt;Pubkey&gt;())
<a href=#1383 id=1383 data-nosnippet>1383</a>            + blockhash_size
<a href=#1384 id=1384 data-nosnippet>1384</a>            + len_size
<a href=#1385 id=1385 data-nosnippet>1385</a>            + expected_instruction_size;
<a href=#1386 id=1386 data-nosnippet>1386</a>        <span class="macro">assert_eq!</span>(expected_transaction_size, <span class="number">215</span>);
<a href=#1387 id=1387 data-nosnippet>1387</a>
<a href=#1388 id=1388 data-nosnippet>1388</a>        <span class="macro">assert_eq!</span>(
<a href=#1389 id=1389 data-nosnippet>1389</a>            serialized_size(<span class="kw-2">&amp;</span>tx).unwrap() <span class="kw">as </span>usize,
<a href=#1390 id=1390 data-nosnippet>1390</a>            expected_transaction_size,
<a href=#1391 id=1391 data-nosnippet>1391</a>            <span class="string">"unexpected serialized transaction size"
<a href=#1392 id=1392 data-nosnippet>1392</a>        </span>);
<a href=#1393 id=1393 data-nosnippet>1393</a>    }
<a href=#1394 id=1394 data-nosnippet>1394</a>
<a href=#1395 id=1395 data-nosnippet>1395</a>    <span class="doccomment">/// Detect binary changes in the serialized transaction data, which could have a downstream
<a href=#1396 id=1396 data-nosnippet>1396</a>    /// affect on SDKs and applications
<a href=#1397 id=1397 data-nosnippet>1397</a>    </span><span class="attr">#[test]
<a href=#1398 id=1398 data-nosnippet>1398</a>    </span><span class="kw">fn </span>test_sdk_serialize() {
<a href=#1399 id=1399 data-nosnippet>1399</a>        <span class="macro">assert_eq!</span>(
<a href=#1400 id=1400 data-nosnippet>1400</a>            serialize(<span class="kw-2">&amp;</span>create_sample_transaction()).unwrap(),
<a href=#1401 id=1401 data-nosnippet>1401</a>            <span class="macro">vec!</span>[
<a href=#1402 id=1402 data-nosnippet>1402</a>                <span class="number">1</span>, <span class="number">120</span>, <span class="number">138</span>, <span class="number">162</span>, <span class="number">185</span>, <span class="number">59</span>, <span class="number">209</span>, <span class="number">241</span>, <span class="number">157</span>, <span class="number">71</span>, <span class="number">157</span>, <span class="number">74</span>, <span class="number">131</span>, <span class="number">4</span>, <span class="number">87</span>, <span class="number">54</span>, <span class="number">28</span>, <span class="number">38</span>, <span class="number">180</span>,
<a href=#1403 id=1403 data-nosnippet>1403</a>                <span class="number">222</span>, <span class="number">82</span>, <span class="number">64</span>, <span class="number">62</span>, <span class="number">61</span>, <span class="number">62</span>, <span class="number">22</span>, <span class="number">46</span>, <span class="number">17</span>, <span class="number">203</span>, <span class="number">187</span>, <span class="number">136</span>, <span class="number">62</span>, <span class="number">43</span>, <span class="number">11</span>, <span class="number">38</span>, <span class="number">235</span>, <span class="number">17</span>, <span class="number">239</span>,
<a href=#1404 id=1404 data-nosnippet>1404</a>                <span class="number">82</span>, <span class="number">240</span>, <span class="number">139</span>, <span class="number">130</span>, <span class="number">217</span>, <span class="number">227</span>, <span class="number">214</span>, <span class="number">9</span>, <span class="number">242</span>, <span class="number">141</span>, <span class="number">223</span>, <span class="number">94</span>, <span class="number">29</span>, <span class="number">184</span>, <span class="number">110</span>, <span class="number">62</span>, <span class="number">32</span>, <span class="number">87</span>,
<a href=#1405 id=1405 data-nosnippet>1405</a>                <span class="number">137</span>, <span class="number">63</span>, <span class="number">139</span>, <span class="number">100</span>, <span class="number">221</span>, <span class="number">20</span>, <span class="number">137</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">1</span>, <span class="number">0</span>, <span class="number">1</span>, <span class="number">3</span>, <span class="number">36</span>, <span class="number">100</span>, <span class="number">158</span>, <span class="number">252</span>, <span class="number">33</span>, <span class="number">161</span>, <span class="number">97</span>,
<a href=#1406 id=1406 data-nosnippet>1406</a>                <span class="number">185</span>, <span class="number">62</span>, <span class="number">89</span>, <span class="number">99</span>, <span class="number">195</span>, <span class="number">250</span>, <span class="number">249</span>, <span class="number">187</span>, <span class="number">189</span>, <span class="number">171</span>, <span class="number">118</span>, <span class="number">241</span>, <span class="number">90</span>, <span class="number">248</span>, <span class="number">14</span>, <span class="number">68</span>, <span class="number">219</span>, <span class="number">231</span>,
<a href=#1407 id=1407 data-nosnippet>1407</a>                <span class="number">62</span>, <span class="number">157</span>, <span class="number">5</span>, <span class="number">142</span>, <span class="number">27</span>, <span class="number">210</span>, <span class="number">117</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>,
<a href=#1408 id=1408 data-nosnippet>1408</a>                <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">9</span>, <span class="number">8</span>, <span class="number">7</span>, <span class="number">6</span>, <span class="number">5</span>, <span class="number">4</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>,
<a href=#1409 id=1409 data-nosnippet>1409</a>                <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span>, <span class="number">9</span>, <span class="number">8</span>, <span class="number">7</span>, <span class="number">6</span>, <span class="number">5</span>, <span class="number">4</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>,
<a href=#1410 id=1410 data-nosnippet>1410</a>                <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">0</span>, <span class="number">1</span>,
<a href=#1411 id=1411 data-nosnippet>1411</a>                <span class="number">3</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3
<a href=#1412 id=1412 data-nosnippet>1412</a>            </span>]
<a href=#1413 id=1413 data-nosnippet>1413</a>        );
<a href=#1414 id=1414 data-nosnippet>1414</a>    }
<a href=#1415 id=1415 data-nosnippet>1415</a>
<a href=#1416 id=1416 data-nosnippet>1416</a>    <span class="attr">#[test]
<a href=#1417 id=1417 data-nosnippet>1417</a>    #[should_panic]
<a href=#1418 id=1418 data-nosnippet>1418</a>    </span><span class="kw">fn </span>test_transaction_missing_key() {
<a href=#1419 id=1419 data-nosnippet>1419</a>        <span class="kw">let </span>keypair = Keypair::new();
<a href=#1420 id=1420 data-nosnippet>1420</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[], <span class="prelude-val">None</span>);
<a href=#1421 id=1421 data-nosnippet>1421</a>        Transaction::new_unsigned(message).sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair], Hash::default());
<a href=#1422 id=1422 data-nosnippet>1422</a>    }
<a href=#1423 id=1423 data-nosnippet>1423</a>
<a href=#1424 id=1424 data-nosnippet>1424</a>    <span class="attr">#[test]
<a href=#1425 id=1425 data-nosnippet>1425</a>    #[should_panic]
<a href=#1426 id=1426 data-nosnippet>1426</a>    </span><span class="kw">fn </span>test_partial_sign_mismatched_key() {
<a href=#1427 id=1427 data-nosnippet>1427</a>        <span class="kw">let </span>keypair = Keypair::new();
<a href=#1428 id=1428 data-nosnippet>1428</a>        <span class="kw">let </span>fee_payer = solana_pubkey::new_rand();
<a href=#1429 id=1429 data-nosnippet>1429</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1430 id=1430 data-nosnippet>1430</a>            Pubkey::default(),
<a href=#1431 id=1431 data-nosnippet>1431</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1432 id=1432 data-nosnippet>1432</a>            <span class="macro">vec!</span>[AccountMeta::new(fee_payer, <span class="bool-val">true</span>)],
<a href=#1433 id=1433 data-nosnippet>1433</a>        );
<a href=#1434 id=1434 data-nosnippet>1434</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>fee_payer));
<a href=#1435 id=1435 data-nosnippet>1435</a>        Transaction::new_unsigned(message).partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair], Hash::default());
<a href=#1436 id=1436 data-nosnippet>1436</a>    }
<a href=#1437 id=1437 data-nosnippet>1437</a>
<a href=#1438 id=1438 data-nosnippet>1438</a>    <span class="attr">#[test]
<a href=#1439 id=1439 data-nosnippet>1439</a>    </span><span class="kw">fn </span>test_partial_sign() {
<a href=#1440 id=1440 data-nosnippet>1440</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#1441 id=1441 data-nosnippet>1441</a>        <span class="kw">let </span>keypair1 = Keypair::new();
<a href=#1442 id=1442 data-nosnippet>1442</a>        <span class="kw">let </span>keypair2 = Keypair::new();
<a href=#1443 id=1443 data-nosnippet>1443</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1444 id=1444 data-nosnippet>1444</a>            Pubkey::default(),
<a href=#1445 id=1445 data-nosnippet>1445</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1446 id=1446 data-nosnippet>1446</a>            <span class="macro">vec!</span>[
<a href=#1447 id=1447 data-nosnippet>1447</a>                AccountMeta::new(keypair0.pubkey(), <span class="bool-val">true</span>),
<a href=#1448 id=1448 data-nosnippet>1448</a>                AccountMeta::new(keypair1.pubkey(), <span class="bool-val">true</span>),
<a href=#1449 id=1449 data-nosnippet>1449</a>                AccountMeta::new(keypair2.pubkey(), <span class="bool-val">true</span>),
<a href=#1450 id=1450 data-nosnippet>1450</a>            ],
<a href=#1451 id=1451 data-nosnippet>1451</a>        );
<a href=#1452 id=1452 data-nosnippet>1452</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keypair0.pubkey()));
<a href=#1453 id=1453 data-nosnippet>1453</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
<a href=#1454 id=1454 data-nosnippet>1454</a>
<a href=#1455 id=1455 data-nosnippet>1455</a>        tx.partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0, <span class="kw-2">&amp;</span>keypair2], Hash::default());
<a href=#1456 id=1456 data-nosnippet>1456</a>        <span class="macro">assert!</span>(!tx.is_signed());
<a href=#1457 id=1457 data-nosnippet>1457</a>        tx.partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair1], Hash::default());
<a href=#1458 id=1458 data-nosnippet>1458</a>        <span class="macro">assert!</span>(tx.is_signed());
<a href=#1459 id=1459 data-nosnippet>1459</a>
<a href=#1460 id=1460 data-nosnippet>1460</a>        <span class="kw">let </span>hash = hash(<span class="kw-2">&amp;</span>[<span class="number">1</span>]);
<a href=#1461 id=1461 data-nosnippet>1461</a>        tx.partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair1], hash);
<a href=#1462 id=1462 data-nosnippet>1462</a>        <span class="macro">assert!</span>(!tx.is_signed());
<a href=#1463 id=1463 data-nosnippet>1463</a>        tx.partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0, <span class="kw-2">&amp;</span>keypair2], hash);
<a href=#1464 id=1464 data-nosnippet>1464</a>        <span class="macro">assert!</span>(tx.is_signed());
<a href=#1465 id=1465 data-nosnippet>1465</a>    }
<a href=#1466 id=1466 data-nosnippet>1466</a>
<a href=#1467 id=1467 data-nosnippet>1467</a>    <span class="attr">#[test]
<a href=#1468 id=1468 data-nosnippet>1468</a>    #[should_panic]
<a href=#1469 id=1469 data-nosnippet>1469</a>    </span><span class="kw">fn </span>test_transaction_missing_keypair() {
<a href=#1470 id=1470 data-nosnippet>1470</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#1471 id=1471 data-nosnippet>1471</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#1472 id=1472 data-nosnippet>1472</a>        <span class="kw">let </span>id0 = keypair0.pubkey();
<a href=#1473 id=1473 data-nosnippet>1473</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">true</span>)]);
<a href=#1474 id=1474 data-nosnippet>1474</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id0));
<a href=#1475 id=1475 data-nosnippet>1475</a>        Transaction::new_unsigned(message).sign(<span class="kw-2">&amp;</span>Vec::&lt;<span class="kw-2">&amp;</span>Keypair&gt;::new(), Hash::default());
<a href=#1476 id=1476 data-nosnippet>1476</a>    }
<a href=#1477 id=1477 data-nosnippet>1477</a>
<a href=#1478 id=1478 data-nosnippet>1478</a>    <span class="attr">#[test]
<a href=#1479 id=1479 data-nosnippet>1479</a>    #[should_panic]
<a href=#1480 id=1480 data-nosnippet>1480</a>    </span><span class="kw">fn </span>test_transaction_wrong_key() {
<a href=#1481 id=1481 data-nosnippet>1481</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#1482 id=1482 data-nosnippet>1482</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#1483 id=1483 data-nosnippet>1483</a>        <span class="kw">let </span>wrong_id = Pubkey::default();
<a href=#1484 id=1484 data-nosnippet>1484</a>        <span class="kw">let </span>ix =
<a href=#1485 id=1485 data-nosnippet>1485</a>            Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(wrong_id, <span class="bool-val">true</span>)]);
<a href=#1486 id=1486 data-nosnippet>1486</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>wrong_id));
<a href=#1487 id=1487 data-nosnippet>1487</a>        Transaction::new_unsigned(message).sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0], Hash::default());
<a href=#1488 id=1488 data-nosnippet>1488</a>    }
<a href=#1489 id=1489 data-nosnippet>1489</a>
<a href=#1490 id=1490 data-nosnippet>1490</a>    <span class="attr">#[test]
<a href=#1491 id=1491 data-nosnippet>1491</a>    </span><span class="kw">fn </span>test_transaction_correct_key() {
<a href=#1492 id=1492 data-nosnippet>1492</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#1493 id=1493 data-nosnippet>1493</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#1494 id=1494 data-nosnippet>1494</a>        <span class="kw">let </span>id0 = keypair0.pubkey();
<a href=#1495 id=1495 data-nosnippet>1495</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">true</span>)]);
<a href=#1496 id=1496 data-nosnippet>1496</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id0));
<a href=#1497 id=1497 data-nosnippet>1497</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
<a href=#1498 id=1498 data-nosnippet>1498</a>        tx.sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0], Hash::default());
<a href=#1499 id=1499 data-nosnippet>1499</a>        <span class="macro">assert_eq!</span>(
<a href=#1500 id=1500 data-nosnippet>1500</a>            tx.message.instructions[<span class="number">0</span>],
<a href=#1501 id=1501 data-nosnippet>1501</a>            CompiledInstruction::new(<span class="number">1</span>, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[<span class="number">0</span>])
<a href=#1502 id=1502 data-nosnippet>1502</a>        );
<a href=#1503 id=1503 data-nosnippet>1503</a>        <span class="macro">assert!</span>(tx.is_signed());
<a href=#1504 id=1504 data-nosnippet>1504</a>    }
<a href=#1505 id=1505 data-nosnippet>1505</a>
<a href=#1506 id=1506 data-nosnippet>1506</a>    <span class="attr">#[test]
<a href=#1507 id=1507 data-nosnippet>1507</a>    </span><span class="kw">fn </span>test_transaction_instruction_with_duplicate_keys() {
<a href=#1508 id=1508 data-nosnippet>1508</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#1509 id=1509 data-nosnippet>1509</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#1510 id=1510 data-nosnippet>1510</a>        <span class="kw">let </span>id0 = keypair0.pubkey();
<a href=#1511 id=1511 data-nosnippet>1511</a>        <span class="kw">let </span>id1 = solana_pubkey::new_rand();
<a href=#1512 id=1512 data-nosnippet>1512</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1513 id=1513 data-nosnippet>1513</a>            program_id,
<a href=#1514 id=1514 data-nosnippet>1514</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1515 id=1515 data-nosnippet>1515</a>            <span class="macro">vec!</span>[
<a href=#1516 id=1516 data-nosnippet>1516</a>                AccountMeta::new(id0, <span class="bool-val">true</span>),
<a href=#1517 id=1517 data-nosnippet>1517</a>                AccountMeta::new(id1, <span class="bool-val">false</span>),
<a href=#1518 id=1518 data-nosnippet>1518</a>                AccountMeta::new(id0, <span class="bool-val">false</span>),
<a href=#1519 id=1519 data-nosnippet>1519</a>                AccountMeta::new(id1, <span class="bool-val">false</span>),
<a href=#1520 id=1520 data-nosnippet>1520</a>            ],
<a href=#1521 id=1521 data-nosnippet>1521</a>        );
<a href=#1522 id=1522 data-nosnippet>1522</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id0));
<a href=#1523 id=1523 data-nosnippet>1523</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
<a href=#1524 id=1524 data-nosnippet>1524</a>        tx.sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0], Hash::default());
<a href=#1525 id=1525 data-nosnippet>1525</a>        <span class="macro">assert_eq!</span>(
<a href=#1526 id=1526 data-nosnippet>1526</a>            tx.message.instructions[<span class="number">0</span>],
<a href=#1527 id=1527 data-nosnippet>1527</a>            CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>, <span class="number">0</span>, <span class="number">1</span>])
<a href=#1528 id=1528 data-nosnippet>1528</a>        );
<a href=#1529 id=1529 data-nosnippet>1529</a>        <span class="macro">assert!</span>(tx.is_signed());
<a href=#1530 id=1530 data-nosnippet>1530</a>    }
<a href=#1531 id=1531 data-nosnippet>1531</a>
<a href=#1532 id=1532 data-nosnippet>1532</a>    <span class="attr">#[test]
<a href=#1533 id=1533 data-nosnippet>1533</a>    </span><span class="kw">fn </span>test_try_sign_dyn_keypairs() {
<a href=#1534 id=1534 data-nosnippet>1534</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#1535 id=1535 data-nosnippet>1535</a>        <span class="kw">let </span>keypair = Keypair::new();
<a href=#1536 id=1536 data-nosnippet>1536</a>        <span class="kw">let </span>pubkey = keypair.pubkey();
<a href=#1537 id=1537 data-nosnippet>1537</a>        <span class="kw">let </span>presigner_keypair = Keypair::new();
<a href=#1538 id=1538 data-nosnippet>1538</a>        <span class="kw">let </span>presigner_pubkey = presigner_keypair.pubkey();
<a href=#1539 id=1539 data-nosnippet>1539</a>
<a href=#1540 id=1540 data-nosnippet>1540</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1541 id=1541 data-nosnippet>1541</a>            program_id,
<a href=#1542 id=1542 data-nosnippet>1542</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1543 id=1543 data-nosnippet>1543</a>            <span class="macro">vec!</span>[
<a href=#1544 id=1544 data-nosnippet>1544</a>                AccountMeta::new(pubkey, <span class="bool-val">true</span>),
<a href=#1545 id=1545 data-nosnippet>1545</a>                AccountMeta::new(presigner_pubkey, <span class="bool-val">true</span>),
<a href=#1546 id=1546 data-nosnippet>1546</a>            ],
<a href=#1547 id=1547 data-nosnippet>1547</a>        );
<a href=#1548 id=1548 data-nosnippet>1548</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>pubkey));
<a href=#1549 id=1549 data-nosnippet>1549</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
<a href=#1550 id=1550 data-nosnippet>1550</a>
<a href=#1551 id=1551 data-nosnippet>1551</a>        <span class="kw">let </span>presigner_sig = presigner_keypair.sign_message(<span class="kw-2">&amp;</span>tx.message_data());
<a href=#1552 id=1552 data-nosnippet>1552</a>        <span class="kw">let </span>presigner = Presigner::new(<span class="kw-2">&amp;</span>presigner_pubkey, <span class="kw-2">&amp;</span>presigner_sig);
<a href=#1553 id=1553 data-nosnippet>1553</a>
<a href=#1554 id=1554 data-nosnippet>1554</a>        <span class="kw">let </span>signers: Vec&lt;<span class="kw-2">&amp;</span><span class="kw">dyn </span>Signer&gt; = <span class="macro">vec!</span>[<span class="kw-2">&amp;</span>keypair, <span class="kw-2">&amp;</span>presigner];
<a href=#1555 id=1555 data-nosnippet>1555</a>
<a href=#1556 id=1556 data-nosnippet>1556</a>        <span class="kw">let </span>res = tx.try_sign(<span class="kw-2">&amp;</span>signers, Hash::default());
<a href=#1557 id=1557 data-nosnippet>1557</a>        <span class="macro">assert_eq!</span>(res, <span class="prelude-val">Ok</span>(()));
<a href=#1558 id=1558 data-nosnippet>1558</a>        <span class="macro">assert_eq!</span>(tx.signatures[<span class="number">0</span>], keypair.sign_message(<span class="kw-2">&amp;</span>tx.message_data()));
<a href=#1559 id=1559 data-nosnippet>1559</a>        <span class="macro">assert_eq!</span>(tx.signatures[<span class="number">1</span>], presigner_sig);
<a href=#1560 id=1560 data-nosnippet>1560</a>
<a href=#1561 id=1561 data-nosnippet>1561</a>        <span class="comment">// Wrong key should error, not panic
<a href=#1562 id=1562 data-nosnippet>1562</a>        </span><span class="kw">let </span>another_pubkey = solana_pubkey::new_rand();
<a href=#1563 id=1563 data-nosnippet>1563</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1564 id=1564 data-nosnippet>1564</a>            program_id,
<a href=#1565 id=1565 data-nosnippet>1565</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1566 id=1566 data-nosnippet>1566</a>            <span class="macro">vec!</span>[
<a href=#1567 id=1567 data-nosnippet>1567</a>                AccountMeta::new(another_pubkey, <span class="bool-val">true</span>),
<a href=#1568 id=1568 data-nosnippet>1568</a>                AccountMeta::new(presigner_pubkey, <span class="bool-val">true</span>),
<a href=#1569 id=1569 data-nosnippet>1569</a>            ],
<a href=#1570 id=1570 data-nosnippet>1570</a>        );
<a href=#1571 id=1571 data-nosnippet>1571</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>another_pubkey));
<a href=#1572 id=1572 data-nosnippet>1572</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
<a href=#1573 id=1573 data-nosnippet>1573</a>
<a href=#1574 id=1574 data-nosnippet>1574</a>        <span class="kw">let </span>res = tx.try_sign(<span class="kw-2">&amp;</span>signers, Hash::default());
<a href=#1575 id=1575 data-nosnippet>1575</a>        <span class="macro">assert!</span>(res.is_err());
<a href=#1576 id=1576 data-nosnippet>1576</a>        <span class="macro">assert_eq!</span>(
<a href=#1577 id=1577 data-nosnippet>1577</a>            tx.signatures,
<a href=#1578 id=1578 data-nosnippet>1578</a>            <span class="macro">vec!</span>[Signature::default(), Signature::default()]
<a href=#1579 id=1579 data-nosnippet>1579</a>        );
<a href=#1580 id=1580 data-nosnippet>1580</a>    }
<a href=#1581 id=1581 data-nosnippet>1581</a>
<a href=#1582 id=1582 data-nosnippet>1582</a>    <span class="kw">fn </span>nonced_transfer_tx() -&gt; (Pubkey, Pubkey, Transaction) {
<a href=#1583 id=1583 data-nosnippet>1583</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#1584 id=1584 data-nosnippet>1584</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#1585 id=1585 data-nosnippet>1585</a>        <span class="kw">let </span>nonce_keypair = Keypair::new();
<a href=#1586 id=1586 data-nosnippet>1586</a>        <span class="kw">let </span>nonce_pubkey = nonce_keypair.pubkey();
<a href=#1587 id=1587 data-nosnippet>1587</a>        <span class="kw">let </span>instructions = [
<a href=#1588 id=1588 data-nosnippet>1588</a>            system_instruction::advance_nonce_account(<span class="kw-2">&amp;</span>nonce_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey),
<a href=#1589 id=1589 data-nosnippet>1589</a>            system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey, <span class="number">42</span>),
<a href=#1590 id=1590 data-nosnippet>1590</a>        ];
<a href=#1591 id=1591 data-nosnippet>1591</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>nonce_pubkey));
<a href=#1592 id=1592 data-nosnippet>1592</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>nonce_keypair], message, Hash::default());
<a href=#1593 id=1593 data-nosnippet>1593</a>        (from_pubkey, nonce_pubkey, tx)
<a href=#1594 id=1594 data-nosnippet>1594</a>    }
<a href=#1595 id=1595 data-nosnippet>1595</a>
<a href=#1596 id=1596 data-nosnippet>1596</a>    <span class="attr">#[test]
<a href=#1597 id=1597 data-nosnippet>1597</a>    </span><span class="kw">fn </span>tx_uses_nonce_ok() {
<a href=#1598 id=1598 data-nosnippet>1598</a>        <span class="kw">let </span>(<span class="kw">_</span>, <span class="kw">_</span>, tx) = nonced_transfer_tx();
<a href=#1599 id=1599 data-nosnippet>1599</a>        <span class="macro">assert!</span>(uses_durable_nonce(<span class="kw-2">&amp;</span>tx).is_some());
<a href=#1600 id=1600 data-nosnippet>1600</a>    }
<a href=#1601 id=1601 data-nosnippet>1601</a>
<a href=#1602 id=1602 data-nosnippet>1602</a>    <span class="attr">#[test]
<a href=#1603 id=1603 data-nosnippet>1603</a>    </span><span class="kw">fn </span>tx_uses_nonce_empty_ix_fail() {
<a href=#1604 id=1604 data-nosnippet>1604</a>        <span class="macro">assert!</span>(uses_durable_nonce(<span class="kw-2">&amp;</span>Transaction::default()).is_none());
<a href=#1605 id=1605 data-nosnippet>1605</a>    }
<a href=#1606 id=1606 data-nosnippet>1606</a>
<a href=#1607 id=1607 data-nosnippet>1607</a>    <span class="attr">#[test]
<a href=#1608 id=1608 data-nosnippet>1608</a>    </span><span class="kw">fn </span>tx_uses_nonce_bad_prog_id_idx_fail() {
<a href=#1609 id=1609 data-nosnippet>1609</a>        <span class="kw">let </span>(<span class="kw">_</span>, <span class="kw">_</span>, <span class="kw-2">mut </span>tx) = nonced_transfer_tx();
<a href=#1610 id=1610 data-nosnippet>1610</a>        tx.message.instructions.get_mut(<span class="number">0</span>).unwrap().program_id_index = <span class="number">255u8</span>;
<a href=#1611 id=1611 data-nosnippet>1611</a>        <span class="macro">assert!</span>(uses_durable_nonce(<span class="kw-2">&amp;</span>tx).is_none());
<a href=#1612 id=1612 data-nosnippet>1612</a>    }
<a href=#1613 id=1613 data-nosnippet>1613</a>
<a href=#1614 id=1614 data-nosnippet>1614</a>    <span class="attr">#[test]
<a href=#1615 id=1615 data-nosnippet>1615</a>    </span><span class="kw">fn </span>tx_uses_nonce_first_prog_id_not_nonce_fail() {
<a href=#1616 id=1616 data-nosnippet>1616</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#1617 id=1617 data-nosnippet>1617</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#1618 id=1618 data-nosnippet>1618</a>        <span class="kw">let </span>nonce_keypair = Keypair::new();
<a href=#1619 id=1619 data-nosnippet>1619</a>        <span class="kw">let </span>nonce_pubkey = nonce_keypair.pubkey();
<a href=#1620 id=1620 data-nosnippet>1620</a>        <span class="kw">let </span>instructions = [
<a href=#1621 id=1621 data-nosnippet>1621</a>            system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey, <span class="number">42</span>),
<a href=#1622 id=1622 data-nosnippet>1622</a>            system_instruction::advance_nonce_account(<span class="kw-2">&amp;</span>nonce_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey),
<a href=#1623 id=1623 data-nosnippet>1623</a>        ];
<a href=#1624 id=1624 data-nosnippet>1624</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>from_pubkey));
<a href=#1625 id=1625 data-nosnippet>1625</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>nonce_keypair], message, Hash::default());
<a href=#1626 id=1626 data-nosnippet>1626</a>        <span class="macro">assert!</span>(uses_durable_nonce(<span class="kw-2">&amp;</span>tx).is_none());
<a href=#1627 id=1627 data-nosnippet>1627</a>    }
<a href=#1628 id=1628 data-nosnippet>1628</a>
<a href=#1629 id=1629 data-nosnippet>1629</a>    <span class="attr">#[test]
<a href=#1630 id=1630 data-nosnippet>1630</a>    </span><span class="kw">fn </span>tx_uses_nonce_wrong_first_nonce_ix_fail() {
<a href=#1631 id=1631 data-nosnippet>1631</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#1632 id=1632 data-nosnippet>1632</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#1633 id=1633 data-nosnippet>1633</a>        <span class="kw">let </span>nonce_keypair = Keypair::new();
<a href=#1634 id=1634 data-nosnippet>1634</a>        <span class="kw">let </span>nonce_pubkey = nonce_keypair.pubkey();
<a href=#1635 id=1635 data-nosnippet>1635</a>        <span class="kw">let </span>instructions = [
<a href=#1636 id=1636 data-nosnippet>1636</a>            system_instruction::withdraw_nonce_account(
<a href=#1637 id=1637 data-nosnippet>1637</a>                <span class="kw-2">&amp;</span>nonce_pubkey,
<a href=#1638 id=1638 data-nosnippet>1638</a>                <span class="kw-2">&amp;</span>nonce_pubkey,
<a href=#1639 id=1639 data-nosnippet>1639</a>                <span class="kw-2">&amp;</span>from_pubkey,
<a href=#1640 id=1640 data-nosnippet>1640</a>                <span class="number">42</span>,
<a href=#1641 id=1641 data-nosnippet>1641</a>            ),
<a href=#1642 id=1642 data-nosnippet>1642</a>            system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>nonce_pubkey, <span class="number">42</span>),
<a href=#1643 id=1643 data-nosnippet>1643</a>        ];
<a href=#1644 id=1644 data-nosnippet>1644</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>nonce_pubkey));
<a href=#1645 id=1645 data-nosnippet>1645</a>        <span class="kw">let </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>nonce_keypair], message, Hash::default());
<a href=#1646 id=1646 data-nosnippet>1646</a>        <span class="macro">assert!</span>(uses_durable_nonce(<span class="kw-2">&amp;</span>tx).is_none());
<a href=#1647 id=1647 data-nosnippet>1647</a>    }
<a href=#1648 id=1648 data-nosnippet>1648</a>
<a href=#1649 id=1649 data-nosnippet>1649</a>    <span class="attr">#[test]
<a href=#1650 id=1650 data-nosnippet>1650</a>    </span><span class="kw">fn </span>tx_keypair_pubkey_mismatch() {
<a href=#1651 id=1651 data-nosnippet>1651</a>        <span class="kw">let </span>from_keypair = Keypair::new();
<a href=#1652 id=1652 data-nosnippet>1652</a>        <span class="kw">let </span>from_pubkey = from_keypair.pubkey();
<a href=#1653 id=1653 data-nosnippet>1653</a>        <span class="kw">let </span>to_pubkey = Pubkey::new_unique();
<a href=#1654 id=1654 data-nosnippet>1654</a>        <span class="kw">let </span>instructions = [system_instruction::transfer(<span class="kw-2">&amp;</span>from_pubkey, <span class="kw-2">&amp;</span>to_pubkey, <span class="number">42</span>)];
<a href=#1655 id=1655 data-nosnippet>1655</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_with_payer(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>from_pubkey));
<a href=#1656 id=1656 data-nosnippet>1656</a>        <span class="kw">let </span>unused_keypair = Keypair::new();
<a href=#1657 id=1657 data-nosnippet>1657</a>        <span class="kw">let </span>err = tx
<a href=#1658 id=1658 data-nosnippet>1658</a>            .try_partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>from_keypair, <span class="kw-2">&amp;</span>unused_keypair], Hash::default())
<a href=#1659 id=1659 data-nosnippet>1659</a>            .unwrap_err();
<a href=#1660 id=1660 data-nosnippet>1660</a>        <span class="macro">assert_eq!</span>(err, SignerError::KeypairPubkeyMismatch);
<a href=#1661 id=1661 data-nosnippet>1661</a>    }
<a href=#1662 id=1662 data-nosnippet>1662</a>
<a href=#1663 id=1663 data-nosnippet>1663</a>    <span class="attr">#[test]
<a href=#1664 id=1664 data-nosnippet>1664</a>    </span><span class="kw">fn </span>test_unsized_signers() {
<a href=#1665 id=1665 data-nosnippet>1665</a>        <span class="kw">fn </span>instructions_to_tx(
<a href=#1666 id=1666 data-nosnippet>1666</a>            instructions: <span class="kw-2">&amp;</span>[Instruction],
<a href=#1667 id=1667 data-nosnippet>1667</a>            signers: Box&lt;<span class="kw">dyn </span>Signers&gt;,
<a href=#1668 id=1668 data-nosnippet>1668</a>        ) -&gt; Transaction {
<a href=#1669 id=1669 data-nosnippet>1669</a>            <span class="kw">let </span>pubkeys = signers.pubkeys();
<a href=#1670 id=1670 data-nosnippet>1670</a>            <span class="kw">let </span>first_signer = pubkeys.first().expect(<span class="string">"should exist"</span>);
<a href=#1671 id=1671 data-nosnippet>1671</a>            <span class="kw">let </span>message = Message::new(instructions, <span class="prelude-val">Some</span>(first_signer));
<a href=#1672 id=1672 data-nosnippet>1672</a>            Transaction::new(signers.as_ref(), message, Hash::default())
<a href=#1673 id=1673 data-nosnippet>1673</a>        }
<a href=#1674 id=1674 data-nosnippet>1674</a>
<a href=#1675 id=1675 data-nosnippet>1675</a>        <span class="kw">let </span>signer: Box&lt;<span class="kw">dyn </span>Signer&gt; = Box::new(Keypair::new());
<a href=#1676 id=1676 data-nosnippet>1676</a>        <span class="kw">let </span>tx = instructions_to_tx(<span class="kw-2">&amp;</span>[], Box::new(<span class="macro">vec!</span>[signer]));
<a href=#1677 id=1677 data-nosnippet>1677</a>
<a href=#1678 id=1678 data-nosnippet>1678</a>        <span class="macro">assert!</span>(tx.is_signed());
<a href=#1679 id=1679 data-nosnippet>1679</a>    }
<a href=#1680 id=1680 data-nosnippet>1680</a>
<a href=#1681 id=1681 data-nosnippet>1681</a>    <span class="attr">#[test]
<a href=#1682 id=1682 data-nosnippet>1682</a>    </span><span class="kw">fn </span>test_replace_signatures() {
<a href=#1683 id=1683 data-nosnippet>1683</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#1684 id=1684 data-nosnippet>1684</a>        <span class="kw">let </span>keypair0 = Keypair::new();
<a href=#1685 id=1685 data-nosnippet>1685</a>        <span class="kw">let </span>keypair1 = Keypair::new();
<a href=#1686 id=1686 data-nosnippet>1686</a>        <span class="kw">let </span>pubkey0 = keypair0.pubkey();
<a href=#1687 id=1687 data-nosnippet>1687</a>        <span class="kw">let </span>pubkey1 = keypair1.pubkey();
<a href=#1688 id=1688 data-nosnippet>1688</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#1689 id=1689 data-nosnippet>1689</a>            program_id,
<a href=#1690 id=1690 data-nosnippet>1690</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1691 id=1691 data-nosnippet>1691</a>            <span class="macro">vec!</span>[
<a href=#1692 id=1692 data-nosnippet>1692</a>                AccountMeta::new(pubkey0, <span class="bool-val">true</span>),
<a href=#1693 id=1693 data-nosnippet>1693</a>                AccountMeta::new(pubkey1, <span class="bool-val">true</span>),
<a href=#1694 id=1694 data-nosnippet>1694</a>            ],
<a href=#1695 id=1695 data-nosnippet>1695</a>        );
<a href=#1696 id=1696 data-nosnippet>1696</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>pubkey0));
<a href=#1697 id=1697 data-nosnippet>1697</a>        <span class="kw">let </span>expected_account_keys = message.account_keys.clone();
<a href=#1698 id=1698 data-nosnippet>1698</a>        <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
<a href=#1699 id=1699 data-nosnippet>1699</a>        tx.sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>keypair0, <span class="kw-2">&amp;</span>keypair1], Hash::new_unique());
<a href=#1700 id=1700 data-nosnippet>1700</a>
<a href=#1701 id=1701 data-nosnippet>1701</a>        <span class="kw">let </span>signature0 = keypair0.sign_message(<span class="kw-2">&amp;</span>tx.message_data());
<a href=#1702 id=1702 data-nosnippet>1702</a>        <span class="kw">let </span>signature1 = keypair1.sign_message(<span class="kw-2">&amp;</span>tx.message_data());
<a href=#1703 id=1703 data-nosnippet>1703</a>
<a href=#1704 id=1704 data-nosnippet>1704</a>        <span class="comment">// Replace signatures with order swapped
<a href=#1705 id=1705 data-nosnippet>1705</a>        </span>tx.replace_signatures(<span class="kw-2">&amp;</span>[(pubkey1, signature1), (pubkey0, signature0)])
<a href=#1706 id=1706 data-nosnippet>1706</a>            .unwrap();
<a href=#1707 id=1707 data-nosnippet>1707</a>        <span class="comment">// Order of account_keys should not change
<a href=#1708 id=1708 data-nosnippet>1708</a>        </span><span class="macro">assert_eq!</span>(tx.message.account_keys, expected_account_keys);
<a href=#1709 id=1709 data-nosnippet>1709</a>        <span class="comment">// Order of signatures should match original account_keys list
<a href=#1710 id=1710 data-nosnippet>1710</a>        </span><span class="macro">assert_eq!</span>(tx.signatures, <span class="kw-2">&amp;</span>[signature0, signature1]);
<a href=#1711 id=1711 data-nosnippet>1711</a>    }
<a href=#1712 id=1712 data-nosnippet>1712</a>}</code></pre></div></section></main></body></html>