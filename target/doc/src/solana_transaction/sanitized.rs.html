<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-transaction-2.2.2/src/sanitized.rs`."><title>sanitized.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_transaction/</div>sanitized.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>{
<a href=#2 id=2 data-nosnippet>2</a>    <span class="kw">crate</span>::versioned::{sanitized::SanitizedVersionedTransaction, VersionedTransaction},
<a href=#3 id=3 data-nosnippet>3</a>    solana_hash::Hash,
<a href=#4 id=4 data-nosnippet>4</a>    solana_message::{
<a href=#5 id=5 data-nosnippet>5</a>        legacy,
<a href=#6 id=6 data-nosnippet>6</a>        v0::{<span class="self">self</span>, LoadedAddresses},
<a href=#7 id=7 data-nosnippet>7</a>        AddressLoader, LegacyMessage, SanitizedMessage, SanitizedVersionedMessage,
<a href=#8 id=8 data-nosnippet>8</a>        VersionedMessage,
<a href=#9 id=9 data-nosnippet>9</a>    },
<a href=#10 id=10 data-nosnippet>10</a>    solana_pubkey::Pubkey,
<a href=#11 id=11 data-nosnippet>11</a>    solana_signature::Signature,
<a href=#12 id=12 data-nosnippet>12</a>    solana_transaction_error::{TransactionError, TransactionResult <span class="kw">as </span><span class="prelude-ty">Result</span>},
<a href=#13 id=13 data-nosnippet>13</a>    std::collections::HashSet,
<a href=#14 id=14 data-nosnippet>14</a>};
<a href=#15 id=15 data-nosnippet>15</a><span class="attr">#[cfg(feature = <span class="string">"blake3"</span>)]
<a href=#16 id=16 data-nosnippet>16</a></span><span class="kw">use </span>{<span class="kw">crate</span>::Transaction, solana_sanitize::Sanitize};
<a href=#17 id=17 data-nosnippet>17</a>
<a href=#18 id=18 data-nosnippet>18</a><span class="doccomment">/// Maximum number of accounts that a transaction may lock.
<a href=#19 id=19 data-nosnippet>19</a>/// 128 was chosen because it is the minimum number of accounts
<a href=#20 id=20 data-nosnippet>20</a>/// needed for the Neon EVM implementation.
<a href=#21 id=21 data-nosnippet>21</a></span><span class="kw">pub const </span>MAX_TX_ACCOUNT_LOCKS: usize = <span class="number">128</span>;
<a href=#22 id=22 data-nosnippet>22</a>
<a href=#23 id=23 data-nosnippet>23</a><span class="doccomment">/// Sanitized transaction and the hash of its message
<a href=#24 id=24 data-nosnippet>24</a></span><span class="attr">#[derive(Debug, Clone, Eq, PartialEq)]
<a href=#25 id=25 data-nosnippet>25</a></span><span class="kw">pub struct </span>SanitizedTransaction {
<a href=#26 id=26 data-nosnippet>26</a>    message: SanitizedMessage,
<a href=#27 id=27 data-nosnippet>27</a>    message_hash: Hash,
<a href=#28 id=28 data-nosnippet>28</a>    is_simple_vote_tx: bool,
<a href=#29 id=29 data-nosnippet>29</a>    signatures: Vec&lt;Signature&gt;,
<a href=#30 id=30 data-nosnippet>30</a>}
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a><span class="doccomment">/// Set of accounts that must be locked for safe transaction processing
<a href=#33 id=33 data-nosnippet>33</a></span><span class="attr">#[derive(Debug, Clone, Default, Eq, PartialEq)]
<a href=#34 id=34 data-nosnippet>34</a></span><span class="kw">pub struct </span>TransactionAccountLocks&lt;<span class="lifetime">'a</span>&gt; {
<a href=#35 id=35 data-nosnippet>35</a>    <span class="doccomment">/// List of readonly account key locks
<a href=#36 id=36 data-nosnippet>36</a>    </span><span class="kw">pub </span>readonly: Vec&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>Pubkey&gt;,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="doccomment">/// List of writable account key locks
<a href=#38 id=38 data-nosnippet>38</a>    </span><span class="kw">pub </span>writable: Vec&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>Pubkey&gt;,
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="doccomment">/// Type that represents whether the transaction message has been precomputed or
<a href=#42 id=42 data-nosnippet>42</a>/// not.
<a href=#43 id=43 data-nosnippet>43</a></span><span class="kw">pub enum </span>MessageHash {
<a href=#44 id=44 data-nosnippet>44</a>    Precomputed(Hash),
<a href=#45 id=45 data-nosnippet>45</a>    Compute,
<a href=#46 id=46 data-nosnippet>46</a>}
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a><span class="kw">impl </span>From&lt;Hash&gt; <span class="kw">for </span>MessageHash {
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">fn </span>from(hash: Hash) -&gt; <span class="self">Self </span>{
<a href=#50 id=50 data-nosnippet>50</a>        <span class="self">Self</span>::Precomputed(hash)
<a href=#51 id=51 data-nosnippet>51</a>    }
<a href=#52 id=52 data-nosnippet>52</a>}
<a href=#53 id=53 data-nosnippet>53</a>
<a href=#54 id=54 data-nosnippet>54</a><span class="kw">impl </span>SanitizedTransaction {
<a href=#55 id=55 data-nosnippet>55</a>    <span class="doccomment">/// Create a sanitized transaction from a sanitized versioned transaction.
<a href=#56 id=56 data-nosnippet>56</a>    /// If the input transaction uses address tables, attempt to lookup the
<a href=#57 id=57 data-nosnippet>57</a>    /// address for each table index.
<a href=#58 id=58 data-nosnippet>58</a>    </span><span class="kw">pub fn </span>try_new(
<a href=#59 id=59 data-nosnippet>59</a>        tx: SanitizedVersionedTransaction,
<a href=#60 id=60 data-nosnippet>60</a>        message_hash: Hash,
<a href=#61 id=61 data-nosnippet>61</a>        is_simple_vote_tx: bool,
<a href=#62 id=62 data-nosnippet>62</a>        address_loader: <span class="kw">impl </span>AddressLoader,
<a href=#63 id=63 data-nosnippet>63</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#64 id=64 data-nosnippet>64</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#65 id=65 data-nosnippet>65</a>        <span class="kw">let </span>signatures = tx.signatures;
<a href=#66 id=66 data-nosnippet>66</a>        <span class="kw">let </span>SanitizedVersionedMessage { message } = tx.message;
<a href=#67 id=67 data-nosnippet>67</a>        <span class="kw">let </span>message = <span class="kw">match </span>message {
<a href=#68 id=68 data-nosnippet>68</a>            VersionedMessage::Legacy(message) =&gt; {
<a href=#69 id=69 data-nosnippet>69</a>                SanitizedMessage::Legacy(LegacyMessage::new(message, reserved_account_keys))
<a href=#70 id=70 data-nosnippet>70</a>            }
<a href=#71 id=71 data-nosnippet>71</a>            VersionedMessage::V0(message) =&gt; {
<a href=#72 id=72 data-nosnippet>72</a>                <span class="kw">let </span>loaded_addresses =
<a href=#73 id=73 data-nosnippet>73</a>                    address_loader.load_addresses(<span class="kw-2">&amp;</span>message.address_table_lookups)<span class="question-mark">?</span>;
<a href=#74 id=74 data-nosnippet>74</a>                SanitizedMessage::V0(v0::LoadedMessage::new(
<a href=#75 id=75 data-nosnippet>75</a>                    message,
<a href=#76 id=76 data-nosnippet>76</a>                    loaded_addresses,
<a href=#77 id=77 data-nosnippet>77</a>                    reserved_account_keys,
<a href=#78 id=78 data-nosnippet>78</a>                ))
<a href=#79 id=79 data-nosnippet>79</a>            }
<a href=#80 id=80 data-nosnippet>80</a>        };
<a href=#81 id=81 data-nosnippet>81</a>
<a href=#82 id=82 data-nosnippet>82</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#83 id=83 data-nosnippet>83</a>            message,
<a href=#84 id=84 data-nosnippet>84</a>            message_hash,
<a href=#85 id=85 data-nosnippet>85</a>            is_simple_vote_tx,
<a href=#86 id=86 data-nosnippet>86</a>            signatures,
<a href=#87 id=87 data-nosnippet>87</a>        })
<a href=#88 id=88 data-nosnippet>88</a>    }
<a href=#89 id=89 data-nosnippet>89</a>
<a href=#90 id=90 data-nosnippet>90</a>    <span class="attr">#[cfg(feature = <span class="string">"blake3"</span>)]
<a href=#91 id=91 data-nosnippet>91</a>    </span><span class="doccomment">/// Create a sanitized transaction from an un-sanitized versioned
<a href=#92 id=92 data-nosnippet>92</a>    /// transaction.  If the input transaction uses address tables, attempt to
<a href=#93 id=93 data-nosnippet>93</a>    /// lookup the address for each table index.
<a href=#94 id=94 data-nosnippet>94</a>    </span><span class="kw">pub fn </span>try_create(
<a href=#95 id=95 data-nosnippet>95</a>        tx: VersionedTransaction,
<a href=#96 id=96 data-nosnippet>96</a>        message_hash: <span class="kw">impl </span>Into&lt;MessageHash&gt;,
<a href=#97 id=97 data-nosnippet>97</a>        is_simple_vote_tx: <span class="prelude-ty">Option</span>&lt;bool&gt;,
<a href=#98 id=98 data-nosnippet>98</a>        address_loader: <span class="kw">impl </span>AddressLoader,
<a href=#99 id=99 data-nosnippet>99</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#100 id=100 data-nosnippet>100</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#101 id=101 data-nosnippet>101</a>        <span class="kw">let </span>sanitized_versioned_tx = SanitizedVersionedTransaction::try_from(tx)<span class="question-mark">?</span>;
<a href=#102 id=102 data-nosnippet>102</a>        <span class="kw">let </span>is_simple_vote_tx = is_simple_vote_tx.unwrap_or_else(|| {
<a href=#103 id=103 data-nosnippet>103</a>            <span class="kw">crate</span>::simple_vote_transaction_checker::is_simple_vote_transaction(
<a href=#104 id=104 data-nosnippet>104</a>                <span class="kw-2">&amp;</span>sanitized_versioned_tx,
<a href=#105 id=105 data-nosnippet>105</a>            )
<a href=#106 id=106 data-nosnippet>106</a>        });
<a href=#107 id=107 data-nosnippet>107</a>        <span class="kw">let </span>message_hash = <span class="kw">match </span>message_hash.into() {
<a href=#108 id=108 data-nosnippet>108</a>            MessageHash::Compute =&gt; sanitized_versioned_tx.message.message.hash(),
<a href=#109 id=109 data-nosnippet>109</a>            MessageHash::Precomputed(hash) =&gt; hash,
<a href=#110 id=110 data-nosnippet>110</a>        };
<a href=#111 id=111 data-nosnippet>111</a>        <span class="self">Self</span>::try_new(
<a href=#112 id=112 data-nosnippet>112</a>            sanitized_versioned_tx,
<a href=#113 id=113 data-nosnippet>113</a>            message_hash,
<a href=#114 id=114 data-nosnippet>114</a>            is_simple_vote_tx,
<a href=#115 id=115 data-nosnippet>115</a>            address_loader,
<a href=#116 id=116 data-nosnippet>116</a>            reserved_account_keys,
<a href=#117 id=117 data-nosnippet>117</a>        )
<a href=#118 id=118 data-nosnippet>118</a>    }
<a href=#119 id=119 data-nosnippet>119</a>
<a href=#120 id=120 data-nosnippet>120</a>    <span class="doccomment">/// Create a sanitized transaction from a legacy transaction
<a href=#121 id=121 data-nosnippet>121</a>    </span><span class="attr">#[cfg(feature = <span class="string">"blake3"</span>)]
<a href=#122 id=122 data-nosnippet>122</a>    </span><span class="kw">pub fn </span>try_from_legacy_transaction(
<a href=#123 id=123 data-nosnippet>123</a>        tx: Transaction,
<a href=#124 id=124 data-nosnippet>124</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#125 id=125 data-nosnippet>125</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#126 id=126 data-nosnippet>126</a>        tx.sanitize()<span class="question-mark">?</span>;
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#129 id=129 data-nosnippet>129</a>            message_hash: tx.message.hash(),
<a href=#130 id=130 data-nosnippet>130</a>            message: SanitizedMessage::Legacy(LegacyMessage::new(
<a href=#131 id=131 data-nosnippet>131</a>                tx.message,
<a href=#132 id=132 data-nosnippet>132</a>                reserved_account_keys,
<a href=#133 id=133 data-nosnippet>133</a>            )),
<a href=#134 id=134 data-nosnippet>134</a>            is_simple_vote_tx: <span class="bool-val">false</span>,
<a href=#135 id=135 data-nosnippet>135</a>            signatures: tx.signatures,
<a href=#136 id=136 data-nosnippet>136</a>        })
<a href=#137 id=137 data-nosnippet>137</a>    }
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>    <span class="doccomment">/// Create a sanitized transaction from a legacy transaction. Used for tests only.
<a href=#140 id=140 data-nosnippet>140</a>    </span><span class="attr">#[cfg(feature = <span class="string">"blake3"</span>)]
<a href=#141 id=141 data-nosnippet>141</a>    </span><span class="kw">pub fn </span>from_transaction_for_tests(tx: Transaction) -&gt; <span class="self">Self </span>{
<a href=#142 id=142 data-nosnippet>142</a>        <span class="kw">let </span>empty_key_set = HashSet::default();
<a href=#143 id=143 data-nosnippet>143</a>        <span class="self">Self</span>::try_from_legacy_transaction(tx, <span class="kw-2">&amp;</span>empty_key_set).unwrap()
<a href=#144 id=144 data-nosnippet>144</a>    }
<a href=#145 id=145 data-nosnippet>145</a>
<a href=#146 id=146 data-nosnippet>146</a>    <span class="doccomment">/// Create a sanitized transaction from fields.
<a href=#147 id=147 data-nosnippet>147</a>    /// Performs only basic signature sanitization.
<a href=#148 id=148 data-nosnippet>148</a>    </span><span class="kw">pub fn </span>try_new_from_fields(
<a href=#149 id=149 data-nosnippet>149</a>        message: SanitizedMessage,
<a href=#150 id=150 data-nosnippet>150</a>        message_hash: Hash,
<a href=#151 id=151 data-nosnippet>151</a>        is_simple_vote_tx: bool,
<a href=#152 id=152 data-nosnippet>152</a>        signatures: Vec&lt;Signature&gt;,
<a href=#153 id=153 data-nosnippet>153</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#154 id=154 data-nosnippet>154</a>        VersionedTransaction::sanitize_signatures_inner(
<a href=#155 id=155 data-nosnippet>155</a>            usize::from(message.header().num_required_signatures),
<a href=#156 id=156 data-nosnippet>156</a>            message.static_account_keys().len(),
<a href=#157 id=157 data-nosnippet>157</a>            signatures.len(),
<a href=#158 id=158 data-nosnippet>158</a>        )<span class="question-mark">?</span>;
<a href=#159 id=159 data-nosnippet>159</a>
<a href=#160 id=160 data-nosnippet>160</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#161 id=161 data-nosnippet>161</a>            message,
<a href=#162 id=162 data-nosnippet>162</a>            message_hash,
<a href=#163 id=163 data-nosnippet>163</a>            signatures,
<a href=#164 id=164 data-nosnippet>164</a>            is_simple_vote_tx,
<a href=#165 id=165 data-nosnippet>165</a>        })
<a href=#166 id=166 data-nosnippet>166</a>    }
<a href=#167 id=167 data-nosnippet>167</a>
<a href=#168 id=168 data-nosnippet>168</a>    <span class="doccomment">/// Return the first signature for this transaction.
<a href=#169 id=169 data-nosnippet>169</a>    ///
<a href=#170 id=170 data-nosnippet>170</a>    /// Notes:
<a href=#171 id=171 data-nosnippet>171</a>    ///
<a href=#172 id=172 data-nosnippet>172</a>    /// Sanitized transactions must have at least one signature because the
<a href=#173 id=173 data-nosnippet>173</a>    /// number of signatures must be greater than or equal to the message header
<a href=#174 id=174 data-nosnippet>174</a>    /// value `num_required_signatures` which must be greater than 0 itself.
<a href=#175 id=175 data-nosnippet>175</a>    </span><span class="kw">pub fn </span>signature(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Signature {
<a href=#176 id=176 data-nosnippet>176</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.signatures[<span class="number">0</span>]
<a href=#177 id=177 data-nosnippet>177</a>    }
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a>    <span class="doccomment">/// Return the list of signatures for this transaction
<a href=#180 id=180 data-nosnippet>180</a>    </span><span class="kw">pub fn </span>signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[Signature] {
<a href=#181 id=181 data-nosnippet>181</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.signatures
<a href=#182 id=182 data-nosnippet>182</a>    }
<a href=#183 id=183 data-nosnippet>183</a>
<a href=#184 id=184 data-nosnippet>184</a>    <span class="doccomment">/// Return the signed message
<a href=#185 id=185 data-nosnippet>185</a>    </span><span class="kw">pub fn </span>message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>SanitizedMessage {
<a href=#186 id=186 data-nosnippet>186</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.message
<a href=#187 id=187 data-nosnippet>187</a>    }
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a>    <span class="doccomment">/// Return the hash of the signed message
<a href=#190 id=190 data-nosnippet>190</a>    </span><span class="kw">pub fn </span>message_hash(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Hash {
<a href=#191 id=191 data-nosnippet>191</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.message_hash
<a href=#192 id=192 data-nosnippet>192</a>    }
<a href=#193 id=193 data-nosnippet>193</a>
<a href=#194 id=194 data-nosnippet>194</a>    <span class="doccomment">/// Returns true if this transaction is a simple vote
<a href=#195 id=195 data-nosnippet>195</a>    </span><span class="kw">pub fn </span>is_simple_vote_transaction(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#196 id=196 data-nosnippet>196</a>        <span class="self">self</span>.is_simple_vote_tx
<a href=#197 id=197 data-nosnippet>197</a>    }
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>    <span class="doccomment">/// Convert this sanitized transaction into a versioned transaction for
<a href=#200 id=200 data-nosnippet>200</a>    /// recording in the ledger.
<a href=#201 id=201 data-nosnippet>201</a>    </span><span class="kw">pub fn </span>to_versioned_transaction(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; VersionedTransaction {
<a href=#202 id=202 data-nosnippet>202</a>        <span class="kw">let </span>signatures = <span class="self">self</span>.signatures.clone();
<a href=#203 id=203 data-nosnippet>203</a>        <span class="kw">match </span><span class="kw-2">&amp;</span><span class="self">self</span>.message {
<a href=#204 id=204 data-nosnippet>204</a>            SanitizedMessage::V0(sanitized_msg) =&gt; VersionedTransaction {
<a href=#205 id=205 data-nosnippet>205</a>                signatures,
<a href=#206 id=206 data-nosnippet>206</a>                message: VersionedMessage::V0(v0::Message::clone(<span class="kw-2">&amp;</span>sanitized_msg.message)),
<a href=#207 id=207 data-nosnippet>207</a>            },
<a href=#208 id=208 data-nosnippet>208</a>            SanitizedMessage::Legacy(legacy_message) =&gt; VersionedTransaction {
<a href=#209 id=209 data-nosnippet>209</a>                signatures,
<a href=#210 id=210 data-nosnippet>210</a>                message: VersionedMessage::Legacy(legacy::Message::clone(<span class="kw-2">&amp;</span>legacy_message.message)),
<a href=#211 id=211 data-nosnippet>211</a>            },
<a href=#212 id=212 data-nosnippet>212</a>        }
<a href=#213 id=213 data-nosnippet>213</a>    }
<a href=#214 id=214 data-nosnippet>214</a>
<a href=#215 id=215 data-nosnippet>215</a>    <span class="doccomment">/// Validate and return the account keys locked by this transaction
<a href=#216 id=216 data-nosnippet>216</a>    </span><span class="kw">pub fn </span>get_account_locks(
<a href=#217 id=217 data-nosnippet>217</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#218 id=218 data-nosnippet>218</a>        tx_account_lock_limit: usize,
<a href=#219 id=219 data-nosnippet>219</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;TransactionAccountLocks&gt; {
<a href=#220 id=220 data-nosnippet>220</a>        <span class="self">Self</span>::validate_account_locks(<span class="self">self</span>.message(), tx_account_lock_limit)<span class="question-mark">?</span>;
<a href=#221 id=221 data-nosnippet>221</a>        <span class="prelude-val">Ok</span>(<span class="self">self</span>.get_account_locks_unchecked())
<a href=#222 id=222 data-nosnippet>222</a>    }
<a href=#223 id=223 data-nosnippet>223</a>
<a href=#224 id=224 data-nosnippet>224</a>    <span class="doccomment">/// Return the list of accounts that must be locked during processing this transaction.
<a href=#225 id=225 data-nosnippet>225</a>    </span><span class="kw">pub fn </span>get_account_locks_unchecked(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; TransactionAccountLocks {
<a href=#226 id=226 data-nosnippet>226</a>        <span class="kw">let </span>message = <span class="kw-2">&amp;</span><span class="self">self</span>.message;
<a href=#227 id=227 data-nosnippet>227</a>        <span class="kw">let </span>account_keys = message.account_keys();
<a href=#228 id=228 data-nosnippet>228</a>        <span class="kw">let </span>num_readonly_accounts = message.num_readonly_accounts();
<a href=#229 id=229 data-nosnippet>229</a>        <span class="kw">let </span>num_writable_accounts = account_keys.len().saturating_sub(num_readonly_accounts);
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>        <span class="kw">let </span><span class="kw-2">mut </span>account_locks = TransactionAccountLocks {
<a href=#232 id=232 data-nosnippet>232</a>            writable: Vec::with_capacity(num_writable_accounts),
<a href=#233 id=233 data-nosnippet>233</a>            readonly: Vec::with_capacity(num_readonly_accounts),
<a href=#234 id=234 data-nosnippet>234</a>        };
<a href=#235 id=235 data-nosnippet>235</a>
<a href=#236 id=236 data-nosnippet>236</a>        <span class="kw">for </span>(i, key) <span class="kw">in </span>account_keys.iter().enumerate() {
<a href=#237 id=237 data-nosnippet>237</a>            <span class="kw">if </span>message.is_writable(i) {
<a href=#238 id=238 data-nosnippet>238</a>                account_locks.writable.push(key);
<a href=#239 id=239 data-nosnippet>239</a>            } <span class="kw">else </span>{
<a href=#240 id=240 data-nosnippet>240</a>                account_locks.readonly.push(key);
<a href=#241 id=241 data-nosnippet>241</a>            }
<a href=#242 id=242 data-nosnippet>242</a>        }
<a href=#243 id=243 data-nosnippet>243</a>
<a href=#244 id=244 data-nosnippet>244</a>        account_locks
<a href=#245 id=245 data-nosnippet>245</a>    }
<a href=#246 id=246 data-nosnippet>246</a>
<a href=#247 id=247 data-nosnippet>247</a>    <span class="doccomment">/// Return the list of addresses loaded from on-chain address lookup tables
<a href=#248 id=248 data-nosnippet>248</a>    </span><span class="kw">pub fn </span>get_loaded_addresses(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; LoadedAddresses {
<a href=#249 id=249 data-nosnippet>249</a>        <span class="kw">match </span><span class="kw-2">&amp;</span><span class="self">self</span>.message {
<a href=#250 id=250 data-nosnippet>250</a>            SanitizedMessage::Legacy(<span class="kw">_</span>) =&gt; LoadedAddresses::default(),
<a href=#251 id=251 data-nosnippet>251</a>            SanitizedMessage::V0(message) =&gt; LoadedAddresses::clone(<span class="kw-2">&amp;</span>message.loaded_addresses),
<a href=#252 id=252 data-nosnippet>252</a>        }
<a href=#253 id=253 data-nosnippet>253</a>    }
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>    <span class="doccomment">/// If the transaction uses a durable nonce, return the pubkey of the nonce account
<a href=#256 id=256 data-nosnippet>256</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#257 id=257 data-nosnippet>257</a>    </span><span class="kw">pub fn </span>get_durable_nonce(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#258 id=258 data-nosnippet>258</a>        <span class="self">self</span>.message.get_durable_nonce()
<a href=#259 id=259 data-nosnippet>259</a>    }
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#262 id=262 data-nosnippet>262</a>    </span><span class="doccomment">/// Return the serialized message data to sign.
<a href=#263 id=263 data-nosnippet>263</a>    </span><span class="kw">fn </span>message_data(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;u8&gt; {
<a href=#264 id=264 data-nosnippet>264</a>        <span class="kw">match </span><span class="kw-2">&amp;</span><span class="self">self</span>.message {
<a href=#265 id=265 data-nosnippet>265</a>            SanitizedMessage::Legacy(legacy_message) =&gt; legacy_message.message.serialize(),
<a href=#266 id=266 data-nosnippet>266</a>            SanitizedMessage::V0(loaded_msg) =&gt; loaded_msg.message.serialize(),
<a href=#267 id=267 data-nosnippet>267</a>        }
<a href=#268 id=268 data-nosnippet>268</a>    }
<a href=#269 id=269 data-nosnippet>269</a>
<a href=#270 id=270 data-nosnippet>270</a>    <span class="attr">#[cfg(feature = <span class="string">"verify"</span>)]
<a href=#271 id=271 data-nosnippet>271</a>    </span><span class="doccomment">/// Verify the transaction signatures
<a href=#272 id=272 data-nosnippet>272</a>    </span><span class="kw">pub fn </span>verify(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#273 id=273 data-nosnippet>273</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.message_data();
<a href=#274 id=274 data-nosnippet>274</a>        <span class="kw">if </span><span class="self">self
<a href=#275 id=275 data-nosnippet>275</a>            </span>.signatures
<a href=#276 id=276 data-nosnippet>276</a>            .iter()
<a href=#277 id=277 data-nosnippet>277</a>            .zip(<span class="self">self</span>.message.account_keys().iter())
<a href=#278 id=278 data-nosnippet>278</a>            .map(|(signature, pubkey)| signature.verify(pubkey.as_ref(), <span class="kw-2">&amp;</span>message_bytes))
<a href=#279 id=279 data-nosnippet>279</a>            .any(|verified| !verified)
<a href=#280 id=280 data-nosnippet>280</a>        {
<a href=#281 id=281 data-nosnippet>281</a>            <span class="prelude-val">Err</span>(TransactionError::SignatureFailure)
<a href=#282 id=282 data-nosnippet>282</a>        } <span class="kw">else </span>{
<a href=#283 id=283 data-nosnippet>283</a>            <span class="prelude-val">Ok</span>(())
<a href=#284 id=284 data-nosnippet>284</a>        }
<a href=#285 id=285 data-nosnippet>285</a>    }
<a href=#286 id=286 data-nosnippet>286</a>
<a href=#287 id=287 data-nosnippet>287</a>    <span class="attr">#[cfg(feature = <span class="string">"precompiles"</span>)]
<a href=#288 id=288 data-nosnippet>288</a>    </span><span class="doccomment">/// Verify the precompiled programs in this transaction
<a href=#289 id=289 data-nosnippet>289</a>    </span><span class="kw">pub fn </span>verify_precompiles(<span class="kw-2">&amp;</span><span class="self">self</span>, feature_set: <span class="kw-2">&amp;</span>solana_feature_set::FeatureSet) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#290 id=290 data-nosnippet>290</a>        <span class="kw">for </span>(index, (program_id, instruction)) <span class="kw">in
<a href=#291 id=291 data-nosnippet>291</a>            </span><span class="self">self</span>.message.program_instructions_iter().enumerate()
<a href=#292 id=292 data-nosnippet>292</a>        {
<a href=#293 id=293 data-nosnippet>293</a>            solana_precompiles::verify_if_precompile(
<a href=#294 id=294 data-nosnippet>294</a>                program_id,
<a href=#295 id=295 data-nosnippet>295</a>                instruction,
<a href=#296 id=296 data-nosnippet>296</a>                <span class="self">self</span>.message().instructions(),
<a href=#297 id=297 data-nosnippet>297</a>                feature_set,
<a href=#298 id=298 data-nosnippet>298</a>            )
<a href=#299 id=299 data-nosnippet>299</a>            .map_err(|err| {
<a href=#300 id=300 data-nosnippet>300</a>                TransactionError::InstructionError(
<a href=#301 id=301 data-nosnippet>301</a>                    index <span class="kw">as </span>u8,
<a href=#302 id=302 data-nosnippet>302</a>                    solana_instruction::error::InstructionError::Custom(err <span class="kw">as </span>u32),
<a href=#303 id=303 data-nosnippet>303</a>                )
<a href=#304 id=304 data-nosnippet>304</a>            })<span class="question-mark">?</span>;
<a href=#305 id=305 data-nosnippet>305</a>        }
<a href=#306 id=306 data-nosnippet>306</a>        <span class="prelude-val">Ok</span>(())
<a href=#307 id=307 data-nosnippet>307</a>    }
<a href=#308 id=308 data-nosnippet>308</a>
<a href=#309 id=309 data-nosnippet>309</a>    <span class="doccomment">/// Validate a transaction message against locked accounts
<a href=#310 id=310 data-nosnippet>310</a>    </span><span class="kw">pub fn </span>validate_account_locks(
<a href=#311 id=311 data-nosnippet>311</a>        message: <span class="kw-2">&amp;</span>SanitizedMessage,
<a href=#312 id=312 data-nosnippet>312</a>        tx_account_lock_limit: usize,
<a href=#313 id=313 data-nosnippet>313</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#314 id=314 data-nosnippet>314</a>        <span class="kw">if </span>message.has_duplicates() {
<a href=#315 id=315 data-nosnippet>315</a>            <span class="prelude-val">Err</span>(TransactionError::AccountLoadedTwice)
<a href=#316 id=316 data-nosnippet>316</a>        } <span class="kw">else if </span>message.account_keys().len() &gt; tx_account_lock_limit {
<a href=#317 id=317 data-nosnippet>317</a>            <span class="prelude-val">Err</span>(TransactionError::TooManyAccountLocks)
<a href=#318 id=318 data-nosnippet>318</a>        } <span class="kw">else </span>{
<a href=#319 id=319 data-nosnippet>319</a>            <span class="prelude-val">Ok</span>(())
<a href=#320 id=320 data-nosnippet>320</a>        }
<a href=#321 id=321 data-nosnippet>321</a>    }
<a href=#322 id=322 data-nosnippet>322</a>
<a href=#323 id=323 data-nosnippet>323</a>    <span class="attr">#[cfg(feature = <span class="string">"dev-context-only-utils"</span>)]
<a href=#324 id=324 data-nosnippet>324</a>    </span><span class="kw">pub fn </span>new_for_tests(
<a href=#325 id=325 data-nosnippet>325</a>        message: SanitizedMessage,
<a href=#326 id=326 data-nosnippet>326</a>        signatures: Vec&lt;Signature&gt;,
<a href=#327 id=327 data-nosnippet>327</a>        is_simple_vote_tx: bool,
<a href=#328 id=328 data-nosnippet>328</a>    ) -&gt; SanitizedTransaction {
<a href=#329 id=329 data-nosnippet>329</a>        SanitizedTransaction {
<a href=#330 id=330 data-nosnippet>330</a>            message,
<a href=#331 id=331 data-nosnippet>331</a>            message_hash: Hash::new_unique(),
<a href=#332 id=332 data-nosnippet>332</a>            signatures,
<a href=#333 id=333 data-nosnippet>333</a>            is_simple_vote_tx,
<a href=#334 id=334 data-nosnippet>334</a>        }
<a href=#335 id=335 data-nosnippet>335</a>    }
<a href=#336 id=336 data-nosnippet>336</a>}
<a href=#337 id=337 data-nosnippet>337</a>
<a href=#338 id=338 data-nosnippet>338</a><span class="attr">#[cfg(test)]
<a href=#339 id=339 data-nosnippet>339</a>#[allow(clippy::arithmetic_side_effects)]
<a href=#340 id=340 data-nosnippet>340</a></span><span class="kw">mod </span>tests {
<a href=#341 id=341 data-nosnippet>341</a>    <span class="kw">use </span>{
<a href=#342 id=342 data-nosnippet>342</a>        <span class="kw">super</span>::<span class="kw-2">*</span>,
<a href=#343 id=343 data-nosnippet>343</a>        solana_keypair::Keypair,
<a href=#344 id=344 data-nosnippet>344</a>        solana_message::{MessageHeader, SimpleAddressLoader},
<a href=#345 id=345 data-nosnippet>345</a>        solana_program::vote::{<span class="self">self</span>, state::Vote},
<a href=#346 id=346 data-nosnippet>346</a>        solana_signer::Signer,
<a href=#347 id=347 data-nosnippet>347</a>    };
<a href=#348 id=348 data-nosnippet>348</a>
<a href=#349 id=349 data-nosnippet>349</a>    <span class="attr">#[test]
<a href=#350 id=350 data-nosnippet>350</a>    </span><span class="kw">fn </span>test_try_create_simple_vote_tx() {
<a href=#351 id=351 data-nosnippet>351</a>        <span class="kw">let </span>bank_hash = Hash::default();
<a href=#352 id=352 data-nosnippet>352</a>        <span class="kw">let </span>block_hash = Hash::default();
<a href=#353 id=353 data-nosnippet>353</a>        <span class="kw">let </span>empty_key_set = HashSet::default();
<a href=#354 id=354 data-nosnippet>354</a>        <span class="kw">let </span>vote_keypair = Keypair::new();
<a href=#355 id=355 data-nosnippet>355</a>        <span class="kw">let </span>node_keypair = Keypair::new();
<a href=#356 id=356 data-nosnippet>356</a>        <span class="kw">let </span>auth_keypair = Keypair::new();
<a href=#357 id=357 data-nosnippet>357</a>        <span class="kw">let </span>votes = Vote::new(<span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>], bank_hash);
<a href=#358 id=358 data-nosnippet>358</a>        <span class="kw">let </span>vote_ix =
<a href=#359 id=359 data-nosnippet>359</a>            vote::instruction::vote(<span class="kw-2">&amp;</span>vote_keypair.pubkey(), <span class="kw-2">&amp;</span>auth_keypair.pubkey(), votes);
<a href=#360 id=360 data-nosnippet>360</a>        <span class="kw">let </span><span class="kw-2">mut </span>vote_tx = Transaction::new_with_payer(<span class="kw-2">&amp;</span>[vote_ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>node_keypair.pubkey()));
<a href=#361 id=361 data-nosnippet>361</a>        vote_tx.partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>node_keypair], block_hash);
<a href=#362 id=362 data-nosnippet>362</a>        vote_tx.partial_sign(<span class="kw-2">&amp;</span>[<span class="kw-2">&amp;</span>auth_keypair], block_hash);
<a href=#363 id=363 data-nosnippet>363</a>
<a href=#364 id=364 data-nosnippet>364</a>        <span class="comment">// single legacy vote ix, 2 signatures
<a href=#365 id=365 data-nosnippet>365</a>        </span>{
<a href=#366 id=366 data-nosnippet>366</a>            <span class="kw">let </span>vote_transaction = SanitizedTransaction::try_create(
<a href=#367 id=367 data-nosnippet>367</a>                VersionedTransaction::from(vote_tx.clone()),
<a href=#368 id=368 data-nosnippet>368</a>                MessageHash::Compute,
<a href=#369 id=369 data-nosnippet>369</a>                <span class="prelude-val">None</span>,
<a href=#370 id=370 data-nosnippet>370</a>                SimpleAddressLoader::Disabled,
<a href=#371 id=371 data-nosnippet>371</a>                <span class="kw-2">&amp;</span>empty_key_set,
<a href=#372 id=372 data-nosnippet>372</a>            )
<a href=#373 id=373 data-nosnippet>373</a>            .unwrap();
<a href=#374 id=374 data-nosnippet>374</a>            <span class="macro">assert!</span>(vote_transaction.is_simple_vote_transaction());
<a href=#375 id=375 data-nosnippet>375</a>        }
<a href=#376 id=376 data-nosnippet>376</a>
<a href=#377 id=377 data-nosnippet>377</a>        {
<a href=#378 id=378 data-nosnippet>378</a>            <span class="comment">// call side says it is not a vote
<a href=#379 id=379 data-nosnippet>379</a>            </span><span class="kw">let </span>vote_transaction = SanitizedTransaction::try_create(
<a href=#380 id=380 data-nosnippet>380</a>                VersionedTransaction::from(vote_tx.clone()),
<a href=#381 id=381 data-nosnippet>381</a>                MessageHash::Compute,
<a href=#382 id=382 data-nosnippet>382</a>                <span class="prelude-val">Some</span>(<span class="bool-val">false</span>),
<a href=#383 id=383 data-nosnippet>383</a>                SimpleAddressLoader::Disabled,
<a href=#384 id=384 data-nosnippet>384</a>                <span class="kw-2">&amp;</span>empty_key_set,
<a href=#385 id=385 data-nosnippet>385</a>            )
<a href=#386 id=386 data-nosnippet>386</a>            .unwrap();
<a href=#387 id=387 data-nosnippet>387</a>            <span class="macro">assert!</span>(!vote_transaction.is_simple_vote_transaction());
<a href=#388 id=388 data-nosnippet>388</a>        }
<a href=#389 id=389 data-nosnippet>389</a>
<a href=#390 id=390 data-nosnippet>390</a>        <span class="comment">// single legacy vote ix, 3 signatures
<a href=#391 id=391 data-nosnippet>391</a>        </span>vote_tx.signatures.push(Signature::default());
<a href=#392 id=392 data-nosnippet>392</a>        vote_tx.message.header.num_required_signatures = <span class="number">3</span>;
<a href=#393 id=393 data-nosnippet>393</a>        {
<a href=#394 id=394 data-nosnippet>394</a>            <span class="kw">let </span>vote_transaction = SanitizedTransaction::try_create(
<a href=#395 id=395 data-nosnippet>395</a>                VersionedTransaction::from(vote_tx.clone()),
<a href=#396 id=396 data-nosnippet>396</a>                MessageHash::Compute,
<a href=#397 id=397 data-nosnippet>397</a>                <span class="prelude-val">None</span>,
<a href=#398 id=398 data-nosnippet>398</a>                SimpleAddressLoader::Disabled,
<a href=#399 id=399 data-nosnippet>399</a>                <span class="kw-2">&amp;</span>empty_key_set,
<a href=#400 id=400 data-nosnippet>400</a>            )
<a href=#401 id=401 data-nosnippet>401</a>            .unwrap();
<a href=#402 id=402 data-nosnippet>402</a>            <span class="macro">assert!</span>(!vote_transaction.is_simple_vote_transaction());
<a href=#403 id=403 data-nosnippet>403</a>        }
<a href=#404 id=404 data-nosnippet>404</a>
<a href=#405 id=405 data-nosnippet>405</a>        {
<a href=#406 id=406 data-nosnippet>406</a>            <span class="comment">// call site says it is simple vote
<a href=#407 id=407 data-nosnippet>407</a>            </span><span class="kw">let </span>vote_transaction = SanitizedTransaction::try_create(
<a href=#408 id=408 data-nosnippet>408</a>                VersionedTransaction::from(vote_tx),
<a href=#409 id=409 data-nosnippet>409</a>                MessageHash::Compute,
<a href=#410 id=410 data-nosnippet>410</a>                <span class="prelude-val">Some</span>(<span class="bool-val">true</span>),
<a href=#411 id=411 data-nosnippet>411</a>                SimpleAddressLoader::Disabled,
<a href=#412 id=412 data-nosnippet>412</a>                <span class="kw-2">&amp;</span>empty_key_set,
<a href=#413 id=413 data-nosnippet>413</a>            )
<a href=#414 id=414 data-nosnippet>414</a>            .unwrap();
<a href=#415 id=415 data-nosnippet>415</a>            <span class="macro">assert!</span>(vote_transaction.is_simple_vote_transaction());
<a href=#416 id=416 data-nosnippet>416</a>        }
<a href=#417 id=417 data-nosnippet>417</a>    }
<a href=#418 id=418 data-nosnippet>418</a>
<a href=#419 id=419 data-nosnippet>419</a>    <span class="attr">#[test]
<a href=#420 id=420 data-nosnippet>420</a>    </span><span class="kw">fn </span>test_try_new_from_fields() {
<a href=#421 id=421 data-nosnippet>421</a>        <span class="kw">let </span>legacy_message = SanitizedMessage::try_from_legacy_message(
<a href=#422 id=422 data-nosnippet>422</a>            legacy::Message {
<a href=#423 id=423 data-nosnippet>423</a>                header: MessageHeader {
<a href=#424 id=424 data-nosnippet>424</a>                    num_required_signatures: <span class="number">2</span>,
<a href=#425 id=425 data-nosnippet>425</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#426 id=426 data-nosnippet>426</a>                    num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#427 id=427 data-nosnippet>427</a>                },
<a href=#428 id=428 data-nosnippet>428</a>                account_keys: <span class="macro">vec!</span>[
<a href=#429 id=429 data-nosnippet>429</a>                    Pubkey::new_unique(),
<a href=#430 id=430 data-nosnippet>430</a>                    Pubkey::new_unique(),
<a href=#431 id=431 data-nosnippet>431</a>                    Pubkey::new_unique(),
<a href=#432 id=432 data-nosnippet>432</a>                ],
<a href=#433 id=433 data-nosnippet>433</a>                ..legacy::Message::default()
<a href=#434 id=434 data-nosnippet>434</a>            },
<a href=#435 id=435 data-nosnippet>435</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#436 id=436 data-nosnippet>436</a>        )
<a href=#437 id=437 data-nosnippet>437</a>        .unwrap();
<a href=#438 id=438 data-nosnippet>438</a>
<a href=#439 id=439 data-nosnippet>439</a>        <span class="kw">for </span>is_simple_vote_tx <span class="kw">in </span>[<span class="bool-val">false</span>, <span class="bool-val">true</span>] {
<a href=#440 id=440 data-nosnippet>440</a>            <span class="comment">// Not enough signatures
<a href=#441 id=441 data-nosnippet>441</a>            </span><span class="macro">assert!</span>(SanitizedTransaction::try_new_from_fields(
<a href=#442 id=442 data-nosnippet>442</a>                legacy_message.clone(),
<a href=#443 id=443 data-nosnippet>443</a>                Hash::new_unique(),
<a href=#444 id=444 data-nosnippet>444</a>                is_simple_vote_tx,
<a href=#445 id=445 data-nosnippet>445</a>                <span class="macro">vec!</span>[],
<a href=#446 id=446 data-nosnippet>446</a>            )
<a href=#447 id=447 data-nosnippet>447</a>            .is_err());
<a href=#448 id=448 data-nosnippet>448</a>            <span class="comment">// Too many signatures
<a href=#449 id=449 data-nosnippet>449</a>            </span><span class="macro">assert!</span>(SanitizedTransaction::try_new_from_fields(
<a href=#450 id=450 data-nosnippet>450</a>                legacy_message.clone(),
<a href=#451 id=451 data-nosnippet>451</a>                Hash::new_unique(),
<a href=#452 id=452 data-nosnippet>452</a>                is_simple_vote_tx,
<a href=#453 id=453 data-nosnippet>453</a>                <span class="macro">vec!</span>[
<a href=#454 id=454 data-nosnippet>454</a>                    Signature::default(),
<a href=#455 id=455 data-nosnippet>455</a>                    Signature::default(),
<a href=#456 id=456 data-nosnippet>456</a>                    Signature::default()
<a href=#457 id=457 data-nosnippet>457</a>                ],
<a href=#458 id=458 data-nosnippet>458</a>            )
<a href=#459 id=459 data-nosnippet>459</a>            .is_err());
<a href=#460 id=460 data-nosnippet>460</a>            <span class="comment">// Correct number of signatures.
<a href=#461 id=461 data-nosnippet>461</a>            </span><span class="macro">assert!</span>(SanitizedTransaction::try_new_from_fields(
<a href=#462 id=462 data-nosnippet>462</a>                legacy_message.clone(),
<a href=#463 id=463 data-nosnippet>463</a>                Hash::new_unique(),
<a href=#464 id=464 data-nosnippet>464</a>                is_simple_vote_tx,
<a href=#465 id=465 data-nosnippet>465</a>                <span class="macro">vec!</span>[Signature::default(), Signature::default()]
<a href=#466 id=466 data-nosnippet>466</a>            )
<a href=#467 id=467 data-nosnippet>467</a>            .is_ok());
<a href=#468 id=468 data-nosnippet>468</a>        }
<a href=#469 id=469 data-nosnippet>469</a>    }
<a href=#470 id=470 data-nosnippet>470</a>}</code></pre></div></section></main></body></html>