<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/compiled_keys.rs`."><title>compiled_keys.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>compiled_keys.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#[cfg(not(target_os = <span class="string">"solana"</span>))]
<a href=#2 id=2 data-nosnippet>2</a></span><span class="kw">use crate</span>::{
<a href=#3 id=3 data-nosnippet>3</a>    v0::{LoadedAddresses, MessageAddressTableLookup},
<a href=#4 id=4 data-nosnippet>4</a>    AddressLookupTableAccount,
<a href=#5 id=5 data-nosnippet>5</a>};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>{
<a href=#7 id=7 data-nosnippet>7</a>    <span class="kw">crate</span>::MessageHeader, core::fmt, solana_instruction::Instruction, solana_pubkey::Pubkey,
<a href=#8 id=8 data-nosnippet>8</a>    std::collections::BTreeMap,
<a href=#9 id=9 data-nosnippet>9</a>};
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="doccomment">/// A helper struct to collect pubkeys compiled for a set of instructions
<a href=#12 id=12 data-nosnippet>12</a></span><span class="attr">#[derive(Default, Debug, Clone, PartialEq, Eq)]
<a href=#13 id=13 data-nosnippet>13</a></span><span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">struct </span>CompiledKeys {
<a href=#14 id=14 data-nosnippet>14</a>    payer: <span class="prelude-ty">Option</span>&lt;Pubkey&gt;,
<a href=#15 id=15 data-nosnippet>15</a>    key_meta_map: BTreeMap&lt;Pubkey, CompiledKeyMeta&gt;,
<a href=#16 id=16 data-nosnippet>16</a>}
<a href=#17 id=17 data-nosnippet>17</a>
<a href=#18 id=18 data-nosnippet>18</a><span class="attr">#[cfg_attr(target_os = <span class="string">"solana"</span>, allow(dead_code))]
<a href=#19 id=19 data-nosnippet>19</a>#[derive(PartialEq, Debug, Eq, Clone)]
<a href=#20 id=20 data-nosnippet>20</a></span><span class="kw">pub enum </span>CompileError {
<a href=#21 id=21 data-nosnippet>21</a>    AccountIndexOverflow,
<a href=#22 id=22 data-nosnippet>22</a>    AddressTableLookupIndexOverflow,
<a href=#23 id=23 data-nosnippet>23</a>    UnknownInstructionKey(Pubkey),
<a href=#24 id=24 data-nosnippet>24</a>}
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a><span class="kw">impl </span>std::error::Error <span class="kw">for </span>CompileError {}
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a><span class="kw">impl </span>fmt::Display <span class="kw">for </span>CompileError {
<a href=#29 id=29 data-nosnippet>29</a>    <span class="kw">fn </span>fmt(<span class="kw-2">&amp;</span><span class="self">self</span>, f: <span class="kw-2">&amp;mut </span>fmt::Formatter) -&gt; fmt::Result {
<a href=#30 id=30 data-nosnippet>30</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#31 id=31 data-nosnippet>31</a>            CompileError::AccountIndexOverflow =&gt; {
<a href=#32 id=32 data-nosnippet>32</a>                f.write_str(<span class="string">"account index overflowed during compilation"</span>)
<a href=#33 id=33 data-nosnippet>33</a>            }
<a href=#34 id=34 data-nosnippet>34</a>            CompileError::AddressTableLookupIndexOverflow =&gt; {
<a href=#35 id=35 data-nosnippet>35</a>                f.write_str(<span class="string">"address lookup table index overflowed during compilation"</span>)
<a href=#36 id=36 data-nosnippet>36</a>            }
<a href=#37 id=37 data-nosnippet>37</a>            CompileError::UnknownInstructionKey(key) =&gt; f.write_fmt(<span class="macro">format_args!</span>(
<a href=#38 id=38 data-nosnippet>38</a>                <span class="string">"encountered unknown account key `{0}` during instruction compilation"</span>,
<a href=#39 id=39 data-nosnippet>39</a>                key,
<a href=#40 id=40 data-nosnippet>40</a>            )),
<a href=#41 id=41 data-nosnippet>41</a>        }
<a href=#42 id=42 data-nosnippet>42</a>    }
<a href=#43 id=43 data-nosnippet>43</a>}
<a href=#44 id=44 data-nosnippet>44</a>
<a href=#45 id=45 data-nosnippet>45</a><span class="attr">#[derive(Default, Debug, Clone, PartialEq, Eq)]
<a href=#46 id=46 data-nosnippet>46</a></span><span class="kw">struct </span>CompiledKeyMeta {
<a href=#47 id=47 data-nosnippet>47</a>    is_signer: bool,
<a href=#48 id=48 data-nosnippet>48</a>    is_writable: bool,
<a href=#49 id=49 data-nosnippet>49</a>    is_invoked: bool,
<a href=#50 id=50 data-nosnippet>50</a>}
<a href=#51 id=51 data-nosnippet>51</a>
<a href=#52 id=52 data-nosnippet>52</a><span class="kw">impl </span>CompiledKeys {
<a href=#53 id=53 data-nosnippet>53</a>    <span class="doccomment">/// Compiles the pubkeys referenced by a list of instructions and organizes by
<a href=#54 id=54 data-nosnippet>54</a>    /// signer/non-signer and writable/readonly.
<a href=#55 id=55 data-nosnippet>55</a>    </span><span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">fn </span>compile(instructions: <span class="kw-2">&amp;</span>[Instruction], payer: <span class="prelude-ty">Option</span>&lt;Pubkey&gt;) -&gt; <span class="self">Self </span>{
<a href=#56 id=56 data-nosnippet>56</a>        <span class="kw">let </span><span class="kw-2">mut </span>key_meta_map = BTreeMap::&lt;Pubkey, CompiledKeyMeta&gt;::new();
<a href=#57 id=57 data-nosnippet>57</a>        <span class="kw">for </span>ix <span class="kw">in </span>instructions {
<a href=#58 id=58 data-nosnippet>58</a>            <span class="kw">let </span>meta = key_meta_map.entry(ix.program_id).or_default();
<a href=#59 id=59 data-nosnippet>59</a>            meta.is_invoked = <span class="bool-val">true</span>;
<a href=#60 id=60 data-nosnippet>60</a>            <span class="kw">for </span>account_meta <span class="kw">in </span><span class="kw-2">&amp;</span>ix.accounts {
<a href=#61 id=61 data-nosnippet>61</a>                <span class="kw">let </span>meta = key_meta_map.entry(account_meta.pubkey).or_default();
<a href=#62 id=62 data-nosnippet>62</a>                meta.is_signer |= account_meta.is_signer;
<a href=#63 id=63 data-nosnippet>63</a>                meta.is_writable |= account_meta.is_writable;
<a href=#64 id=64 data-nosnippet>64</a>            }
<a href=#65 id=65 data-nosnippet>65</a>        }
<a href=#66 id=66 data-nosnippet>66</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(payer) = <span class="kw-2">&amp;</span>payer {
<a href=#67 id=67 data-nosnippet>67</a>            <span class="kw">let </span>meta = key_meta_map.entry(<span class="kw-2">*</span>payer).or_default();
<a href=#68 id=68 data-nosnippet>68</a>            meta.is_signer = <span class="bool-val">true</span>;
<a href=#69 id=69 data-nosnippet>69</a>            meta.is_writable = <span class="bool-val">true</span>;
<a href=#70 id=70 data-nosnippet>70</a>        }
<a href=#71 id=71 data-nosnippet>71</a>        <span class="self">Self </span>{
<a href=#72 id=72 data-nosnippet>72</a>            payer,
<a href=#73 id=73 data-nosnippet>73</a>            key_meta_map,
<a href=#74 id=74 data-nosnippet>74</a>        }
<a href=#75 id=75 data-nosnippet>75</a>    }
<a href=#76 id=76 data-nosnippet>76</a>
<a href=#77 id=77 data-nosnippet>77</a>    <span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">fn </span>try_into_message_components(
<a href=#78 id=78 data-nosnippet>78</a>        <span class="self">self</span>,
<a href=#79 id=79 data-nosnippet>79</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;(MessageHeader, Vec&lt;Pubkey&gt;), CompileError&gt; {
<a href=#80 id=80 data-nosnippet>80</a>        <span class="kw">let </span>try_into_u8 = |num: usize| -&gt; <span class="prelude-ty">Result</span>&lt;u8, CompileError&gt; {
<a href=#81 id=81 data-nosnippet>81</a>            u8::try_from(num).map_err(|<span class="kw">_</span>| CompileError::AccountIndexOverflow)
<a href=#82 id=82 data-nosnippet>82</a>        };
<a href=#83 id=83 data-nosnippet>83</a>
<a href=#84 id=84 data-nosnippet>84</a>        <span class="kw">let </span><span class="self">Self </span>{
<a href=#85 id=85 data-nosnippet>85</a>            payer,
<a href=#86 id=86 data-nosnippet>86</a>            <span class="kw-2">mut </span>key_meta_map,
<a href=#87 id=87 data-nosnippet>87</a>        } = <span class="self">self</span>;
<a href=#88 id=88 data-nosnippet>88</a>
<a href=#89 id=89 data-nosnippet>89</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(payer) = <span class="kw-2">&amp;</span>payer {
<a href=#90 id=90 data-nosnippet>90</a>            key_meta_map.remove_entry(payer);
<a href=#91 id=91 data-nosnippet>91</a>        }
<a href=#92 id=92 data-nosnippet>92</a>
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">let </span>writable_signer_keys: Vec&lt;Pubkey&gt; = payer
<a href=#94 id=94 data-nosnippet>94</a>            .into_iter()
<a href=#95 id=95 data-nosnippet>95</a>            .chain(
<a href=#96 id=96 data-nosnippet>96</a>                key_meta_map
<a href=#97 id=97 data-nosnippet>97</a>                    .iter()
<a href=#98 id=98 data-nosnippet>98</a>                    .filter_map(|(key, meta)| (meta.is_signer &amp;&amp; meta.is_writable).then_some(<span class="kw-2">*</span>key)),
<a href=#99 id=99 data-nosnippet>99</a>            )
<a href=#100 id=100 data-nosnippet>100</a>            .collect();
<a href=#101 id=101 data-nosnippet>101</a>        <span class="kw">let </span>readonly_signer_keys: Vec&lt;Pubkey&gt; = key_meta_map
<a href=#102 id=102 data-nosnippet>102</a>            .iter()
<a href=#103 id=103 data-nosnippet>103</a>            .filter_map(|(key, meta)| (meta.is_signer &amp;&amp; !meta.is_writable).then_some(<span class="kw-2">*</span>key))
<a href=#104 id=104 data-nosnippet>104</a>            .collect();
<a href=#105 id=105 data-nosnippet>105</a>        <span class="kw">let </span>writable_non_signer_keys: Vec&lt;Pubkey&gt; = key_meta_map
<a href=#106 id=106 data-nosnippet>106</a>            .iter()
<a href=#107 id=107 data-nosnippet>107</a>            .filter_map(|(key, meta)| (!meta.is_signer &amp;&amp; meta.is_writable).then_some(<span class="kw-2">*</span>key))
<a href=#108 id=108 data-nosnippet>108</a>            .collect();
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">let </span>readonly_non_signer_keys: Vec&lt;Pubkey&gt; = key_meta_map
<a href=#110 id=110 data-nosnippet>110</a>            .iter()
<a href=#111 id=111 data-nosnippet>111</a>            .filter_map(|(key, meta)| (!meta.is_signer &amp;&amp; !meta.is_writable).then_some(<span class="kw-2">*</span>key))
<a href=#112 id=112 data-nosnippet>112</a>            .collect();
<a href=#113 id=113 data-nosnippet>113</a>
<a href=#114 id=114 data-nosnippet>114</a>        <span class="kw">let </span>signers_len = writable_signer_keys
<a href=#115 id=115 data-nosnippet>115</a>            .len()
<a href=#116 id=116 data-nosnippet>116</a>            .saturating_add(readonly_signer_keys.len());
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a>        <span class="kw">let </span>header = MessageHeader {
<a href=#119 id=119 data-nosnippet>119</a>            num_required_signatures: try_into_u8(signers_len)<span class="question-mark">?</span>,
<a href=#120 id=120 data-nosnippet>120</a>            num_readonly_signed_accounts: try_into_u8(readonly_signer_keys.len())<span class="question-mark">?</span>,
<a href=#121 id=121 data-nosnippet>121</a>            num_readonly_unsigned_accounts: try_into_u8(readonly_non_signer_keys.len())<span class="question-mark">?</span>,
<a href=#122 id=122 data-nosnippet>122</a>        };
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>        <span class="kw">let </span>static_account_keys = std::iter::empty()
<a href=#125 id=125 data-nosnippet>125</a>            .chain(writable_signer_keys)
<a href=#126 id=126 data-nosnippet>126</a>            .chain(readonly_signer_keys)
<a href=#127 id=127 data-nosnippet>127</a>            .chain(writable_non_signer_keys)
<a href=#128 id=128 data-nosnippet>128</a>            .chain(readonly_non_signer_keys)
<a href=#129 id=129 data-nosnippet>129</a>            .collect();
<a href=#130 id=130 data-nosnippet>130</a>
<a href=#131 id=131 data-nosnippet>131</a>        <span class="prelude-val">Ok</span>((header, static_account_keys))
<a href=#132 id=132 data-nosnippet>132</a>    }
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a>    <span class="attr">#[cfg(not(target_os = <span class="string">"solana"</span>))]
<a href=#135 id=135 data-nosnippet>135</a>    </span><span class="kw">pub</span>(<span class="kw">crate</span>) <span class="kw">fn </span>try_extract_table_lookup(
<a href=#136 id=136 data-nosnippet>136</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#137 id=137 data-nosnippet>137</a>        lookup_table_account: <span class="kw-2">&amp;</span>AddressLookupTableAccount,
<a href=#138 id=138 data-nosnippet>138</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;(MessageAddressTableLookup, LoadedAddresses)&gt;, CompileError&gt; {
<a href=#139 id=139 data-nosnippet>139</a>        <span class="kw">let </span>(writable_indexes, drained_writable_keys) = <span class="self">self
<a href=#140 id=140 data-nosnippet>140</a>            </span>.try_drain_keys_found_in_lookup_table(<span class="kw-2">&amp;</span>lookup_table_account.addresses, |meta| {
<a href=#141 id=141 data-nosnippet>141</a>                !meta.is_signer &amp;&amp; !meta.is_invoked &amp;&amp; meta.is_writable
<a href=#142 id=142 data-nosnippet>142</a>            })<span class="question-mark">?</span>;
<a href=#143 id=143 data-nosnippet>143</a>        <span class="kw">let </span>(readonly_indexes, drained_readonly_keys) = <span class="self">self
<a href=#144 id=144 data-nosnippet>144</a>            </span>.try_drain_keys_found_in_lookup_table(<span class="kw-2">&amp;</span>lookup_table_account.addresses, |meta| {
<a href=#145 id=145 data-nosnippet>145</a>                !meta.is_signer &amp;&amp; !meta.is_invoked &amp;&amp; !meta.is_writable
<a href=#146 id=146 data-nosnippet>146</a>            })<span class="question-mark">?</span>;
<a href=#147 id=147 data-nosnippet>147</a>
<a href=#148 id=148 data-nosnippet>148</a>        <span class="comment">// Don't extract lookup if no keys were found
<a href=#149 id=149 data-nosnippet>149</a>        </span><span class="kw">if </span>writable_indexes.is_empty() &amp;&amp; readonly_indexes.is_empty() {
<a href=#150 id=150 data-nosnippet>150</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>);
<a href=#151 id=151 data-nosnippet>151</a>        }
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>        <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>((
<a href=#154 id=154 data-nosnippet>154</a>            MessageAddressTableLookup {
<a href=#155 id=155 data-nosnippet>155</a>                account_key: lookup_table_account.key,
<a href=#156 id=156 data-nosnippet>156</a>                writable_indexes,
<a href=#157 id=157 data-nosnippet>157</a>                readonly_indexes,
<a href=#158 id=158 data-nosnippet>158</a>            },
<a href=#159 id=159 data-nosnippet>159</a>            LoadedAddresses {
<a href=#160 id=160 data-nosnippet>160</a>                writable: drained_writable_keys,
<a href=#161 id=161 data-nosnippet>161</a>                readonly: drained_readonly_keys,
<a href=#162 id=162 data-nosnippet>162</a>            },
<a href=#163 id=163 data-nosnippet>163</a>        )))
<a href=#164 id=164 data-nosnippet>164</a>    }
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>    <span class="attr">#[cfg(not(target_os = <span class="string">"solana"</span>))]
<a href=#167 id=167 data-nosnippet>167</a>    </span><span class="kw">fn </span>try_drain_keys_found_in_lookup_table(
<a href=#168 id=168 data-nosnippet>168</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>,
<a href=#169 id=169 data-nosnippet>169</a>        lookup_table_addresses: <span class="kw-2">&amp;</span>[Pubkey],
<a href=#170 id=170 data-nosnippet>170</a>        key_meta_filter: <span class="kw">impl </span>Fn(<span class="kw-2">&amp;</span>CompiledKeyMeta) -&gt; bool,
<a href=#171 id=171 data-nosnippet>171</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;(Vec&lt;u8&gt;, Vec&lt;Pubkey&gt;), CompileError&gt; {
<a href=#172 id=172 data-nosnippet>172</a>        <span class="kw">let </span><span class="kw-2">mut </span>lookup_table_indexes = Vec::new();
<a href=#173 id=173 data-nosnippet>173</a>        <span class="kw">let </span><span class="kw-2">mut </span>drained_keys = Vec::new();
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a>        <span class="kw">for </span>search_key <span class="kw">in </span><span class="self">self
<a href=#176 id=176 data-nosnippet>176</a>            </span>.key_meta_map
<a href=#177 id=177 data-nosnippet>177</a>            .iter()
<a href=#178 id=178 data-nosnippet>178</a>            .filter_map(|(key, meta)| key_meta_filter(meta).then_some(key))
<a href=#179 id=179 data-nosnippet>179</a>        {
<a href=#180 id=180 data-nosnippet>180</a>            <span class="kw">for </span>(key_index, key) <span class="kw">in </span>lookup_table_addresses.iter().enumerate() {
<a href=#181 id=181 data-nosnippet>181</a>                <span class="kw">if </span>key == search_key {
<a href=#182 id=182 data-nosnippet>182</a>                    <span class="kw">let </span>lookup_table_index = u8::try_from(key_index)
<a href=#183 id=183 data-nosnippet>183</a>                        .map_err(|<span class="kw">_</span>| CompileError::AddressTableLookupIndexOverflow)<span class="question-mark">?</span>;
<a href=#184 id=184 data-nosnippet>184</a>
<a href=#185 id=185 data-nosnippet>185</a>                    lookup_table_indexes.push(lookup_table_index);
<a href=#186 id=186 data-nosnippet>186</a>                    drained_keys.push(<span class="kw-2">*</span>search_key);
<a href=#187 id=187 data-nosnippet>187</a>                    <span class="kw">break</span>;
<a href=#188 id=188 data-nosnippet>188</a>                }
<a href=#189 id=189 data-nosnippet>189</a>            }
<a href=#190 id=190 data-nosnippet>190</a>        }
<a href=#191 id=191 data-nosnippet>191</a>
<a href=#192 id=192 data-nosnippet>192</a>        <span class="kw">for </span>key <span class="kw">in </span><span class="kw-2">&amp;</span>drained_keys {
<a href=#193 id=193 data-nosnippet>193</a>            <span class="self">self</span>.key_meta_map.remove_entry(key);
<a href=#194 id=194 data-nosnippet>194</a>        }
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>        <span class="prelude-val">Ok</span>((lookup_table_indexes, drained_keys))
<a href=#197 id=197 data-nosnippet>197</a>    }
<a href=#198 id=198 data-nosnippet>198</a>}
<a href=#199 id=199 data-nosnippet>199</a>
<a href=#200 id=200 data-nosnippet>200</a><span class="attr">#[cfg(test)]
<a href=#201 id=201 data-nosnippet>201</a></span><span class="kw">mod </span>tests {
<a href=#202 id=202 data-nosnippet>202</a>    <span class="kw">use </span>{<span class="kw">super</span>::<span class="kw-2">*</span>, bitflags::bitflags, solana_instruction::AccountMeta};
<a href=#203 id=203 data-nosnippet>203</a>
<a href=#204 id=204 data-nosnippet>204</a>    <span class="macro">bitflags!</span> {
<a href=#205 id=205 data-nosnippet>205</a>        <span class="attr">#[derive(Clone, Copy)]
<a href=#206 id=206 data-nosnippet>206</a>        </span><span class="kw">pub struct </span>KeyFlags: u8 {
<a href=#207 id=207 data-nosnippet>207</a>            <span class="kw">const </span>SIGNER   = <span class="number">0b00000001</span>;
<a href=#208 id=208 data-nosnippet>208</a>            <span class="kw">const </span>WRITABLE = <span class="number">0b00000010</span>;
<a href=#209 id=209 data-nosnippet>209</a>            <span class="kw">const </span>INVOKED  = <span class="number">0b00000100</span>;
<a href=#210 id=210 data-nosnippet>210</a>        }
<a href=#211 id=211 data-nosnippet>211</a>    }
<a href=#212 id=212 data-nosnippet>212</a>
<a href=#213 id=213 data-nosnippet>213</a>    <span class="kw">impl </span>From&lt;KeyFlags&gt; <span class="kw">for </span>CompiledKeyMeta {
<a href=#214 id=214 data-nosnippet>214</a>        <span class="kw">fn </span>from(flags: KeyFlags) -&gt; <span class="self">Self </span>{
<a href=#215 id=215 data-nosnippet>215</a>            <span class="self">Self </span>{
<a href=#216 id=216 data-nosnippet>216</a>                is_signer: flags.contains(KeyFlags::SIGNER),
<a href=#217 id=217 data-nosnippet>217</a>                is_writable: flags.contains(KeyFlags::WRITABLE),
<a href=#218 id=218 data-nosnippet>218</a>                is_invoked: flags.contains(KeyFlags::INVOKED),
<a href=#219 id=219 data-nosnippet>219</a>            }
<a href=#220 id=220 data-nosnippet>220</a>        }
<a href=#221 id=221 data-nosnippet>221</a>    }
<a href=#222 id=222 data-nosnippet>222</a>
<a href=#223 id=223 data-nosnippet>223</a>    <span class="attr">#[test]
<a href=#224 id=224 data-nosnippet>224</a>    </span><span class="kw">fn </span>test_compile_with_dups() {
<a href=#225 id=225 data-nosnippet>225</a>        <span class="kw">let </span>program_id0 = Pubkey::new_unique();
<a href=#226 id=226 data-nosnippet>226</a>        <span class="kw">let </span>program_id1 = Pubkey::new_unique();
<a href=#227 id=227 data-nosnippet>227</a>        <span class="kw">let </span>program_id2 = Pubkey::new_unique();
<a href=#228 id=228 data-nosnippet>228</a>        <span class="kw">let </span>program_id3 = Pubkey::new_unique();
<a href=#229 id=229 data-nosnippet>229</a>        <span class="kw">let </span>id0 = Pubkey::new_unique();
<a href=#230 id=230 data-nosnippet>230</a>        <span class="kw">let </span>id1 = Pubkey::new_unique();
<a href=#231 id=231 data-nosnippet>231</a>        <span class="kw">let </span>id2 = Pubkey::new_unique();
<a href=#232 id=232 data-nosnippet>232</a>        <span class="kw">let </span>id3 = Pubkey::new_unique();
<a href=#233 id=233 data-nosnippet>233</a>        <span class="kw">let </span>compiled_keys = CompiledKeys::compile(
<a href=#234 id=234 data-nosnippet>234</a>            <span class="kw-2">&amp;</span>[
<a href=#235 id=235 data-nosnippet>235</a>                Instruction::new_with_bincode(
<a href=#236 id=236 data-nosnippet>236</a>                    program_id0,
<a href=#237 id=237 data-nosnippet>237</a>                    <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#238 id=238 data-nosnippet>238</a>                    <span class="macro">vec!</span>[
<a href=#239 id=239 data-nosnippet>239</a>                        AccountMeta::new_readonly(id0, <span class="bool-val">false</span>),
<a href=#240 id=240 data-nosnippet>240</a>                        AccountMeta::new_readonly(id1, <span class="bool-val">true</span>),
<a href=#241 id=241 data-nosnippet>241</a>                        AccountMeta::new(id2, <span class="bool-val">false</span>),
<a href=#242 id=242 data-nosnippet>242</a>                        AccountMeta::new(id3, <span class="bool-val">true</span>),
<a href=#243 id=243 data-nosnippet>243</a>                        <span class="comment">// duplicate the account inputs
<a href=#244 id=244 data-nosnippet>244</a>                        </span>AccountMeta::new_readonly(id0, <span class="bool-val">false</span>),
<a href=#245 id=245 data-nosnippet>245</a>                        AccountMeta::new_readonly(id1, <span class="bool-val">true</span>),
<a href=#246 id=246 data-nosnippet>246</a>                        AccountMeta::new(id2, <span class="bool-val">false</span>),
<a href=#247 id=247 data-nosnippet>247</a>                        AccountMeta::new(id3, <span class="bool-val">true</span>),
<a href=#248 id=248 data-nosnippet>248</a>                        <span class="comment">// reference program ids
<a href=#249 id=249 data-nosnippet>249</a>                        </span>AccountMeta::new_readonly(program_id0, <span class="bool-val">false</span>),
<a href=#250 id=250 data-nosnippet>250</a>                        AccountMeta::new_readonly(program_id1, <span class="bool-val">true</span>),
<a href=#251 id=251 data-nosnippet>251</a>                        AccountMeta::new(program_id2, <span class="bool-val">false</span>),
<a href=#252 id=252 data-nosnippet>252</a>                        AccountMeta::new(program_id3, <span class="bool-val">true</span>),
<a href=#253 id=253 data-nosnippet>253</a>                    ],
<a href=#254 id=254 data-nosnippet>254</a>                ),
<a href=#255 id=255 data-nosnippet>255</a>                Instruction::new_with_bincode(program_id1, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[]),
<a href=#256 id=256 data-nosnippet>256</a>                Instruction::new_with_bincode(program_id2, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[]),
<a href=#257 id=257 data-nosnippet>257</a>                Instruction::new_with_bincode(program_id3, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[]),
<a href=#258 id=258 data-nosnippet>258</a>            ],
<a href=#259 id=259 data-nosnippet>259</a>            <span class="prelude-val">None</span>,
<a href=#260 id=260 data-nosnippet>260</a>        );
<a href=#261 id=261 data-nosnippet>261</a>
<a href=#262 id=262 data-nosnippet>262</a>        <span class="macro">assert_eq!</span>(
<a href=#263 id=263 data-nosnippet>263</a>            compiled_keys,
<a href=#264 id=264 data-nosnippet>264</a>            CompiledKeys {
<a href=#265 id=265 data-nosnippet>265</a>                payer: <span class="prelude-val">None</span>,
<a href=#266 id=266 data-nosnippet>266</a>                key_meta_map: BTreeMap::from([
<a href=#267 id=267 data-nosnippet>267</a>                    (id0, KeyFlags::empty().into()),
<a href=#268 id=268 data-nosnippet>268</a>                    (id1, KeyFlags::SIGNER.into()),
<a href=#269 id=269 data-nosnippet>269</a>                    (id2, KeyFlags::WRITABLE.into()),
<a href=#270 id=270 data-nosnippet>270</a>                    (id3, (KeyFlags::SIGNER | KeyFlags::WRITABLE).into()),
<a href=#271 id=271 data-nosnippet>271</a>                    (program_id0, KeyFlags::INVOKED.into()),
<a href=#272 id=272 data-nosnippet>272</a>                    (program_id1, (KeyFlags::INVOKED | KeyFlags::SIGNER).into()),
<a href=#273 id=273 data-nosnippet>273</a>                    (program_id2, (KeyFlags::INVOKED | KeyFlags::WRITABLE).into()),
<a href=#274 id=274 data-nosnippet>274</a>                    (program_id3, KeyFlags::all().into()),
<a href=#275 id=275 data-nosnippet>275</a>                ]),
<a href=#276 id=276 data-nosnippet>276</a>            }
<a href=#277 id=277 data-nosnippet>277</a>        );
<a href=#278 id=278 data-nosnippet>278</a>    }
<a href=#279 id=279 data-nosnippet>279</a>
<a href=#280 id=280 data-nosnippet>280</a>    <span class="attr">#[test]
<a href=#281 id=281 data-nosnippet>281</a>    </span><span class="kw">fn </span>test_compile_with_dup_payer() {
<a href=#282 id=282 data-nosnippet>282</a>        <span class="kw">let </span>program_id = Pubkey::new_unique();
<a href=#283 id=283 data-nosnippet>283</a>        <span class="kw">let </span>payer = Pubkey::new_unique();
<a href=#284 id=284 data-nosnippet>284</a>        <span class="kw">let </span>compiled_keys = CompiledKeys::compile(
<a href=#285 id=285 data-nosnippet>285</a>            <span class="kw-2">&amp;</span>[Instruction::new_with_bincode(
<a href=#286 id=286 data-nosnippet>286</a>                program_id,
<a href=#287 id=287 data-nosnippet>287</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#288 id=288 data-nosnippet>288</a>                <span class="macro">vec!</span>[AccountMeta::new_readonly(payer, <span class="bool-val">false</span>)],
<a href=#289 id=289 data-nosnippet>289</a>            )],
<a href=#290 id=290 data-nosnippet>290</a>            <span class="prelude-val">Some</span>(payer),
<a href=#291 id=291 data-nosnippet>291</a>        );
<a href=#292 id=292 data-nosnippet>292</a>        <span class="macro">assert_eq!</span>(
<a href=#293 id=293 data-nosnippet>293</a>            compiled_keys,
<a href=#294 id=294 data-nosnippet>294</a>            CompiledKeys {
<a href=#295 id=295 data-nosnippet>295</a>                payer: <span class="prelude-val">Some</span>(payer),
<a href=#296 id=296 data-nosnippet>296</a>                key_meta_map: BTreeMap::from([
<a href=#297 id=297 data-nosnippet>297</a>                    (payer, (KeyFlags::SIGNER | KeyFlags::WRITABLE).into()),
<a href=#298 id=298 data-nosnippet>298</a>                    (program_id, KeyFlags::INVOKED.into()),
<a href=#299 id=299 data-nosnippet>299</a>                ]),
<a href=#300 id=300 data-nosnippet>300</a>            }
<a href=#301 id=301 data-nosnippet>301</a>        );
<a href=#302 id=302 data-nosnippet>302</a>    }
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>    <span class="attr">#[test]
<a href=#305 id=305 data-nosnippet>305</a>    </span><span class="kw">fn </span>test_compile_with_dup_signer_mismatch() {
<a href=#306 id=306 data-nosnippet>306</a>        <span class="kw">let </span>program_id = Pubkey::new_unique();
<a href=#307 id=307 data-nosnippet>307</a>        <span class="kw">let </span>id0 = Pubkey::new_unique();
<a href=#308 id=308 data-nosnippet>308</a>        <span class="kw">let </span>compiled_keys = CompiledKeys::compile(
<a href=#309 id=309 data-nosnippet>309</a>            <span class="kw-2">&amp;</span>[Instruction::new_with_bincode(
<a href=#310 id=310 data-nosnippet>310</a>                program_id,
<a href=#311 id=311 data-nosnippet>311</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#312 id=312 data-nosnippet>312</a>                <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>), AccountMeta::new(id0, <span class="bool-val">true</span>)],
<a href=#313 id=313 data-nosnippet>313</a>            )],
<a href=#314 id=314 data-nosnippet>314</a>            <span class="prelude-val">None</span>,
<a href=#315 id=315 data-nosnippet>315</a>        );
<a href=#316 id=316 data-nosnippet>316</a>
<a href=#317 id=317 data-nosnippet>317</a>        <span class="comment">// Ensure the dup writable key is a signer
<a href=#318 id=318 data-nosnippet>318</a>        </span><span class="macro">assert_eq!</span>(
<a href=#319 id=319 data-nosnippet>319</a>            compiled_keys,
<a href=#320 id=320 data-nosnippet>320</a>            CompiledKeys {
<a href=#321 id=321 data-nosnippet>321</a>                payer: <span class="prelude-val">None</span>,
<a href=#322 id=322 data-nosnippet>322</a>                key_meta_map: BTreeMap::from([
<a href=#323 id=323 data-nosnippet>323</a>                    (id0, (KeyFlags::SIGNER | KeyFlags::WRITABLE).into()),
<a href=#324 id=324 data-nosnippet>324</a>                    (program_id, KeyFlags::INVOKED.into()),
<a href=#325 id=325 data-nosnippet>325</a>                ]),
<a href=#326 id=326 data-nosnippet>326</a>            }
<a href=#327 id=327 data-nosnippet>327</a>        );
<a href=#328 id=328 data-nosnippet>328</a>    }
<a href=#329 id=329 data-nosnippet>329</a>
<a href=#330 id=330 data-nosnippet>330</a>    <span class="attr">#[test]
<a href=#331 id=331 data-nosnippet>331</a>    </span><span class="kw">fn </span>test_compile_with_dup_signer_writable_mismatch() {
<a href=#332 id=332 data-nosnippet>332</a>        <span class="kw">let </span>program_id = Pubkey::new_unique();
<a href=#333 id=333 data-nosnippet>333</a>        <span class="kw">let </span>id0 = Pubkey::new_unique();
<a href=#334 id=334 data-nosnippet>334</a>        <span class="kw">let </span>compiled_keys = CompiledKeys::compile(
<a href=#335 id=335 data-nosnippet>335</a>            <span class="kw-2">&amp;</span>[Instruction::new_with_bincode(
<a href=#336 id=336 data-nosnippet>336</a>                program_id,
<a href=#337 id=337 data-nosnippet>337</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#338 id=338 data-nosnippet>338</a>                <span class="macro">vec!</span>[
<a href=#339 id=339 data-nosnippet>339</a>                    AccountMeta::new_readonly(id0, <span class="bool-val">true</span>),
<a href=#340 id=340 data-nosnippet>340</a>                    AccountMeta::new(id0, <span class="bool-val">true</span>),
<a href=#341 id=341 data-nosnippet>341</a>                ],
<a href=#342 id=342 data-nosnippet>342</a>            )],
<a href=#343 id=343 data-nosnippet>343</a>            <span class="prelude-val">None</span>,
<a href=#344 id=344 data-nosnippet>344</a>        );
<a href=#345 id=345 data-nosnippet>345</a>
<a href=#346 id=346 data-nosnippet>346</a>        <span class="comment">// Ensure the dup signer key is writable
<a href=#347 id=347 data-nosnippet>347</a>        </span><span class="macro">assert_eq!</span>(
<a href=#348 id=348 data-nosnippet>348</a>            compiled_keys,
<a href=#349 id=349 data-nosnippet>349</a>            CompiledKeys {
<a href=#350 id=350 data-nosnippet>350</a>                payer: <span class="prelude-val">None</span>,
<a href=#351 id=351 data-nosnippet>351</a>                key_meta_map: BTreeMap::from([
<a href=#352 id=352 data-nosnippet>352</a>                    (id0, (KeyFlags::SIGNER | KeyFlags::WRITABLE).into()),
<a href=#353 id=353 data-nosnippet>353</a>                    (program_id, KeyFlags::INVOKED.into()),
<a href=#354 id=354 data-nosnippet>354</a>                ]),
<a href=#355 id=355 data-nosnippet>355</a>            }
<a href=#356 id=356 data-nosnippet>356</a>        );
<a href=#357 id=357 data-nosnippet>357</a>    }
<a href=#358 id=358 data-nosnippet>358</a>
<a href=#359 id=359 data-nosnippet>359</a>    <span class="attr">#[test]
<a href=#360 id=360 data-nosnippet>360</a>    </span><span class="kw">fn </span>test_compile_with_dup_nonsigner_writable_mismatch() {
<a href=#361 id=361 data-nosnippet>361</a>        <span class="kw">let </span>program_id = Pubkey::new_unique();
<a href=#362 id=362 data-nosnippet>362</a>        <span class="kw">let </span>id0 = Pubkey::new_unique();
<a href=#363 id=363 data-nosnippet>363</a>        <span class="kw">let </span>compiled_keys = CompiledKeys::compile(
<a href=#364 id=364 data-nosnippet>364</a>            <span class="kw-2">&amp;</span>[
<a href=#365 id=365 data-nosnippet>365</a>                Instruction::new_with_bincode(
<a href=#366 id=366 data-nosnippet>366</a>                    program_id,
<a href=#367 id=367 data-nosnippet>367</a>                    <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#368 id=368 data-nosnippet>368</a>                    <span class="macro">vec!</span>[
<a href=#369 id=369 data-nosnippet>369</a>                        AccountMeta::new_readonly(id0, <span class="bool-val">false</span>),
<a href=#370 id=370 data-nosnippet>370</a>                        AccountMeta::new(id0, <span class="bool-val">false</span>),
<a href=#371 id=371 data-nosnippet>371</a>                    ],
<a href=#372 id=372 data-nosnippet>372</a>                ),
<a href=#373 id=373 data-nosnippet>373</a>                Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>)]),
<a href=#374 id=374 data-nosnippet>374</a>            ],
<a href=#375 id=375 data-nosnippet>375</a>            <span class="prelude-val">None</span>,
<a href=#376 id=376 data-nosnippet>376</a>        );
<a href=#377 id=377 data-nosnippet>377</a>
<a href=#378 id=378 data-nosnippet>378</a>        <span class="comment">// Ensure the dup nonsigner key is writable
<a href=#379 id=379 data-nosnippet>379</a>        </span><span class="macro">assert_eq!</span>(
<a href=#380 id=380 data-nosnippet>380</a>            compiled_keys,
<a href=#381 id=381 data-nosnippet>381</a>            CompiledKeys {
<a href=#382 id=382 data-nosnippet>382</a>                payer: <span class="prelude-val">None</span>,
<a href=#383 id=383 data-nosnippet>383</a>                key_meta_map: BTreeMap::from([
<a href=#384 id=384 data-nosnippet>384</a>                    (id0, KeyFlags::WRITABLE.into()),
<a href=#385 id=385 data-nosnippet>385</a>                    (program_id, KeyFlags::INVOKED.into()),
<a href=#386 id=386 data-nosnippet>386</a>                ]),
<a href=#387 id=387 data-nosnippet>387</a>            }
<a href=#388 id=388 data-nosnippet>388</a>        );
<a href=#389 id=389 data-nosnippet>389</a>    }
<a href=#390 id=390 data-nosnippet>390</a>
<a href=#391 id=391 data-nosnippet>391</a>    <span class="attr">#[test]
<a href=#392 id=392 data-nosnippet>392</a>    </span><span class="kw">fn </span>test_try_into_message_components() {
<a href=#393 id=393 data-nosnippet>393</a>        <span class="kw">let </span>keys = <span class="macro">vec!</span>[
<a href=#394 id=394 data-nosnippet>394</a>            Pubkey::new_unique(),
<a href=#395 id=395 data-nosnippet>395</a>            Pubkey::new_unique(),
<a href=#396 id=396 data-nosnippet>396</a>            Pubkey::new_unique(),
<a href=#397 id=397 data-nosnippet>397</a>            Pubkey::new_unique(),
<a href=#398 id=398 data-nosnippet>398</a>        ];
<a href=#399 id=399 data-nosnippet>399</a>
<a href=#400 id=400 data-nosnippet>400</a>        <span class="kw">let </span>compiled_keys = CompiledKeys {
<a href=#401 id=401 data-nosnippet>401</a>            payer: <span class="prelude-val">None</span>,
<a href=#402 id=402 data-nosnippet>402</a>            key_meta_map: BTreeMap::from([
<a href=#403 id=403 data-nosnippet>403</a>                (keys[<span class="number">0</span>], (KeyFlags::SIGNER | KeyFlags::WRITABLE).into()),
<a href=#404 id=404 data-nosnippet>404</a>                (keys[<span class="number">1</span>], KeyFlags::SIGNER.into()),
<a href=#405 id=405 data-nosnippet>405</a>                (keys[<span class="number">2</span>], KeyFlags::WRITABLE.into()),
<a href=#406 id=406 data-nosnippet>406</a>                (keys[<span class="number">3</span>], KeyFlags::empty().into()),
<a href=#407 id=407 data-nosnippet>407</a>            ]),
<a href=#408 id=408 data-nosnippet>408</a>        };
<a href=#409 id=409 data-nosnippet>409</a>
<a href=#410 id=410 data-nosnippet>410</a>        <span class="kw">let </span>result = compiled_keys.try_into_message_components();
<a href=#411 id=411 data-nosnippet>411</a>        <span class="macro">assert_eq!</span>(result.as_ref().err(), <span class="prelude-val">None</span>);
<a href=#412 id=412 data-nosnippet>412</a>        <span class="kw">let </span>(header, static_keys) = result.unwrap();
<a href=#413 id=413 data-nosnippet>413</a>
<a href=#414 id=414 data-nosnippet>414</a>        <span class="macro">assert_eq!</span>(static_keys, keys);
<a href=#415 id=415 data-nosnippet>415</a>        <span class="macro">assert_eq!</span>(
<a href=#416 id=416 data-nosnippet>416</a>            header,
<a href=#417 id=417 data-nosnippet>417</a>            MessageHeader {
<a href=#418 id=418 data-nosnippet>418</a>                num_required_signatures: <span class="number">2</span>,
<a href=#419 id=419 data-nosnippet>419</a>                num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#420 id=420 data-nosnippet>420</a>                num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#421 id=421 data-nosnippet>421</a>            }
<a href=#422 id=422 data-nosnippet>422</a>        );
<a href=#423 id=423 data-nosnippet>423</a>    }
<a href=#424 id=424 data-nosnippet>424</a>
<a href=#425 id=425 data-nosnippet>425</a>    <span class="attr">#[test]
<a href=#426 id=426 data-nosnippet>426</a>    </span><span class="kw">fn </span>test_try_into_message_components_with_too_many_keys() {
<a href=#427 id=427 data-nosnippet>427</a>        <span class="kw">const </span>TOO_MANY_KEYS: usize = <span class="number">257</span>;
<a href=#428 id=428 data-nosnippet>428</a>
<a href=#429 id=429 data-nosnippet>429</a>        <span class="kw">for </span>key_flags <span class="kw">in </span>[
<a href=#430 id=430 data-nosnippet>430</a>            KeyFlags::WRITABLE | KeyFlags::SIGNER,
<a href=#431 id=431 data-nosnippet>431</a>            KeyFlags::SIGNER,
<a href=#432 id=432 data-nosnippet>432</a>            <span class="comment">// skip writable_non_signer_keys because it isn't used for creating header values
<a href=#433 id=433 data-nosnippet>433</a>            </span>KeyFlags::empty(),
<a href=#434 id=434 data-nosnippet>434</a>        ] {
<a href=#435 id=435 data-nosnippet>435</a>            <span class="kw">let </span>test_keys = CompiledKeys {
<a href=#436 id=436 data-nosnippet>436</a>                payer: <span class="prelude-val">None</span>,
<a href=#437 id=437 data-nosnippet>437</a>                key_meta_map: BTreeMap::from_iter(
<a href=#438 id=438 data-nosnippet>438</a>                    (<span class="number">0</span>..TOO_MANY_KEYS).map(|<span class="kw">_</span>| (Pubkey::new_unique(), key_flags.into())),
<a href=#439 id=439 data-nosnippet>439</a>                ),
<a href=#440 id=440 data-nosnippet>440</a>            };
<a href=#441 id=441 data-nosnippet>441</a>
<a href=#442 id=442 data-nosnippet>442</a>            <span class="macro">assert_eq!</span>(
<a href=#443 id=443 data-nosnippet>443</a>                test_keys.try_into_message_components(),
<a href=#444 id=444 data-nosnippet>444</a>                <span class="prelude-val">Err</span>(CompileError::AccountIndexOverflow)
<a href=#445 id=445 data-nosnippet>445</a>            );
<a href=#446 id=446 data-nosnippet>446</a>        }
<a href=#447 id=447 data-nosnippet>447</a>    }
<a href=#448 id=448 data-nosnippet>448</a>
<a href=#449 id=449 data-nosnippet>449</a>    <span class="attr">#[test]
<a href=#450 id=450 data-nosnippet>450</a>    </span><span class="kw">fn </span>test_try_extract_table_lookup() {
<a href=#451 id=451 data-nosnippet>451</a>        <span class="kw">let </span>keys = <span class="macro">vec!</span>[
<a href=#452 id=452 data-nosnippet>452</a>            Pubkey::new_unique(),
<a href=#453 id=453 data-nosnippet>453</a>            Pubkey::new_unique(),
<a href=#454 id=454 data-nosnippet>454</a>            Pubkey::new_unique(),
<a href=#455 id=455 data-nosnippet>455</a>            Pubkey::new_unique(),
<a href=#456 id=456 data-nosnippet>456</a>            Pubkey::new_unique(),
<a href=#457 id=457 data-nosnippet>457</a>            Pubkey::new_unique(),
<a href=#458 id=458 data-nosnippet>458</a>        ];
<a href=#459 id=459 data-nosnippet>459</a>
<a href=#460 id=460 data-nosnippet>460</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys {
<a href=#461 id=461 data-nosnippet>461</a>            payer: <span class="prelude-val">None</span>,
<a href=#462 id=462 data-nosnippet>462</a>            key_meta_map: BTreeMap::from([
<a href=#463 id=463 data-nosnippet>463</a>                (keys[<span class="number">0</span>], (KeyFlags::SIGNER | KeyFlags::WRITABLE).into()),
<a href=#464 id=464 data-nosnippet>464</a>                (keys[<span class="number">1</span>], KeyFlags::SIGNER.into()),
<a href=#465 id=465 data-nosnippet>465</a>                (keys[<span class="number">2</span>], KeyFlags::WRITABLE.into()),
<a href=#466 id=466 data-nosnippet>466</a>                (keys[<span class="number">3</span>], KeyFlags::empty().into()),
<a href=#467 id=467 data-nosnippet>467</a>                (keys[<span class="number">4</span>], (KeyFlags::INVOKED | KeyFlags::WRITABLE).into()),
<a href=#468 id=468 data-nosnippet>468</a>                (keys[<span class="number">5</span>], (KeyFlags::INVOKED).into()),
<a href=#469 id=469 data-nosnippet>469</a>            ]),
<a href=#470 id=470 data-nosnippet>470</a>        };
<a href=#471 id=471 data-nosnippet>471</a>
<a href=#472 id=472 data-nosnippet>472</a>        <span class="comment">// add some duplicates to ensure lowest index is selected
<a href=#473 id=473 data-nosnippet>473</a>        </span><span class="kw">let </span>addresses = [keys.clone(), keys.clone()].concat();
<a href=#474 id=474 data-nosnippet>474</a>        <span class="kw">let </span>lookup_table_account = AddressLookupTableAccount {
<a href=#475 id=475 data-nosnippet>475</a>            key: Pubkey::new_unique(),
<a href=#476 id=476 data-nosnippet>476</a>            addresses,
<a href=#477 id=477 data-nosnippet>477</a>        };
<a href=#478 id=478 data-nosnippet>478</a>
<a href=#479 id=479 data-nosnippet>479</a>        <span class="macro">assert_eq!</span>(
<a href=#480 id=480 data-nosnippet>480</a>            compiled_keys.try_extract_table_lookup(<span class="kw-2">&amp;</span>lookup_table_account),
<a href=#481 id=481 data-nosnippet>481</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>((
<a href=#482 id=482 data-nosnippet>482</a>                MessageAddressTableLookup {
<a href=#483 id=483 data-nosnippet>483</a>                    account_key: lookup_table_account.key,
<a href=#484 id=484 data-nosnippet>484</a>                    writable_indexes: <span class="macro">vec!</span>[<span class="number">2</span>],
<a href=#485 id=485 data-nosnippet>485</a>                    readonly_indexes: <span class="macro">vec!</span>[<span class="number">3</span>],
<a href=#486 id=486 data-nosnippet>486</a>                },
<a href=#487 id=487 data-nosnippet>487</a>                LoadedAddresses {
<a href=#488 id=488 data-nosnippet>488</a>                    writable: <span class="macro">vec!</span>[keys[<span class="number">2</span>]],
<a href=#489 id=489 data-nosnippet>489</a>                    readonly: <span class="macro">vec!</span>[keys[<span class="number">3</span>]],
<a href=#490 id=490 data-nosnippet>490</a>                },
<a href=#491 id=491 data-nosnippet>491</a>            )))
<a href=#492 id=492 data-nosnippet>492</a>        );
<a href=#493 id=493 data-nosnippet>493</a>
<a href=#494 id=494 data-nosnippet>494</a>        <span class="macro">assert_eq!</span>(compiled_keys.key_meta_map.len(), <span class="number">4</span>);
<a href=#495 id=495 data-nosnippet>495</a>        <span class="macro">assert!</span>(!compiled_keys.key_meta_map.contains_key(<span class="kw-2">&amp;</span>keys[<span class="number">2</span>]));
<a href=#496 id=496 data-nosnippet>496</a>        <span class="macro">assert!</span>(!compiled_keys.key_meta_map.contains_key(<span class="kw-2">&amp;</span>keys[<span class="number">3</span>]));
<a href=#497 id=497 data-nosnippet>497</a>    }
<a href=#498 id=498 data-nosnippet>498</a>
<a href=#499 id=499 data-nosnippet>499</a>    <span class="attr">#[test]
<a href=#500 id=500 data-nosnippet>500</a>    </span><span class="kw">fn </span>test_try_extract_table_lookup_returns_none() {
<a href=#501 id=501 data-nosnippet>501</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys {
<a href=#502 id=502 data-nosnippet>502</a>            payer: <span class="prelude-val">None</span>,
<a href=#503 id=503 data-nosnippet>503</a>            key_meta_map: BTreeMap::from([
<a href=#504 id=504 data-nosnippet>504</a>                (Pubkey::new_unique(), KeyFlags::WRITABLE.into()),
<a href=#505 id=505 data-nosnippet>505</a>                (Pubkey::new_unique(), KeyFlags::empty().into()),
<a href=#506 id=506 data-nosnippet>506</a>            ]),
<a href=#507 id=507 data-nosnippet>507</a>        };
<a href=#508 id=508 data-nosnippet>508</a>
<a href=#509 id=509 data-nosnippet>509</a>        <span class="kw">let </span>lookup_table_account = AddressLookupTableAccount {
<a href=#510 id=510 data-nosnippet>510</a>            key: Pubkey::new_unique(),
<a href=#511 id=511 data-nosnippet>511</a>            addresses: <span class="macro">vec!</span>[],
<a href=#512 id=512 data-nosnippet>512</a>        };
<a href=#513 id=513 data-nosnippet>513</a>
<a href=#514 id=514 data-nosnippet>514</a>        <span class="kw">let </span>expected_compiled_keys = compiled_keys.clone();
<a href=#515 id=515 data-nosnippet>515</a>        <span class="macro">assert_eq!</span>(
<a href=#516 id=516 data-nosnippet>516</a>            compiled_keys.try_extract_table_lookup(<span class="kw-2">&amp;</span>lookup_table_account),
<a href=#517 id=517 data-nosnippet>517</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#518 id=518 data-nosnippet>518</a>        );
<a href=#519 id=519 data-nosnippet>519</a>        <span class="macro">assert_eq!</span>(compiled_keys, expected_compiled_keys);
<a href=#520 id=520 data-nosnippet>520</a>    }
<a href=#521 id=521 data-nosnippet>521</a>
<a href=#522 id=522 data-nosnippet>522</a>    <span class="attr">#[test]
<a href=#523 id=523 data-nosnippet>523</a>    </span><span class="kw">fn </span>test_try_extract_table_lookup_for_invalid_table() {
<a href=#524 id=524 data-nosnippet>524</a>        <span class="kw">let </span>writable_key = Pubkey::new_unique();
<a href=#525 id=525 data-nosnippet>525</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys {
<a href=#526 id=526 data-nosnippet>526</a>            payer: <span class="prelude-val">None</span>,
<a href=#527 id=527 data-nosnippet>527</a>            key_meta_map: BTreeMap::from([
<a href=#528 id=528 data-nosnippet>528</a>                (writable_key, KeyFlags::WRITABLE.into()),
<a href=#529 id=529 data-nosnippet>529</a>                (Pubkey::new_unique(), KeyFlags::empty().into()),
<a href=#530 id=530 data-nosnippet>530</a>            ]),
<a href=#531 id=531 data-nosnippet>531</a>        };
<a href=#532 id=532 data-nosnippet>532</a>
<a href=#533 id=533 data-nosnippet>533</a>        <span class="kw">const </span>MAX_LENGTH_WITHOUT_OVERFLOW: usize = u8::MAX <span class="kw">as </span>usize + <span class="number">1</span>;
<a href=#534 id=534 data-nosnippet>534</a>        <span class="kw">let </span><span class="kw-2">mut </span>addresses = <span class="macro">vec!</span>[Pubkey::default(); MAX_LENGTH_WITHOUT_OVERFLOW];
<a href=#535 id=535 data-nosnippet>535</a>        addresses.push(writable_key);
<a href=#536 id=536 data-nosnippet>536</a>
<a href=#537 id=537 data-nosnippet>537</a>        <span class="kw">let </span>lookup_table_account = AddressLookupTableAccount {
<a href=#538 id=538 data-nosnippet>538</a>            key: Pubkey::new_unique(),
<a href=#539 id=539 data-nosnippet>539</a>            addresses,
<a href=#540 id=540 data-nosnippet>540</a>        };
<a href=#541 id=541 data-nosnippet>541</a>
<a href=#542 id=542 data-nosnippet>542</a>        <span class="kw">let </span>expected_compiled_keys = compiled_keys.clone();
<a href=#543 id=543 data-nosnippet>543</a>        <span class="macro">assert_eq!</span>(
<a href=#544 id=544 data-nosnippet>544</a>            compiled_keys.try_extract_table_lookup(<span class="kw-2">&amp;</span>lookup_table_account),
<a href=#545 id=545 data-nosnippet>545</a>            <span class="prelude-val">Err</span>(CompileError::AddressTableLookupIndexOverflow),
<a href=#546 id=546 data-nosnippet>546</a>        );
<a href=#547 id=547 data-nosnippet>547</a>        <span class="macro">assert_eq!</span>(compiled_keys, expected_compiled_keys);
<a href=#548 id=548 data-nosnippet>548</a>    }
<a href=#549 id=549 data-nosnippet>549</a>
<a href=#550 id=550 data-nosnippet>550</a>    <span class="attr">#[test]
<a href=#551 id=551 data-nosnippet>551</a>    </span><span class="kw">fn </span>test_try_drain_keys_found_in_lookup_table() {
<a href=#552 id=552 data-nosnippet>552</a>        <span class="kw">let </span>orig_keys = [
<a href=#553 id=553 data-nosnippet>553</a>            Pubkey::new_unique(),
<a href=#554 id=554 data-nosnippet>554</a>            Pubkey::new_unique(),
<a href=#555 id=555 data-nosnippet>555</a>            Pubkey::new_unique(),
<a href=#556 id=556 data-nosnippet>556</a>            Pubkey::new_unique(),
<a href=#557 id=557 data-nosnippet>557</a>            Pubkey::new_unique(),
<a href=#558 id=558 data-nosnippet>558</a>        ];
<a href=#559 id=559 data-nosnippet>559</a>
<a href=#560 id=560 data-nosnippet>560</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys {
<a href=#561 id=561 data-nosnippet>561</a>            payer: <span class="prelude-val">None</span>,
<a href=#562 id=562 data-nosnippet>562</a>            key_meta_map: BTreeMap::from([
<a href=#563 id=563 data-nosnippet>563</a>                (orig_keys[<span class="number">0</span>], KeyFlags::empty().into()),
<a href=#564 id=564 data-nosnippet>564</a>                (orig_keys[<span class="number">1</span>], KeyFlags::WRITABLE.into()),
<a href=#565 id=565 data-nosnippet>565</a>                (orig_keys[<span class="number">2</span>], KeyFlags::WRITABLE.into()),
<a href=#566 id=566 data-nosnippet>566</a>                (orig_keys[<span class="number">3</span>], KeyFlags::empty().into()),
<a href=#567 id=567 data-nosnippet>567</a>                (orig_keys[<span class="number">4</span>], KeyFlags::empty().into()),
<a href=#568 id=568 data-nosnippet>568</a>            ]),
<a href=#569 id=569 data-nosnippet>569</a>        };
<a href=#570 id=570 data-nosnippet>570</a>
<a href=#571 id=571 data-nosnippet>571</a>        <span class="kw">let </span>lookup_table_addresses = <span class="macro">vec!</span>[
<a href=#572 id=572 data-nosnippet>572</a>            Pubkey::new_unique(),
<a href=#573 id=573 data-nosnippet>573</a>            orig_keys[<span class="number">0</span>],
<a href=#574 id=574 data-nosnippet>574</a>            Pubkey::new_unique(),
<a href=#575 id=575 data-nosnippet>575</a>            orig_keys[<span class="number">4</span>],
<a href=#576 id=576 data-nosnippet>576</a>            Pubkey::new_unique(),
<a href=#577 id=577 data-nosnippet>577</a>            orig_keys[<span class="number">2</span>],
<a href=#578 id=578 data-nosnippet>578</a>            Pubkey::new_unique(),
<a href=#579 id=579 data-nosnippet>579</a>        ];
<a href=#580 id=580 data-nosnippet>580</a>
<a href=#581 id=581 data-nosnippet>581</a>        <span class="kw">let </span>drain_result = compiled_keys
<a href=#582 id=582 data-nosnippet>582</a>            .try_drain_keys_found_in_lookup_table(<span class="kw-2">&amp;</span>lookup_table_addresses, |meta| {
<a href=#583 id=583 data-nosnippet>583</a>                !meta.is_writable
<a href=#584 id=584 data-nosnippet>584</a>            });
<a href=#585 id=585 data-nosnippet>585</a>        <span class="macro">assert_eq!</span>(drain_result.as_ref().err(), <span class="prelude-val">None</span>);
<a href=#586 id=586 data-nosnippet>586</a>        <span class="kw">let </span>(lookup_table_indexes, drained_keys) = drain_result.unwrap();
<a href=#587 id=587 data-nosnippet>587</a>
<a href=#588 id=588 data-nosnippet>588</a>        <span class="macro">assert_eq!</span>(
<a href=#589 id=589 data-nosnippet>589</a>            compiled_keys.key_meta_map.keys().collect::&lt;Vec&lt;<span class="kw-2">&amp;</span><span class="kw">_</span>&gt;&gt;(),
<a href=#590 id=590 data-nosnippet>590</a>            <span class="macro">vec!</span>[<span class="kw-2">&amp;</span>orig_keys[<span class="number">1</span>], <span class="kw-2">&amp;</span>orig_keys[<span class="number">2</span>], <span class="kw-2">&amp;</span>orig_keys[<span class="number">3</span>]]
<a href=#591 id=591 data-nosnippet>591</a>        );
<a href=#592 id=592 data-nosnippet>592</a>        <span class="macro">assert_eq!</span>(drained_keys, <span class="macro">vec!</span>[orig_keys[<span class="number">0</span>], orig_keys[<span class="number">4</span>]]);
<a href=#593 id=593 data-nosnippet>593</a>        <span class="macro">assert_eq!</span>(lookup_table_indexes, <span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">3</span>]);
<a href=#594 id=594 data-nosnippet>594</a>    }
<a href=#595 id=595 data-nosnippet>595</a>
<a href=#596 id=596 data-nosnippet>596</a>    <span class="attr">#[test]
<a href=#597 id=597 data-nosnippet>597</a>    </span><span class="kw">fn </span>test_try_drain_keys_found_in_lookup_table_with_empty_keys() {
<a href=#598 id=598 data-nosnippet>598</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys::default();
<a href=#599 id=599 data-nosnippet>599</a>
<a href=#600 id=600 data-nosnippet>600</a>        <span class="kw">let </span>lookup_table_addresses = <span class="macro">vec!</span>[
<a href=#601 id=601 data-nosnippet>601</a>            Pubkey::new_unique(),
<a href=#602 id=602 data-nosnippet>602</a>            Pubkey::new_unique(),
<a href=#603 id=603 data-nosnippet>603</a>            Pubkey::new_unique(),
<a href=#604 id=604 data-nosnippet>604</a>        ];
<a href=#605 id=605 data-nosnippet>605</a>
<a href=#606 id=606 data-nosnippet>606</a>        <span class="kw">let </span>drain_result =
<a href=#607 id=607 data-nosnippet>607</a>            compiled_keys.try_drain_keys_found_in_lookup_table(<span class="kw-2">&amp;</span>lookup_table_addresses, |<span class="kw">_</span>| <span class="bool-val">true</span>);
<a href=#608 id=608 data-nosnippet>608</a>        <span class="macro">assert_eq!</span>(drain_result.as_ref().err(), <span class="prelude-val">None</span>);
<a href=#609 id=609 data-nosnippet>609</a>        <span class="kw">let </span>(lookup_table_indexes, drained_keys) = drain_result.unwrap();
<a href=#610 id=610 data-nosnippet>610</a>
<a href=#611 id=611 data-nosnippet>611</a>        <span class="macro">assert!</span>(drained_keys.is_empty());
<a href=#612 id=612 data-nosnippet>612</a>        <span class="macro">assert!</span>(lookup_table_indexes.is_empty());
<a href=#613 id=613 data-nosnippet>613</a>    }
<a href=#614 id=614 data-nosnippet>614</a>
<a href=#615 id=615 data-nosnippet>615</a>    <span class="attr">#[test]
<a href=#616 id=616 data-nosnippet>616</a>    </span><span class="kw">fn </span>test_try_drain_keys_found_in_lookup_table_with_empty_table() {
<a href=#617 id=617 data-nosnippet>617</a>        <span class="kw">let </span>original_keys = [
<a href=#618 id=618 data-nosnippet>618</a>            Pubkey::new_unique(),
<a href=#619 id=619 data-nosnippet>619</a>            Pubkey::new_unique(),
<a href=#620 id=620 data-nosnippet>620</a>            Pubkey::new_unique(),
<a href=#621 id=621 data-nosnippet>621</a>        ];
<a href=#622 id=622 data-nosnippet>622</a>
<a href=#623 id=623 data-nosnippet>623</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys {
<a href=#624 id=624 data-nosnippet>624</a>            payer: <span class="prelude-val">None</span>,
<a href=#625 id=625 data-nosnippet>625</a>            key_meta_map: BTreeMap::from_iter(
<a href=#626 id=626 data-nosnippet>626</a>                original_keys
<a href=#627 id=627 data-nosnippet>627</a>                    .iter()
<a href=#628 id=628 data-nosnippet>628</a>                    .map(|key| (<span class="kw-2">*</span>key, CompiledKeyMeta::default())),
<a href=#629 id=629 data-nosnippet>629</a>            ),
<a href=#630 id=630 data-nosnippet>630</a>        };
<a href=#631 id=631 data-nosnippet>631</a>
<a href=#632 id=632 data-nosnippet>632</a>        <span class="kw">let </span>lookup_table_addresses = <span class="macro">vec!</span>[];
<a href=#633 id=633 data-nosnippet>633</a>
<a href=#634 id=634 data-nosnippet>634</a>        <span class="kw">let </span>drain_result =
<a href=#635 id=635 data-nosnippet>635</a>            compiled_keys.try_drain_keys_found_in_lookup_table(<span class="kw-2">&amp;</span>lookup_table_addresses, |<span class="kw">_</span>| <span class="bool-val">true</span>);
<a href=#636 id=636 data-nosnippet>636</a>        <span class="macro">assert_eq!</span>(drain_result.as_ref().err(), <span class="prelude-val">None</span>);
<a href=#637 id=637 data-nosnippet>637</a>        <span class="kw">let </span>(lookup_table_indexes, drained_keys) = drain_result.unwrap();
<a href=#638 id=638 data-nosnippet>638</a>
<a href=#639 id=639 data-nosnippet>639</a>        <span class="macro">assert_eq!</span>(compiled_keys.key_meta_map.len(), original_keys.len());
<a href=#640 id=640 data-nosnippet>640</a>        <span class="macro">assert!</span>(drained_keys.is_empty());
<a href=#641 id=641 data-nosnippet>641</a>        <span class="macro">assert!</span>(lookup_table_indexes.is_empty());
<a href=#642 id=642 data-nosnippet>642</a>    }
<a href=#643 id=643 data-nosnippet>643</a>
<a href=#644 id=644 data-nosnippet>644</a>    <span class="attr">#[test]
<a href=#645 id=645 data-nosnippet>645</a>    </span><span class="kw">fn </span>test_try_drain_keys_found_in_lookup_table_with_too_many_addresses() {
<a href=#646 id=646 data-nosnippet>646</a>        <span class="kw">let </span>key = Pubkey::new_unique();
<a href=#647 id=647 data-nosnippet>647</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys {
<a href=#648 id=648 data-nosnippet>648</a>            payer: <span class="prelude-val">None</span>,
<a href=#649 id=649 data-nosnippet>649</a>            key_meta_map: BTreeMap::from([(key, CompiledKeyMeta::default())]),
<a href=#650 id=650 data-nosnippet>650</a>        };
<a href=#651 id=651 data-nosnippet>651</a>
<a href=#652 id=652 data-nosnippet>652</a>        <span class="kw">const </span>MAX_LENGTH_WITHOUT_OVERFLOW: usize = u8::MAX <span class="kw">as </span>usize + <span class="number">1</span>;
<a href=#653 id=653 data-nosnippet>653</a>        <span class="kw">let </span><span class="kw-2">mut </span>lookup_table_addresses = <span class="macro">vec!</span>[Pubkey::default(); MAX_LENGTH_WITHOUT_OVERFLOW];
<a href=#654 id=654 data-nosnippet>654</a>        lookup_table_addresses.push(key);
<a href=#655 id=655 data-nosnippet>655</a>
<a href=#656 id=656 data-nosnippet>656</a>        <span class="kw">let </span>drain_result =
<a href=#657 id=657 data-nosnippet>657</a>            compiled_keys.try_drain_keys_found_in_lookup_table(<span class="kw-2">&amp;</span>lookup_table_addresses, |<span class="kw">_</span>| <span class="bool-val">true</span>);
<a href=#658 id=658 data-nosnippet>658</a>        <span class="macro">assert_eq!</span>(
<a href=#659 id=659 data-nosnippet>659</a>            drain_result.err(),
<a href=#660 id=660 data-nosnippet>660</a>            <span class="prelude-val">Some</span>(CompileError::AddressTableLookupIndexOverflow)
<a href=#661 id=661 data-nosnippet>661</a>        );
<a href=#662 id=662 data-nosnippet>662</a>    }
<a href=#663 id=663 data-nosnippet>663</a>}</code></pre></div></section></main></body></html>