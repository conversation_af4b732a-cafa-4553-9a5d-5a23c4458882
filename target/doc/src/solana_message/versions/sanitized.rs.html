<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/versions/sanitized.rs`."><title>sanitized.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/versions/</div>sanitized.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>{
<a href=#2 id=2 data-nosnippet>2</a>    <span class="kw">super</span>::VersionedMessage, <span class="kw">crate</span>::compiled_instruction::CompiledInstruction,
<a href=#3 id=3 data-nosnippet>3</a>    solana_pubkey::Pubkey, solana_sanitize::SanitizeError,
<a href=#4 id=4 data-nosnippet>4</a>};
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="doccomment">/// Wraps a sanitized `VersionedMessage` to provide a safe API
<a href=#7 id=7 data-nosnippet>7</a></span><span class="attr">#[derive(Clone, Debug, PartialEq, Eq)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">pub struct </span>SanitizedVersionedMessage {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>message: VersionedMessage,
<a href=#10 id=10 data-nosnippet>10</a>}
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">impl </span>TryFrom&lt;VersionedMessage&gt; <span class="kw">for </span>SanitizedVersionedMessage {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">type </span>Error = SanitizeError;
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">fn </span>try_from(message: VersionedMessage) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, <span class="self">Self</span>::Error&gt; {
<a href=#15 id=15 data-nosnippet>15</a>        <span class="self">Self</span>::try_new(message)
<a href=#16 id=16 data-nosnippet>16</a>    }
<a href=#17 id=17 data-nosnippet>17</a>}
<a href=#18 id=18 data-nosnippet>18</a>
<a href=#19 id=19 data-nosnippet>19</a><span class="kw">impl </span>SanitizedVersionedMessage {
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub fn </span>try_new(message: VersionedMessage) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, SanitizeError&gt; {
<a href=#21 id=21 data-nosnippet>21</a>        message.sanitize()<span class="question-mark">?</span>;
<a href=#22 id=22 data-nosnippet>22</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{ message })
<a href=#23 id=23 data-nosnippet>23</a>    }
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a>    <span class="doccomment">/// Program instructions that will be executed in sequence and committed in
<a href=#26 id=26 data-nosnippet>26</a>    /// one atomic transaction if all succeed.
<a href=#27 id=27 data-nosnippet>27</a>    </span><span class="kw">pub fn </span>instructions(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[CompiledInstruction] {
<a href=#28 id=28 data-nosnippet>28</a>        <span class="self">self</span>.message.instructions()
<a href=#29 id=29 data-nosnippet>29</a>    }
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>    <span class="doccomment">/// Program instructions iterator which includes each instruction's program
<a href=#32 id=32 data-nosnippet>32</a>    /// id.
<a href=#33 id=33 data-nosnippet>33</a>    </span><span class="kw">pub fn </span>program_instructions_iter(
<a href=#34 id=34 data-nosnippet>34</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#35 id=35 data-nosnippet>35</a>    ) -&gt; <span class="kw">impl </span>Iterator&lt;Item = (<span class="kw-2">&amp;</span>Pubkey, <span class="kw-2">&amp;</span>CompiledInstruction)&gt; + Clone {
<a href=#36 id=36 data-nosnippet>36</a>        <span class="self">self</span>.message.instructions().iter().map(<span class="kw">move </span>|ix| {
<a href=#37 id=37 data-nosnippet>37</a>            (
<a href=#38 id=38 data-nosnippet>38</a>                <span class="self">self</span>.message
<a href=#39 id=39 data-nosnippet>39</a>                    .static_account_keys()
<a href=#40 id=40 data-nosnippet>40</a>                    .get(usize::from(ix.program_id_index))
<a href=#41 id=41 data-nosnippet>41</a>                    .expect(<span class="string">"program id index is sanitized"</span>),
<a href=#42 id=42 data-nosnippet>42</a>                ix,
<a href=#43 id=43 data-nosnippet>43</a>            )
<a href=#44 id=44 data-nosnippet>44</a>        })
<a href=#45 id=45 data-nosnippet>45</a>    }
<a href=#46 id=46 data-nosnippet>46</a>}</code></pre></div></section></main></body></html>