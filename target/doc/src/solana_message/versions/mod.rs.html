<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/versions/mod.rs`."><title>mod.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/versions/</div>mod.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#[cfg(feature = <span class="string">"frozen-abi"</span>)]
<a href=#2 id=2 data-nosnippet>2</a></span><span class="kw">use </span>solana_frozen_abi_macro::{frozen_abi, AbiEnumVisitor, AbiExample};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>{
<a href=#4 id=4 data-nosnippet>4</a>    <span class="kw">crate</span>::{
<a href=#5 id=5 data-nosnippet>5</a>        compiled_instruction::CompiledInstruction, legacy::Message <span class="kw">as </span>LegacyMessage,
<a href=#6 id=6 data-nosnippet>6</a>        v0::MessageAddressTableLookup, MessageHeader,
<a href=#7 id=7 data-nosnippet>7</a>    },
<a href=#8 id=8 data-nosnippet>8</a>    solana_hash::Hash,
<a href=#9 id=9 data-nosnippet>9</a>    solana_pubkey::Pubkey,
<a href=#10 id=10 data-nosnippet>10</a>    solana_sanitize::{Sanitize, SanitizeError},
<a href=#11 id=11 data-nosnippet>11</a>    std::collections::HashSet,
<a href=#12 id=12 data-nosnippet>12</a>};
<a href=#13 id=13 data-nosnippet>13</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#14 id=14 data-nosnippet>14</a></span><span class="kw">use </span>{
<a href=#15 id=15 data-nosnippet>15</a>    serde::{
<a href=#16 id=16 data-nosnippet>16</a>        de::{<span class="self">self</span>, Deserializer, SeqAccess, Unexpected, Visitor},
<a href=#17 id=17 data-nosnippet>17</a>        ser::{SerializeTuple, Serializer},
<a href=#18 id=18 data-nosnippet>18</a>    },
<a href=#19 id=19 data-nosnippet>19</a>    serde_derive::{Deserialize, Serialize},
<a href=#20 id=20 data-nosnippet>20</a>    std::fmt,
<a href=#21 id=21 data-nosnippet>21</a>};
<a href=#22 id=22 data-nosnippet>22</a>
<a href=#23 id=23 data-nosnippet>23</a><span class="kw">mod </span>sanitized;
<a href=#24 id=24 data-nosnippet>24</a><span class="kw">pub mod </span>v0;
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a><span class="kw">pub use </span>sanitized::<span class="kw-2">*</span>;
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a><span class="doccomment">/// Bit mask that indicates whether a serialized message is versioned.
<a href=#29 id=29 data-nosnippet>29</a></span><span class="kw">pub const </span>MESSAGE_VERSION_PREFIX: u8 = <span class="number">0x80</span>;
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a><span class="doccomment">/// Either a legacy message or a v0 message.
<a href=#32 id=32 data-nosnippet>32</a>///
<a href=#33 id=33 data-nosnippet>33</a>/// # Serialization
<a href=#34 id=34 data-nosnippet>34</a>///
<a href=#35 id=35 data-nosnippet>35</a>/// If the first bit is set, the remaining 7 bits will be used to determine
<a href=#36 id=36 data-nosnippet>36</a>/// which message version is serialized starting from version `0`. If the first
<a href=#37 id=37 data-nosnippet>37</a>/// is bit is not set, all bytes are used to encode the legacy `Message`
<a href=#38 id=38 data-nosnippet>38</a>/// format.
<a href=#39 id=39 data-nosnippet>39</a></span><span class="attr">#[cfg_attr(
<a href=#40 id=40 data-nosnippet>40</a>    feature = <span class="string">"frozen-abi"</span>,
<a href=#41 id=41 data-nosnippet>41</a>    frozen_abi(digest = <span class="string">"2RTtea34NPrb8p9mWHCWjFh76cwP3MbjSmeoj5CXEBwN"</span>),
<a href=#42 id=42 data-nosnippet>42</a>    derive(AbiEnumVisitor, AbiExample)
<a href=#43 id=43 data-nosnippet>43</a>)]
<a href=#44 id=44 data-nosnippet>44</a>#[derive(Debug, PartialEq, Eq, Clone)]
<a href=#45 id=45 data-nosnippet>45</a></span><span class="kw">pub enum </span>VersionedMessage {
<a href=#46 id=46 data-nosnippet>46</a>    Legacy(LegacyMessage),
<a href=#47 id=47 data-nosnippet>47</a>    V0(v0::Message),
<a href=#48 id=48 data-nosnippet>48</a>}
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a><span class="kw">impl </span>VersionedMessage {
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub fn </span>sanitize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;(), SanitizeError&gt; {
<a href=#52 id=52 data-nosnippet>52</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#53 id=53 data-nosnippet>53</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.sanitize(),
<a href=#54 id=54 data-nosnippet>54</a>            <span class="self">Self</span>::V0(message) =&gt; message.sanitize(),
<a href=#55 id=55 data-nosnippet>55</a>        }
<a href=#56 id=56 data-nosnippet>56</a>    }
<a href=#57 id=57 data-nosnippet>57</a>
<a href=#58 id=58 data-nosnippet>58</a>    <span class="kw">pub fn </span>header(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>MessageHeader {
<a href=#59 id=59 data-nosnippet>59</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#60 id=60 data-nosnippet>60</a>            <span class="self">Self</span>::Legacy(message) =&gt; <span class="kw-2">&amp;</span>message.header,
<a href=#61 id=61 data-nosnippet>61</a>            <span class="self">Self</span>::V0(message) =&gt; <span class="kw-2">&amp;</span>message.header,
<a href=#62 id=62 data-nosnippet>62</a>        }
<a href=#63 id=63 data-nosnippet>63</a>    }
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a>    <span class="kw">pub fn </span>static_account_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[Pubkey] {
<a href=#66 id=66 data-nosnippet>66</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#67 id=67 data-nosnippet>67</a>            <span class="self">Self</span>::Legacy(message) =&gt; <span class="kw-2">&amp;</span>message.account_keys,
<a href=#68 id=68 data-nosnippet>68</a>            <span class="self">Self</span>::V0(message) =&gt; <span class="kw-2">&amp;</span>message.account_keys,
<a href=#69 id=69 data-nosnippet>69</a>        }
<a href=#70 id=70 data-nosnippet>70</a>    }
<a href=#71 id=71 data-nosnippet>71</a>
<a href=#72 id=72 data-nosnippet>72</a>    <span class="kw">pub fn </span>address_table_lookups(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>[MessageAddressTableLookup]&gt; {
<a href=#73 id=73 data-nosnippet>73</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#74 id=74 data-nosnippet>74</a>            <span class="self">Self</span>::Legacy(<span class="kw">_</span>) =&gt; <span class="prelude-val">None</span>,
<a href=#75 id=75 data-nosnippet>75</a>            <span class="self">Self</span>::V0(message) =&gt; <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>message.address_table_lookups),
<a href=#76 id=76 data-nosnippet>76</a>        }
<a href=#77 id=77 data-nosnippet>77</a>    }
<a href=#78 id=78 data-nosnippet>78</a>
<a href=#79 id=79 data-nosnippet>79</a>    <span class="doccomment">/// Returns true if the account at the specified index signed this
<a href=#80 id=80 data-nosnippet>80</a>    /// message.
<a href=#81 id=81 data-nosnippet>81</a>    </span><span class="kw">pub fn </span>is_signer(<span class="kw-2">&amp;</span><span class="self">self</span>, index: usize) -&gt; bool {
<a href=#82 id=82 data-nosnippet>82</a>        index &lt; usize::from(<span class="self">self</span>.header().num_required_signatures)
<a href=#83 id=83 data-nosnippet>83</a>    }
<a href=#84 id=84 data-nosnippet>84</a>
<a href=#85 id=85 data-nosnippet>85</a>    <span class="doccomment">/// Returns true if the account at the specified index is writable by the
<a href=#86 id=86 data-nosnippet>86</a>    /// instructions in this message. Since dynamically loaded addresses can't
<a href=#87 id=87 data-nosnippet>87</a>    /// have write locks demoted without loading addresses, this shouldn't be
<a href=#88 id=88 data-nosnippet>88</a>    /// used in the runtime.
<a href=#89 id=89 data-nosnippet>89</a>    </span><span class="kw">pub fn </span>is_maybe_writable(
<a href=#90 id=90 data-nosnippet>90</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#91 id=91 data-nosnippet>91</a>        index: usize,
<a href=#92 id=92 data-nosnippet>92</a>        reserved_account_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;&gt;,
<a href=#93 id=93 data-nosnippet>93</a>    ) -&gt; bool {
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#95 id=95 data-nosnippet>95</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.is_maybe_writable(index, reserved_account_keys),
<a href=#96 id=96 data-nosnippet>96</a>            <span class="self">Self</span>::V0(message) =&gt; message.is_maybe_writable(index, reserved_account_keys),
<a href=#97 id=97 data-nosnippet>97</a>        }
<a href=#98 id=98 data-nosnippet>98</a>    }
<a href=#99 id=99 data-nosnippet>99</a>
<a href=#100 id=100 data-nosnippet>100</a>    <span class="attr">#[deprecated(since = <span class="string">"2.0.0"</span>, note = <span class="string">"Please use `is_instruction_account` instead"</span>)]
<a href=#101 id=101 data-nosnippet>101</a>    </span><span class="kw">pub fn </span>is_key_passed_to_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#102 id=102 data-nosnippet>102</a>        <span class="self">self</span>.is_instruction_account(key_index)
<a href=#103 id=103 data-nosnippet>103</a>    }
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>    <span class="doccomment">/// Returns true if the account at the specified index is an input to some
<a href=#106 id=106 data-nosnippet>106</a>    /// program instruction in this message.
<a href=#107 id=107 data-nosnippet>107</a>    </span><span class="kw">fn </span>is_instruction_account(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#108 id=108 data-nosnippet>108</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(key_index) = u8::try_from(key_index) {
<a href=#109 id=109 data-nosnippet>109</a>            <span class="self">self</span>.instructions()
<a href=#110 id=110 data-nosnippet>110</a>                .iter()
<a href=#111 id=111 data-nosnippet>111</a>                .any(|ix| ix.accounts.contains(<span class="kw-2">&amp;</span>key_index))
<a href=#112 id=112 data-nosnippet>112</a>        } <span class="kw">else </span>{
<a href=#113 id=113 data-nosnippet>113</a>            <span class="bool-val">false
<a href=#114 id=114 data-nosnippet>114</a>        </span>}
<a href=#115 id=115 data-nosnippet>115</a>    }
<a href=#116 id=116 data-nosnippet>116</a>
<a href=#117 id=117 data-nosnippet>117</a>    <span class="kw">pub fn </span>is_invoked(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#118 id=118 data-nosnippet>118</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#119 id=119 data-nosnippet>119</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.is_key_called_as_program(key_index),
<a href=#120 id=120 data-nosnippet>120</a>            <span class="self">Self</span>::V0(message) =&gt; message.is_key_called_as_program(key_index),
<a href=#121 id=121 data-nosnippet>121</a>        }
<a href=#122 id=122 data-nosnippet>122</a>    }
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>    <span class="doccomment">/// Returns true if the account at the specified index is not invoked as a
<a href=#125 id=125 data-nosnippet>125</a>    /// program or, if invoked, is passed to a program.
<a href=#126 id=126 data-nosnippet>126</a>    </span><span class="kw">pub fn </span>is_non_loader_key(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#127 id=127 data-nosnippet>127</a>        !<span class="self">self</span>.is_invoked(key_index) || <span class="self">self</span>.is_instruction_account(key_index)
<a href=#128 id=128 data-nosnippet>128</a>    }
<a href=#129 id=129 data-nosnippet>129</a>
<a href=#130 id=130 data-nosnippet>130</a>    <span class="kw">pub fn </span>recent_blockhash(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Hash {
<a href=#131 id=131 data-nosnippet>131</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#132 id=132 data-nosnippet>132</a>            <span class="self">Self</span>::Legacy(message) =&gt; <span class="kw-2">&amp;</span>message.recent_blockhash,
<a href=#133 id=133 data-nosnippet>133</a>            <span class="self">Self</span>::V0(message) =&gt; <span class="kw-2">&amp;</span>message.recent_blockhash,
<a href=#134 id=134 data-nosnippet>134</a>        }
<a href=#135 id=135 data-nosnippet>135</a>    }
<a href=#136 id=136 data-nosnippet>136</a>
<a href=#137 id=137 data-nosnippet>137</a>    <span class="kw">pub fn </span>set_recent_blockhash(<span class="kw-2">&amp;mut </span><span class="self">self</span>, recent_blockhash: Hash) {
<a href=#138 id=138 data-nosnippet>138</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#139 id=139 data-nosnippet>139</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.recent_blockhash = recent_blockhash,
<a href=#140 id=140 data-nosnippet>140</a>            <span class="self">Self</span>::V0(message) =&gt; message.recent_blockhash = recent_blockhash,
<a href=#141 id=141 data-nosnippet>141</a>        }
<a href=#142 id=142 data-nosnippet>142</a>    }
<a href=#143 id=143 data-nosnippet>143</a>
<a href=#144 id=144 data-nosnippet>144</a>    <span class="doccomment">/// Program instructions that will be executed in sequence and committed in
<a href=#145 id=145 data-nosnippet>145</a>    /// one atomic transaction if all succeed.
<a href=#146 id=146 data-nosnippet>146</a>    </span><span class="kw">pub fn </span>instructions(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[CompiledInstruction] {
<a href=#147 id=147 data-nosnippet>147</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#148 id=148 data-nosnippet>148</a>            <span class="self">Self</span>::Legacy(message) =&gt; <span class="kw-2">&amp;</span>message.instructions,
<a href=#149 id=149 data-nosnippet>149</a>            <span class="self">Self</span>::V0(message) =&gt; <span class="kw-2">&amp;</span>message.instructions,
<a href=#150 id=150 data-nosnippet>150</a>        }
<a href=#151 id=151 data-nosnippet>151</a>    }
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#154 id=154 data-nosnippet>154</a>    </span><span class="kw">pub fn </span>serialize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;u8&gt; {
<a href=#155 id=155 data-nosnippet>155</a>        bincode::serialize(<span class="self">self</span>).unwrap()
<a href=#156 id=156 data-nosnippet>156</a>    }
<a href=#157 id=157 data-nosnippet>157</a>
<a href=#158 id=158 data-nosnippet>158</a>    <span class="attr">#[cfg(all(feature = <span class="string">"bincode"</span>, feature = <span class="string">"blake3"</span>))]
<a href=#159 id=159 data-nosnippet>159</a>    </span><span class="doccomment">/// Compute the blake3 hash of this transaction's message
<a href=#160 id=160 data-nosnippet>160</a>    </span><span class="kw">pub fn </span>hash(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Hash {
<a href=#161 id=161 data-nosnippet>161</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.serialize();
<a href=#162 id=162 data-nosnippet>162</a>        <span class="self">Self</span>::hash_raw_message(<span class="kw-2">&amp;</span>message_bytes)
<a href=#163 id=163 data-nosnippet>163</a>    }
<a href=#164 id=164 data-nosnippet>164</a>
<a href=#165 id=165 data-nosnippet>165</a>    <span class="attr">#[cfg(feature = <span class="string">"blake3"</span>)]
<a href=#166 id=166 data-nosnippet>166</a>    </span><span class="doccomment">/// Compute the blake3 hash of a raw transaction message
<a href=#167 id=167 data-nosnippet>167</a>    </span><span class="kw">pub fn </span>hash_raw_message(message_bytes: <span class="kw-2">&amp;</span>[u8]) -&gt; Hash {
<a href=#168 id=168 data-nosnippet>168</a>        <span class="kw">use </span>blake3::traits::digest::Digest;
<a href=#169 id=169 data-nosnippet>169</a>        <span class="kw">let </span><span class="kw-2">mut </span>hasher = blake3::Hasher::new();
<a href=#170 id=170 data-nosnippet>170</a>        hasher.update(<span class="string">b"solana-tx-message-v1"</span>);
<a href=#171 id=171 data-nosnippet>171</a>        hasher.update(message_bytes);
<a href=#172 id=172 data-nosnippet>172</a>        <span class="kw">let </span>hash_bytes: [u8; solana_hash::HASH_BYTES] = hasher.finalize().into();
<a href=#173 id=173 data-nosnippet>173</a>        hash_bytes.into()
<a href=#174 id=174 data-nosnippet>174</a>    }
<a href=#175 id=175 data-nosnippet>175</a>}
<a href=#176 id=176 data-nosnippet>176</a>
<a href=#177 id=177 data-nosnippet>177</a><span class="kw">impl </span>Default <span class="kw">for </span>VersionedMessage {
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#179 id=179 data-nosnippet>179</a>        <span class="self">Self</span>::Legacy(LegacyMessage::default())
<a href=#180 id=180 data-nosnippet>180</a>    }
<a href=#181 id=181 data-nosnippet>181</a>}
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#184 id=184 data-nosnippet>184</a></span><span class="kw">impl </span>serde::Serialize <span class="kw">for </span>VersionedMessage {
<a href=#185 id=185 data-nosnippet>185</a>    <span class="kw">fn </span>serialize&lt;S&gt;(<span class="kw-2">&amp;</span><span class="self">self</span>, serializer: S) -&gt; <span class="prelude-ty">Result</span>&lt;S::Ok, S::Error&gt;
<a href=#186 id=186 data-nosnippet>186</a>    <span class="kw">where
<a href=#187 id=187 data-nosnippet>187</a>        </span>S: Serializer,
<a href=#188 id=188 data-nosnippet>188</a>    {
<a href=#189 id=189 data-nosnippet>189</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#190 id=190 data-nosnippet>190</a>            <span class="self">Self</span>::Legacy(message) =&gt; {
<a href=#191 id=191 data-nosnippet>191</a>                <span class="kw">let </span><span class="kw-2">mut </span>seq = serializer.serialize_tuple(<span class="number">1</span>)<span class="question-mark">?</span>;
<a href=#192 id=192 data-nosnippet>192</a>                seq.serialize_element(message)<span class="question-mark">?</span>;
<a href=#193 id=193 data-nosnippet>193</a>                seq.end()
<a href=#194 id=194 data-nosnippet>194</a>            }
<a href=#195 id=195 data-nosnippet>195</a>            <span class="self">Self</span>::V0(message) =&gt; {
<a href=#196 id=196 data-nosnippet>196</a>                <span class="kw">let </span><span class="kw-2">mut </span>seq = serializer.serialize_tuple(<span class="number">2</span>)<span class="question-mark">?</span>;
<a href=#197 id=197 data-nosnippet>197</a>                seq.serialize_element(<span class="kw-2">&amp;</span>MESSAGE_VERSION_PREFIX)<span class="question-mark">?</span>;
<a href=#198 id=198 data-nosnippet>198</a>                seq.serialize_element(message)<span class="question-mark">?</span>;
<a href=#199 id=199 data-nosnippet>199</a>                seq.end()
<a href=#200 id=200 data-nosnippet>200</a>            }
<a href=#201 id=201 data-nosnippet>201</a>        }
<a href=#202 id=202 data-nosnippet>202</a>    }
<a href=#203 id=203 data-nosnippet>203</a>}
<a href=#204 id=204 data-nosnippet>204</a>
<a href=#205 id=205 data-nosnippet>205</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#206 id=206 data-nosnippet>206</a></span><span class="kw">enum </span>MessagePrefix {
<a href=#207 id=207 data-nosnippet>207</a>    Legacy(u8),
<a href=#208 id=208 data-nosnippet>208</a>    Versioned(u8),
<a href=#209 id=209 data-nosnippet>209</a>}
<a href=#210 id=210 data-nosnippet>210</a>
<a href=#211 id=211 data-nosnippet>211</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#212 id=212 data-nosnippet>212</a></span><span class="kw">impl</span>&lt;<span class="lifetime">'de</span>&gt; serde::Deserialize&lt;<span class="lifetime">'de</span>&gt; <span class="kw">for </span>MessagePrefix {
<a href=#213 id=213 data-nosnippet>213</a>    <span class="kw">fn </span>deserialize&lt;D&gt;(deserializer: D) -&gt; <span class="prelude-ty">Result</span>&lt;MessagePrefix, D::Error&gt;
<a href=#214 id=214 data-nosnippet>214</a>    <span class="kw">where
<a href=#215 id=215 data-nosnippet>215</a>        </span>D: Deserializer&lt;<span class="lifetime">'de</span>&gt;,
<a href=#216 id=216 data-nosnippet>216</a>    {
<a href=#217 id=217 data-nosnippet>217</a>        <span class="kw">struct </span>PrefixVisitor;
<a href=#218 id=218 data-nosnippet>218</a>
<a href=#219 id=219 data-nosnippet>219</a>        <span class="kw">impl </span>Visitor&lt;<span class="lifetime">'_</span>&gt; <span class="kw">for </span>PrefixVisitor {
<a href=#220 id=220 data-nosnippet>220</a>            <span class="kw">type </span>Value = MessagePrefix;
<a href=#221 id=221 data-nosnippet>221</a>
<a href=#222 id=222 data-nosnippet>222</a>            <span class="kw">fn </span>expecting(<span class="kw-2">&amp;</span><span class="self">self</span>, formatter: <span class="kw-2">&amp;mut </span>fmt::Formatter) -&gt; fmt::Result {
<a href=#223 id=223 data-nosnippet>223</a>                formatter.write_str(<span class="string">"message prefix byte"</span>)
<a href=#224 id=224 data-nosnippet>224</a>            }
<a href=#225 id=225 data-nosnippet>225</a>
<a href=#226 id=226 data-nosnippet>226</a>            <span class="comment">// Serde's integer visitors bubble up to u64 so check the prefix
<a href=#227 id=227 data-nosnippet>227</a>            // with this function instead of visit_u8. This approach is
<a href=#228 id=228 data-nosnippet>228</a>            // necessary because serde_json directly calls visit_u64 for
<a href=#229 id=229 data-nosnippet>229</a>            // unsigned integers.
<a href=#230 id=230 data-nosnippet>230</a>            </span><span class="kw">fn </span>visit_u64&lt;E: de::Error&gt;(<span class="self">self</span>, value: u64) -&gt; <span class="prelude-ty">Result</span>&lt;MessagePrefix, E&gt; {
<a href=#231 id=231 data-nosnippet>231</a>                <span class="kw">if </span>value &gt; u8::MAX <span class="kw">as </span>u64 {
<a href=#232 id=232 data-nosnippet>232</a>                    <span class="prelude-val">Err</span>(de::Error::invalid_type(Unexpected::Unsigned(value), <span class="kw-2">&amp;</span><span class="self">self</span>))<span class="question-mark">?</span>;
<a href=#233 id=233 data-nosnippet>233</a>                }
<a href=#234 id=234 data-nosnippet>234</a>
<a href=#235 id=235 data-nosnippet>235</a>                <span class="kw">let </span>byte = value <span class="kw">as </span>u8;
<a href=#236 id=236 data-nosnippet>236</a>                <span class="kw">if </span>byte &amp; MESSAGE_VERSION_PREFIX != <span class="number">0 </span>{
<a href=#237 id=237 data-nosnippet>237</a>                    <span class="prelude-val">Ok</span>(MessagePrefix::Versioned(byte &amp; !MESSAGE_VERSION_PREFIX))
<a href=#238 id=238 data-nosnippet>238</a>                } <span class="kw">else </span>{
<a href=#239 id=239 data-nosnippet>239</a>                    <span class="prelude-val">Ok</span>(MessagePrefix::Legacy(byte))
<a href=#240 id=240 data-nosnippet>240</a>                }
<a href=#241 id=241 data-nosnippet>241</a>            }
<a href=#242 id=242 data-nosnippet>242</a>        }
<a href=#243 id=243 data-nosnippet>243</a>
<a href=#244 id=244 data-nosnippet>244</a>        deserializer.deserialize_u8(PrefixVisitor)
<a href=#245 id=245 data-nosnippet>245</a>    }
<a href=#246 id=246 data-nosnippet>246</a>}
<a href=#247 id=247 data-nosnippet>247</a>
<a href=#248 id=248 data-nosnippet>248</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#249 id=249 data-nosnippet>249</a></span><span class="kw">impl</span>&lt;<span class="lifetime">'de</span>&gt; serde::Deserialize&lt;<span class="lifetime">'de</span>&gt; <span class="kw">for </span>VersionedMessage {
<a href=#250 id=250 data-nosnippet>250</a>    <span class="kw">fn </span>deserialize&lt;D&gt;(deserializer: D) -&gt; <span class="prelude-ty">Result</span>&lt;VersionedMessage, D::Error&gt;
<a href=#251 id=251 data-nosnippet>251</a>    <span class="kw">where
<a href=#252 id=252 data-nosnippet>252</a>        </span>D: Deserializer&lt;<span class="lifetime">'de</span>&gt;,
<a href=#253 id=253 data-nosnippet>253</a>    {
<a href=#254 id=254 data-nosnippet>254</a>        <span class="kw">struct </span>MessageVisitor;
<a href=#255 id=255 data-nosnippet>255</a>
<a href=#256 id=256 data-nosnippet>256</a>        <span class="kw">impl</span>&lt;<span class="lifetime">'de</span>&gt; Visitor&lt;<span class="lifetime">'de</span>&gt; <span class="kw">for </span>MessageVisitor {
<a href=#257 id=257 data-nosnippet>257</a>            <span class="kw">type </span>Value = VersionedMessage;
<a href=#258 id=258 data-nosnippet>258</a>
<a href=#259 id=259 data-nosnippet>259</a>            <span class="kw">fn </span>expecting(<span class="kw-2">&amp;</span><span class="self">self</span>, formatter: <span class="kw-2">&amp;mut </span>fmt::Formatter) -&gt; fmt::Result {
<a href=#260 id=260 data-nosnippet>260</a>                formatter.write_str(<span class="string">"message bytes"</span>)
<a href=#261 id=261 data-nosnippet>261</a>            }
<a href=#262 id=262 data-nosnippet>262</a>
<a href=#263 id=263 data-nosnippet>263</a>            <span class="kw">fn </span>visit_seq&lt;A&gt;(<span class="self">self</span>, <span class="kw-2">mut </span>seq: A) -&gt; <span class="prelude-ty">Result</span>&lt;VersionedMessage, A::Error&gt;
<a href=#264 id=264 data-nosnippet>264</a>            <span class="kw">where
<a href=#265 id=265 data-nosnippet>265</a>                </span>A: SeqAccess&lt;<span class="lifetime">'de</span>&gt;,
<a href=#266 id=266 data-nosnippet>266</a>            {
<a href=#267 id=267 data-nosnippet>267</a>                <span class="kw">let </span>prefix: MessagePrefix = seq
<a href=#268 id=268 data-nosnippet>268</a>                    .next_element()<span class="question-mark">?
<a href=#269 id=269 data-nosnippet>269</a>                    </span>.ok_or_else(|| de::Error::invalid_length(<span class="number">0</span>, <span class="kw-2">&amp;</span><span class="self">self</span>))<span class="question-mark">?</span>;
<a href=#270 id=270 data-nosnippet>270</a>
<a href=#271 id=271 data-nosnippet>271</a>                <span class="kw">match </span>prefix {
<a href=#272 id=272 data-nosnippet>272</a>                    MessagePrefix::Legacy(num_required_signatures) =&gt; {
<a href=#273 id=273 data-nosnippet>273</a>                        <span class="comment">// The remaining fields of the legacy Message struct after the first byte.
<a href=#274 id=274 data-nosnippet>274</a>                        </span><span class="attr">#[derive(Serialize, Deserialize)]
<a href=#275 id=275 data-nosnippet>275</a>                        </span><span class="kw">struct </span>RemainingLegacyMessage {
<a href=#276 id=276 data-nosnippet>276</a>                            <span class="kw">pub </span>num_readonly_signed_accounts: u8,
<a href=#277 id=277 data-nosnippet>277</a>                            <span class="kw">pub </span>num_readonly_unsigned_accounts: u8,
<a href=#278 id=278 data-nosnippet>278</a>                            <span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#279 id=279 data-nosnippet>279</a>                            </span><span class="kw">pub </span>account_keys: Vec&lt;Pubkey&gt;,
<a href=#280 id=280 data-nosnippet>280</a>                            <span class="kw">pub </span>recent_blockhash: Hash,
<a href=#281 id=281 data-nosnippet>281</a>                            <span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#282 id=282 data-nosnippet>282</a>                            </span><span class="kw">pub </span>instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#283 id=283 data-nosnippet>283</a>                        }
<a href=#284 id=284 data-nosnippet>284</a>
<a href=#285 id=285 data-nosnippet>285</a>                        <span class="kw">let </span>message: RemainingLegacyMessage =
<a href=#286 id=286 data-nosnippet>286</a>                            seq.next_element()<span class="question-mark">?</span>.ok_or_else(|| {
<a href=#287 id=287 data-nosnippet>287</a>                                <span class="comment">// will never happen since tuple length is always 2
<a href=#288 id=288 data-nosnippet>288</a>                                </span>de::Error::invalid_length(<span class="number">1</span>, <span class="kw-2">&amp;</span><span class="self">self</span>)
<a href=#289 id=289 data-nosnippet>289</a>                            })<span class="question-mark">?</span>;
<a href=#290 id=290 data-nosnippet>290</a>
<a href=#291 id=291 data-nosnippet>291</a>                        <span class="prelude-val">Ok</span>(VersionedMessage::Legacy(LegacyMessage {
<a href=#292 id=292 data-nosnippet>292</a>                            header: MessageHeader {
<a href=#293 id=293 data-nosnippet>293</a>                                num_required_signatures,
<a href=#294 id=294 data-nosnippet>294</a>                                num_readonly_signed_accounts: message.num_readonly_signed_accounts,
<a href=#295 id=295 data-nosnippet>295</a>                                num_readonly_unsigned_accounts: message
<a href=#296 id=296 data-nosnippet>296</a>                                    .num_readonly_unsigned_accounts,
<a href=#297 id=297 data-nosnippet>297</a>                            },
<a href=#298 id=298 data-nosnippet>298</a>                            account_keys: message.account_keys,
<a href=#299 id=299 data-nosnippet>299</a>                            recent_blockhash: message.recent_blockhash,
<a href=#300 id=300 data-nosnippet>300</a>                            instructions: message.instructions,
<a href=#301 id=301 data-nosnippet>301</a>                        }))
<a href=#302 id=302 data-nosnippet>302</a>                    }
<a href=#303 id=303 data-nosnippet>303</a>                    MessagePrefix::Versioned(version) =&gt; {
<a href=#304 id=304 data-nosnippet>304</a>                        <span class="kw">match </span>version {
<a href=#305 id=305 data-nosnippet>305</a>                            <span class="number">0 </span>=&gt; {
<a href=#306 id=306 data-nosnippet>306</a>                                <span class="prelude-val">Ok</span>(VersionedMessage::V0(seq.next_element()<span class="question-mark">?</span>.ok_or_else(
<a href=#307 id=307 data-nosnippet>307</a>                                    || {
<a href=#308 id=308 data-nosnippet>308</a>                                        <span class="comment">// will never happen since tuple length is always 2
<a href=#309 id=309 data-nosnippet>309</a>                                        </span>de::Error::invalid_length(<span class="number">1</span>, <span class="kw-2">&amp;</span><span class="self">self</span>)
<a href=#310 id=310 data-nosnippet>310</a>                                    },
<a href=#311 id=311 data-nosnippet>311</a>                                )<span class="question-mark">?</span>))
<a href=#312 id=312 data-nosnippet>312</a>                            }
<a href=#313 id=313 data-nosnippet>313</a>                            <span class="number">127 </span>=&gt; {
<a href=#314 id=314 data-nosnippet>314</a>                                <span class="comment">// 0xff is used as the first byte of the off-chain messages
<a href=#315 id=315 data-nosnippet>315</a>                                // which corresponds to version 127 of the versioned messages.
<a href=#316 id=316 data-nosnippet>316</a>                                // This explicit check is added to prevent the usage of version 127
<a href=#317 id=317 data-nosnippet>317</a>                                // in the runtime as a valid transaction.
<a href=#318 id=318 data-nosnippet>318</a>                                </span><span class="prelude-val">Err</span>(de::Error::custom(<span class="string">"off-chain messages are not accepted"</span>))
<a href=#319 id=319 data-nosnippet>319</a>                            }
<a href=#320 id=320 data-nosnippet>320</a>                            <span class="kw">_ </span>=&gt; <span class="prelude-val">Err</span>(de::Error::invalid_value(
<a href=#321 id=321 data-nosnippet>321</a>                                de::Unexpected::Unsigned(version <span class="kw">as </span>u64),
<a href=#322 id=322 data-nosnippet>322</a>                                <span class="kw-2">&amp;</span><span class="string">"a valid transaction message version"</span>,
<a href=#323 id=323 data-nosnippet>323</a>                            )),
<a href=#324 id=324 data-nosnippet>324</a>                        }
<a href=#325 id=325 data-nosnippet>325</a>                    }
<a href=#326 id=326 data-nosnippet>326</a>                }
<a href=#327 id=327 data-nosnippet>327</a>            }
<a href=#328 id=328 data-nosnippet>328</a>        }
<a href=#329 id=329 data-nosnippet>329</a>
<a href=#330 id=330 data-nosnippet>330</a>        deserializer.deserialize_tuple(<span class="number">2</span>, MessageVisitor)
<a href=#331 id=331 data-nosnippet>331</a>    }
<a href=#332 id=332 data-nosnippet>332</a>}
<a href=#333 id=333 data-nosnippet>333</a>
<a href=#334 id=334 data-nosnippet>334</a><span class="attr">#[cfg(test)]
<a href=#335 id=335 data-nosnippet>335</a></span><span class="kw">mod </span>tests {
<a href=#336 id=336 data-nosnippet>336</a>    <span class="kw">use </span>{
<a href=#337 id=337 data-nosnippet>337</a>        <span class="kw">super</span>::<span class="kw-2">*</span>,
<a href=#338 id=338 data-nosnippet>338</a>        <span class="kw">crate</span>::v0::MessageAddressTableLookup,
<a href=#339 id=339 data-nosnippet>339</a>        solana_instruction::{AccountMeta, Instruction},
<a href=#340 id=340 data-nosnippet>340</a>    };
<a href=#341 id=341 data-nosnippet>341</a>
<a href=#342 id=342 data-nosnippet>342</a>    <span class="attr">#[test]
<a href=#343 id=343 data-nosnippet>343</a>    </span><span class="kw">fn </span>test_legacy_message_serialization() {
<a href=#344 id=344 data-nosnippet>344</a>        <span class="kw">let </span>program_id0 = Pubkey::new_unique();
<a href=#345 id=345 data-nosnippet>345</a>        <span class="kw">let </span>program_id1 = Pubkey::new_unique();
<a href=#346 id=346 data-nosnippet>346</a>        <span class="kw">let </span>id0 = Pubkey::new_unique();
<a href=#347 id=347 data-nosnippet>347</a>        <span class="kw">let </span>id1 = Pubkey::new_unique();
<a href=#348 id=348 data-nosnippet>348</a>        <span class="kw">let </span>id2 = Pubkey::new_unique();
<a href=#349 id=349 data-nosnippet>349</a>        <span class="kw">let </span>id3 = Pubkey::new_unique();
<a href=#350 id=350 data-nosnippet>350</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[
<a href=#351 id=351 data-nosnippet>351</a>            Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>)]),
<a href=#352 id=352 data-nosnippet>352</a>            Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id1, <span class="bool-val">true</span>)]),
<a href=#353 id=353 data-nosnippet>353</a>            Instruction::new_with_bincode(
<a href=#354 id=354 data-nosnippet>354</a>                program_id1,
<a href=#355 id=355 data-nosnippet>355</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#356 id=356 data-nosnippet>356</a>                <span class="macro">vec!</span>[AccountMeta::new_readonly(id2, <span class="bool-val">false</span>)],
<a href=#357 id=357 data-nosnippet>357</a>            ),
<a href=#358 id=358 data-nosnippet>358</a>            Instruction::new_with_bincode(
<a href=#359 id=359 data-nosnippet>359</a>                program_id1,
<a href=#360 id=360 data-nosnippet>360</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#361 id=361 data-nosnippet>361</a>                <span class="macro">vec!</span>[AccountMeta::new_readonly(id3, <span class="bool-val">true</span>)],
<a href=#362 id=362 data-nosnippet>362</a>            ),
<a href=#363 id=363 data-nosnippet>363</a>        ];
<a href=#364 id=364 data-nosnippet>364</a>
<a href=#365 id=365 data-nosnippet>365</a>        <span class="kw">let </span><span class="kw-2">mut </span>message = LegacyMessage::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id1));
<a href=#366 id=366 data-nosnippet>366</a>        message.recent_blockhash = Hash::new_unique();
<a href=#367 id=367 data-nosnippet>367</a>        <span class="kw">let </span>wrapped_message = VersionedMessage::Legacy(message.clone());
<a href=#368 id=368 data-nosnippet>368</a>
<a href=#369 id=369 data-nosnippet>369</a>        <span class="comment">// bincode
<a href=#370 id=370 data-nosnippet>370</a>        </span>{
<a href=#371 id=371 data-nosnippet>371</a>            <span class="kw">let </span>bytes = bincode::serialize(<span class="kw-2">&amp;</span>message).unwrap();
<a href=#372 id=372 data-nosnippet>372</a>            <span class="macro">assert_eq!</span>(bytes, bincode::serialize(<span class="kw-2">&amp;</span>wrapped_message).unwrap());
<a href=#373 id=373 data-nosnippet>373</a>
<a href=#374 id=374 data-nosnippet>374</a>            <span class="kw">let </span>message_from_bytes: LegacyMessage = bincode::deserialize(<span class="kw-2">&amp;</span>bytes).unwrap();
<a href=#375 id=375 data-nosnippet>375</a>            <span class="kw">let </span>wrapped_message_from_bytes: VersionedMessage =
<a href=#376 id=376 data-nosnippet>376</a>                bincode::deserialize(<span class="kw-2">&amp;</span>bytes).unwrap();
<a href=#377 id=377 data-nosnippet>377</a>
<a href=#378 id=378 data-nosnippet>378</a>            <span class="macro">assert_eq!</span>(message, message_from_bytes);
<a href=#379 id=379 data-nosnippet>379</a>            <span class="macro">assert_eq!</span>(wrapped_message, wrapped_message_from_bytes);
<a href=#380 id=380 data-nosnippet>380</a>        }
<a href=#381 id=381 data-nosnippet>381</a>
<a href=#382 id=382 data-nosnippet>382</a>        <span class="comment">// serde_json
<a href=#383 id=383 data-nosnippet>383</a>        </span>{
<a href=#384 id=384 data-nosnippet>384</a>            <span class="kw">let </span>string = serde_json::to_string(<span class="kw-2">&amp;</span>message).unwrap();
<a href=#385 id=385 data-nosnippet>385</a>            <span class="kw">let </span>message_from_string: LegacyMessage = serde_json::from_str(<span class="kw-2">&amp;</span>string).unwrap();
<a href=#386 id=386 data-nosnippet>386</a>            <span class="macro">assert_eq!</span>(message, message_from_string);
<a href=#387 id=387 data-nosnippet>387</a>        }
<a href=#388 id=388 data-nosnippet>388</a>    }
<a href=#389 id=389 data-nosnippet>389</a>
<a href=#390 id=390 data-nosnippet>390</a>    <span class="attr">#[test]
<a href=#391 id=391 data-nosnippet>391</a>    </span><span class="kw">fn </span>test_versioned_message_serialization() {
<a href=#392 id=392 data-nosnippet>392</a>        <span class="kw">let </span>message = VersionedMessage::V0(v0::Message {
<a href=#393 id=393 data-nosnippet>393</a>            header: MessageHeader {
<a href=#394 id=394 data-nosnippet>394</a>                num_required_signatures: <span class="number">1</span>,
<a href=#395 id=395 data-nosnippet>395</a>                num_readonly_signed_accounts: <span class="number">0</span>,
<a href=#396 id=396 data-nosnippet>396</a>                num_readonly_unsigned_accounts: <span class="number">0</span>,
<a href=#397 id=397 data-nosnippet>397</a>            },
<a href=#398 id=398 data-nosnippet>398</a>            recent_blockhash: Hash::new_unique(),
<a href=#399 id=399 data-nosnippet>399</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#400 id=400 data-nosnippet>400</a>            address_table_lookups: <span class="macro">vec!</span>[
<a href=#401 id=401 data-nosnippet>401</a>                MessageAddressTableLookup {
<a href=#402 id=402 data-nosnippet>402</a>                    account_key: Pubkey::new_unique(),
<a href=#403 id=403 data-nosnippet>403</a>                    writable_indexes: <span class="macro">vec!</span>[<span class="number">1</span>],
<a href=#404 id=404 data-nosnippet>404</a>                    readonly_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#405 id=405 data-nosnippet>405</a>                },
<a href=#406 id=406 data-nosnippet>406</a>                MessageAddressTableLookup {
<a href=#407 id=407 data-nosnippet>407</a>                    account_key: Pubkey::new_unique(),
<a href=#408 id=408 data-nosnippet>408</a>                    writable_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#409 id=409 data-nosnippet>409</a>                    readonly_indexes: <span class="macro">vec!</span>[<span class="number">1</span>],
<a href=#410 id=410 data-nosnippet>410</a>                },
<a href=#411 id=411 data-nosnippet>411</a>            ],
<a href=#412 id=412 data-nosnippet>412</a>            instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#413 id=413 data-nosnippet>413</a>                program_id_index: <span class="number">1</span>,
<a href=#414 id=414 data-nosnippet>414</a>                accounts: <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>],
<a href=#415 id=415 data-nosnippet>415</a>                data: <span class="macro">vec!</span>[],
<a href=#416 id=416 data-nosnippet>416</a>            }],
<a href=#417 id=417 data-nosnippet>417</a>        });
<a href=#418 id=418 data-nosnippet>418</a>
<a href=#419 id=419 data-nosnippet>419</a>        <span class="kw">let </span>bytes = bincode::serialize(<span class="kw-2">&amp;</span>message).unwrap();
<a href=#420 id=420 data-nosnippet>420</a>        <span class="kw">let </span>message_from_bytes: VersionedMessage = bincode::deserialize(<span class="kw-2">&amp;</span>bytes).unwrap();
<a href=#421 id=421 data-nosnippet>421</a>        <span class="macro">assert_eq!</span>(message, message_from_bytes);
<a href=#422 id=422 data-nosnippet>422</a>
<a href=#423 id=423 data-nosnippet>423</a>        <span class="kw">let </span>string = serde_json::to_string(<span class="kw-2">&amp;</span>message).unwrap();
<a href=#424 id=424 data-nosnippet>424</a>        <span class="kw">let </span>message_from_string: VersionedMessage = serde_json::from_str(<span class="kw-2">&amp;</span>string).unwrap();
<a href=#425 id=425 data-nosnippet>425</a>        <span class="macro">assert_eq!</span>(message, message_from_string);
<a href=#426 id=426 data-nosnippet>426</a>    }
<a href=#427 id=427 data-nosnippet>427</a>}</code></pre></div></section></main></body></html>