<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/versions/v0/mod.rs`."><title>mod.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../../../" data-static-root-path="../../../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../../static.files/storage-4e99c027.js"></script><script defer src="../../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../../src-files.js"></script><script defer src="../../../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/versions/v0/</div>mod.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="doccomment">//! A future Solana message format.
<a href=#2 id=2 data-nosnippet>2</a>//!
<a href=#3 id=3 data-nosnippet>3</a>//! This crate defines two versions of `Message` in their own modules:
<a href=#4 id=4 data-nosnippet>4</a>//! [`legacy`] and [`v0`]. `legacy` is the current version as of Solana 1.10.0.
<a href=#5 id=5 data-nosnippet>5</a>//! `v0` is a [future message format] that encodes more account keys into a
<a href=#6 id=6 data-nosnippet>6</a>//! transaction than the legacy format.
<a href=#7 id=7 data-nosnippet>7</a>//!
<a href=#8 id=8 data-nosnippet>8</a>//! [`legacy`]: crate::legacy
<a href=#9 id=9 data-nosnippet>9</a>//! [`v0`]: crate::v0
<a href=#10 id=10 data-nosnippet>10</a>//! [future message format]: https://docs.solanalabs.com/proposals/versioned-transactions
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub use </span>loaded::<span class="kw-2">*</span>;
<a href=#13 id=13 data-nosnippet>13</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#14 id=14 data-nosnippet>14</a></span><span class="kw">use </span>serde_derive::{Deserialize, Serialize};
<a href=#15 id=15 data-nosnippet>15</a><span class="attr">#[cfg(feature = <span class="string">"frozen-abi"</span>)]
<a href=#16 id=16 data-nosnippet>16</a></span><span class="kw">use </span>solana_frozen_abi_macro::AbiExample;
<a href=#17 id=17 data-nosnippet>17</a><span class="kw">use </span>{
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">crate</span>::{
<a href=#19 id=19 data-nosnippet>19</a>        compiled_instruction::CompiledInstruction,
<a href=#20 id=20 data-nosnippet>20</a>        compiled_keys::{CompileError, CompiledKeys},
<a href=#21 id=21 data-nosnippet>21</a>        AccountKeys, AddressLookupTableAccount, MessageHeader,
<a href=#22 id=22 data-nosnippet>22</a>    },
<a href=#23 id=23 data-nosnippet>23</a>    solana_hash::Hash,
<a href=#24 id=24 data-nosnippet>24</a>    solana_instruction::Instruction,
<a href=#25 id=25 data-nosnippet>25</a>    solana_pubkey::Pubkey,
<a href=#26 id=26 data-nosnippet>26</a>    solana_sanitize::SanitizeError,
<a href=#27 id=27 data-nosnippet>27</a>    solana_sdk_ids::bpf_loader_upgradeable,
<a href=#28 id=28 data-nosnippet>28</a>    std::collections::HashSet,
<a href=#29 id=29 data-nosnippet>29</a>};
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a><span class="kw">mod </span>loaded;
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a><span class="doccomment">/// Address table lookups describe an on-chain address lookup table to use
<a href=#34 id=34 data-nosnippet>34</a>/// for loading more readonly and writable accounts in a single tx.
<a href=#35 id=35 data-nosnippet>35</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"frozen-abi"</span>, derive(AbiExample))]
<a href=#36 id=36 data-nosnippet>36</a>#[cfg_attr(
<a href=#37 id=37 data-nosnippet>37</a>    feature = <span class="string">"serde"</span>,
<a href=#38 id=38 data-nosnippet>38</a>    derive(Deserialize, Serialize),
<a href=#39 id=39 data-nosnippet>39</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#40 id=40 data-nosnippet>40</a>)]
<a href=#41 id=41 data-nosnippet>41</a>#[derive(Default, Debug, PartialEq, Eq, Clone)]
<a href=#42 id=42 data-nosnippet>42</a></span><span class="kw">pub struct </span>MessageAddressTableLookup {
<a href=#43 id=43 data-nosnippet>43</a>    <span class="doccomment">/// Address lookup table account key
<a href=#44 id=44 data-nosnippet>44</a>    </span><span class="kw">pub </span>account_key: Pubkey,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="doccomment">/// List of indexes used to load writable account addresses
<a href=#46 id=46 data-nosnippet>46</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#47 id=47 data-nosnippet>47</a>    </span><span class="kw">pub </span>writable_indexes: Vec&lt;u8&gt;,
<a href=#48 id=48 data-nosnippet>48</a>    <span class="doccomment">/// List of indexes used to load readonly account addresses
<a href=#49 id=49 data-nosnippet>49</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#50 id=50 data-nosnippet>50</a>    </span><span class="kw">pub </span>readonly_indexes: Vec&lt;u8&gt;,
<a href=#51 id=51 data-nosnippet>51</a>}
<a href=#52 id=52 data-nosnippet>52</a>
<a href=#53 id=53 data-nosnippet>53</a><span class="doccomment">/// A Solana transaction message (v0).
<a href=#54 id=54 data-nosnippet>54</a>///
<a href=#55 id=55 data-nosnippet>55</a>/// This message format supports succinct account loading with
<a href=#56 id=56 data-nosnippet>56</a>/// on-chain address lookup tables.
<a href=#57 id=57 data-nosnippet>57</a>///
<a href=#58 id=58 data-nosnippet>58</a>/// See the crate documentation for further description.
<a href=#59 id=59 data-nosnippet>59</a>///
<a href=#60 id=60 data-nosnippet>60</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"frozen-abi"</span>, derive(AbiExample))]
<a href=#61 id=61 data-nosnippet>61</a>#[cfg_attr(
<a href=#62 id=62 data-nosnippet>62</a>    feature = <span class="string">"serde"</span>,
<a href=#63 id=63 data-nosnippet>63</a>    derive(Deserialize, Serialize),
<a href=#64 id=64 data-nosnippet>64</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#65 id=65 data-nosnippet>65</a>)]
<a href=#66 id=66 data-nosnippet>66</a>#[derive(Default, Debug, PartialEq, Eq, Clone)]
<a href=#67 id=67 data-nosnippet>67</a></span><span class="kw">pub struct </span>Message {
<a href=#68 id=68 data-nosnippet>68</a>    <span class="doccomment">/// The message header, identifying signed and read-only `account_keys`.
<a href=#69 id=69 data-nosnippet>69</a>    /// Header values only describe static `account_keys`, they do not describe
<a href=#70 id=70 data-nosnippet>70</a>    /// any additional account keys loaded via address table lookups.
<a href=#71 id=71 data-nosnippet>71</a>    </span><span class="kw">pub </span>header: MessageHeader,
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>    <span class="doccomment">/// List of accounts loaded by this transaction.
<a href=#74 id=74 data-nosnippet>74</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#75 id=75 data-nosnippet>75</a>    </span><span class="kw">pub </span>account_keys: Vec&lt;Pubkey&gt;,
<a href=#76 id=76 data-nosnippet>76</a>
<a href=#77 id=77 data-nosnippet>77</a>    <span class="doccomment">/// The blockhash of a recent block.
<a href=#78 id=78 data-nosnippet>78</a>    </span><span class="kw">pub </span>recent_blockhash: Hash,
<a href=#79 id=79 data-nosnippet>79</a>
<a href=#80 id=80 data-nosnippet>80</a>    <span class="doccomment">/// Instructions that invoke a designated program, are executed in sequence,
<a href=#81 id=81 data-nosnippet>81</a>    /// and committed in one atomic transaction if all succeed.
<a href=#82 id=82 data-nosnippet>82</a>    ///
<a href=#83 id=83 data-nosnippet>83</a>    /// # Notes
<a href=#84 id=84 data-nosnippet>84</a>    ///
<a href=#85 id=85 data-nosnippet>85</a>    /// Program indexes must index into the list of message `account_keys` because
<a href=#86 id=86 data-nosnippet>86</a>    /// program id's cannot be dynamically loaded from a lookup table.
<a href=#87 id=87 data-nosnippet>87</a>    ///
<a href=#88 id=88 data-nosnippet>88</a>    /// Account indexes must index into the list of addresses
<a href=#89 id=89 data-nosnippet>89</a>    /// constructed from the concatenation of three key lists:
<a href=#90 id=90 data-nosnippet>90</a>    ///   1) message `account_keys`
<a href=#91 id=91 data-nosnippet>91</a>    ///   2) ordered list of keys loaded from `writable` lookup table indexes
<a href=#92 id=92 data-nosnippet>92</a>    ///   3) ordered list of keys loaded from `readable` lookup table indexes
<a href=#93 id=93 data-nosnippet>93</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#94 id=94 data-nosnippet>94</a>    </span><span class="kw">pub </span>instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#95 id=95 data-nosnippet>95</a>
<a href=#96 id=96 data-nosnippet>96</a>    <span class="doccomment">/// List of address table lookups used to load additional accounts
<a href=#97 id=97 data-nosnippet>97</a>    /// for this transaction.
<a href=#98 id=98 data-nosnippet>98</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#99 id=99 data-nosnippet>99</a>    </span><span class="kw">pub </span>address_table_lookups: Vec&lt;MessageAddressTableLookup&gt;,
<a href=#100 id=100 data-nosnippet>100</a>}
<a href=#101 id=101 data-nosnippet>101</a>
<a href=#102 id=102 data-nosnippet>102</a><span class="kw">impl </span>Message {
<a href=#103 id=103 data-nosnippet>103</a>    <span class="doccomment">/// Sanitize message fields and compiled instruction indexes
<a href=#104 id=104 data-nosnippet>104</a>    </span><span class="kw">pub fn </span>sanitize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;(), SanitizeError&gt; {
<a href=#105 id=105 data-nosnippet>105</a>        <span class="kw">let </span>num_static_account_keys = <span class="self">self</span>.account_keys.len();
<a href=#106 id=106 data-nosnippet>106</a>        <span class="kw">if </span>usize::from(<span class="self">self</span>.header.num_required_signatures)
<a href=#107 id=107 data-nosnippet>107</a>            .saturating_add(usize::from(<span class="self">self</span>.header.num_readonly_unsigned_accounts))
<a href=#108 id=108 data-nosnippet>108</a>            &gt; num_static_account_keys
<a href=#109 id=109 data-nosnippet>109</a>        {
<a href=#110 id=110 data-nosnippet>110</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#111 id=111 data-nosnippet>111</a>        }
<a href=#112 id=112 data-nosnippet>112</a>
<a href=#113 id=113 data-nosnippet>113</a>        <span class="comment">// there should be at least 1 RW fee-payer account.
<a href=#114 id=114 data-nosnippet>114</a>        </span><span class="kw">if </span><span class="self">self</span>.header.num_readonly_signed_accounts &gt;= <span class="self">self</span>.header.num_required_signatures {
<a href=#115 id=115 data-nosnippet>115</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::InvalidValue);
<a href=#116 id=116 data-nosnippet>116</a>        }
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a>        <span class="kw">let </span>num_dynamic_account_keys = {
<a href=#119 id=119 data-nosnippet>119</a>            <span class="kw">let </span><span class="kw-2">mut </span>total_lookup_keys: usize = <span class="number">0</span>;
<a href=#120 id=120 data-nosnippet>120</a>            <span class="kw">for </span>lookup <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.address_table_lookups {
<a href=#121 id=121 data-nosnippet>121</a>                <span class="kw">let </span>num_lookup_indexes = lookup
<a href=#122 id=122 data-nosnippet>122</a>                    .writable_indexes
<a href=#123 id=123 data-nosnippet>123</a>                    .len()
<a href=#124 id=124 data-nosnippet>124</a>                    .saturating_add(lookup.readonly_indexes.len());
<a href=#125 id=125 data-nosnippet>125</a>
<a href=#126 id=126 data-nosnippet>126</a>                <span class="comment">// each lookup table must be used to load at least one account
<a href=#127 id=127 data-nosnippet>127</a>                </span><span class="kw">if </span>num_lookup_indexes == <span class="number">0 </span>{
<a href=#128 id=128 data-nosnippet>128</a>                    <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::InvalidValue);
<a href=#129 id=129 data-nosnippet>129</a>                }
<a href=#130 id=130 data-nosnippet>130</a>
<a href=#131 id=131 data-nosnippet>131</a>                total_lookup_keys = total_lookup_keys.saturating_add(num_lookup_indexes);
<a href=#132 id=132 data-nosnippet>132</a>            }
<a href=#133 id=133 data-nosnippet>133</a>            total_lookup_keys
<a href=#134 id=134 data-nosnippet>134</a>        };
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>        <span class="comment">// this is redundant with the above sanitization checks which require that:
<a href=#137 id=137 data-nosnippet>137</a>        // 1) the header describes at least 1 RW account
<a href=#138 id=138 data-nosnippet>138</a>        // 2) the header doesn't describe more account keys than the number of account keys
<a href=#139 id=139 data-nosnippet>139</a>        </span><span class="kw">if </span>num_static_account_keys == <span class="number">0 </span>{
<a href=#140 id=140 data-nosnippet>140</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::InvalidValue);
<a href=#141 id=141 data-nosnippet>141</a>        }
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>        <span class="comment">// the combined number of static and dynamic account keys must be &lt;= 256
<a href=#144 id=144 data-nosnippet>144</a>        // since account indices are encoded as `u8`
<a href=#145 id=145 data-nosnippet>145</a>        // Note that this is different from the per-transaction account load cap
<a href=#146 id=146 data-nosnippet>146</a>        // as defined in `Bank::get_transaction_account_lock_limit`
<a href=#147 id=147 data-nosnippet>147</a>        </span><span class="kw">let </span>total_account_keys = num_static_account_keys.saturating_add(num_dynamic_account_keys);
<a href=#148 id=148 data-nosnippet>148</a>        <span class="kw">if </span>total_account_keys &gt; <span class="number">256 </span>{
<a href=#149 id=149 data-nosnippet>149</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#150 id=150 data-nosnippet>150</a>        }
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>        <span class="comment">// `expect` is safe because of earlier check that
<a href=#153 id=153 data-nosnippet>153</a>        // `num_static_account_keys` is non-zero
<a href=#154 id=154 data-nosnippet>154</a>        </span><span class="kw">let </span>max_account_ix = total_account_keys
<a href=#155 id=155 data-nosnippet>155</a>            .checked_sub(<span class="number">1</span>)
<a href=#156 id=156 data-nosnippet>156</a>            .expect(<span class="string">"message doesn't contain any account keys"</span>);
<a href=#157 id=157 data-nosnippet>157</a>
<a href=#158 id=158 data-nosnippet>158</a>        <span class="comment">// reject program ids loaded from lookup tables so that
<a href=#159 id=159 data-nosnippet>159</a>        // static analysis on program instructions can be performed
<a href=#160 id=160 data-nosnippet>160</a>        // without loading on-chain data from a bank
<a href=#161 id=161 data-nosnippet>161</a>        </span><span class="kw">let </span>max_program_id_ix =
<a href=#162 id=162 data-nosnippet>162</a>            <span class="comment">// `expect` is safe because of earlier check that
<a href=#163 id=163 data-nosnippet>163</a>            // `num_static_account_keys` is non-zero
<a href=#164 id=164 data-nosnippet>164</a>            </span>num_static_account_keys
<a href=#165 id=165 data-nosnippet>165</a>                .checked_sub(<span class="number">1</span>)
<a href=#166 id=166 data-nosnippet>166</a>                .expect(<span class="string">"message doesn't contain any static account keys"</span>);
<a href=#167 id=167 data-nosnippet>167</a>
<a href=#168 id=168 data-nosnippet>168</a>        <span class="kw">for </span>ci <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.instructions {
<a href=#169 id=169 data-nosnippet>169</a>            <span class="kw">if </span>usize::from(ci.program_id_index) &gt; max_program_id_ix {
<a href=#170 id=170 data-nosnippet>170</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#171 id=171 data-nosnippet>171</a>            }
<a href=#172 id=172 data-nosnippet>172</a>            <span class="comment">// A program cannot be a payer.
<a href=#173 id=173 data-nosnippet>173</a>            </span><span class="kw">if </span>ci.program_id_index == <span class="number">0 </span>{
<a href=#174 id=174 data-nosnippet>174</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#175 id=175 data-nosnippet>175</a>            }
<a href=#176 id=176 data-nosnippet>176</a>            <span class="kw">for </span>ai <span class="kw">in </span><span class="kw-2">&amp;</span>ci.accounts {
<a href=#177 id=177 data-nosnippet>177</a>                <span class="kw">if </span>usize::from(<span class="kw-2">*</span>ai) &gt; max_account_ix {
<a href=#178 id=178 data-nosnippet>178</a>                    <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#179 id=179 data-nosnippet>179</a>                }
<a href=#180 id=180 data-nosnippet>180</a>            }
<a href=#181 id=181 data-nosnippet>181</a>        }
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a>        <span class="prelude-val">Ok</span>(())
<a href=#184 id=184 data-nosnippet>184</a>    }
<a href=#185 id=185 data-nosnippet>185</a>}
<a href=#186 id=186 data-nosnippet>186</a>
<a href=#187 id=187 data-nosnippet>187</a><span class="kw">impl </span>Message {
<a href=#188 id=188 data-nosnippet>188</a>    <span class="doccomment">/// Create a signable transaction message from a `payer` public key,
<a href=#189 id=189 data-nosnippet>189</a>    /// `recent_blockhash`, list of `instructions`, and a list of
<a href=#190 id=190 data-nosnippet>190</a>    /// `address_lookup_table_accounts`.
<a href=#191 id=191 data-nosnippet>191</a>    ///
<a href=#192 id=192 data-nosnippet>192</a>    /// # Examples
<a href=#193 id=193 data-nosnippet>193</a>    ///
<a href=#194 id=194 data-nosnippet>194</a>    /// This example uses the [`solana_rpc_client`], [`solana_sdk`], and [`anyhow`] crates.
<a href=#195 id=195 data-nosnippet>195</a>    ///
<a href=#196 id=196 data-nosnippet>196</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#197 id=197 data-nosnippet>197</a>    /// [`solana_sdk`]: https://docs.rs/solana-sdk
<a href=#198 id=198 data-nosnippet>198</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#199 id=199 data-nosnippet>199</a>    ///
<a href=#200 id=200 data-nosnippet>200</a>    /// ```
<a href=#201 id=201 data-nosnippet>201</a>    /// # use solana_program::example_mocks::{
<a href=#202 id=202 data-nosnippet>202</a>    /// #     solana_rpc_client,
<a href=#203 id=203 data-nosnippet>203</a>    /// #     solana_sdk,
<a href=#204 id=204 data-nosnippet>204</a>    /// # };
<a href=#205 id=205 data-nosnippet>205</a>    /// # use std::borrow::Cow;
<a href=#206 id=206 data-nosnippet>206</a>    /// # use solana_sdk::account::Account;
<a href=#207 id=207 data-nosnippet>207</a>    /// use anyhow::Result;
<a href=#208 id=208 data-nosnippet>208</a>    /// use solana_instruction::{AccountMeta, Instruction};
<a href=#209 id=209 data-nosnippet>209</a>    /// use solana_message::{AddressLookupTableAccount, VersionedMessage, v0};
<a href=#210 id=210 data-nosnippet>210</a>    /// use solana_pubkey::Pubkey;
<a href=#211 id=211 data-nosnippet>211</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#212 id=212 data-nosnippet>212</a>    /// use solana_program::address_lookup_table::{self, state::{AddressLookupTable, LookupTableMeta}};
<a href=#213 id=213 data-nosnippet>213</a>    /// use solana_sdk::{
<a href=#214 id=214 data-nosnippet>214</a>    ///      signature::{Keypair, Signer},
<a href=#215 id=215 data-nosnippet>215</a>    ///      transaction::VersionedTransaction,
<a href=#216 id=216 data-nosnippet>216</a>    /// };
<a href=#217 id=217 data-nosnippet>217</a>    ///
<a href=#218 id=218 data-nosnippet>218</a>    /// fn create_tx_with_address_table_lookup(
<a href=#219 id=219 data-nosnippet>219</a>    ///     client: &amp;RpcClient,
<a href=#220 id=220 data-nosnippet>220</a>    ///     instruction: Instruction,
<a href=#221 id=221 data-nosnippet>221</a>    ///     address_lookup_table_key: Pubkey,
<a href=#222 id=222 data-nosnippet>222</a>    ///     payer: &amp;Keypair,
<a href=#223 id=223 data-nosnippet>223</a>    /// ) -&gt; Result&lt;VersionedTransaction&gt; {
<a href=#224 id=224 data-nosnippet>224</a>    ///     # client.set_get_account_response(address_lookup_table_key, Account {
<a href=#225 id=225 data-nosnippet>225</a>    ///     #   lamports: 1,
<a href=#226 id=226 data-nosnippet>226</a>    ///     #   data: AddressLookupTable {
<a href=#227 id=227 data-nosnippet>227</a>    ///     #     meta: LookupTableMeta::default(),
<a href=#228 id=228 data-nosnippet>228</a>    ///     #     addresses: Cow::Owned(instruction.accounts.iter().map(|meta| meta.pubkey).collect()),
<a href=#229 id=229 data-nosnippet>229</a>    ///     #   }.serialize_for_tests().unwrap(),
<a href=#230 id=230 data-nosnippet>230</a>    ///     #   owner: address_lookup_table::program::id(),
<a href=#231 id=231 data-nosnippet>231</a>    ///     #   executable: false,
<a href=#232 id=232 data-nosnippet>232</a>    ///     #   rent_epoch: 1,
<a href=#233 id=233 data-nosnippet>233</a>    ///     # });
<a href=#234 id=234 data-nosnippet>234</a>    ///     let raw_account = client.get_account(&amp;address_lookup_table_key)?;
<a href=#235 id=235 data-nosnippet>235</a>    ///     let address_lookup_table = AddressLookupTable::deserialize(&amp;raw_account.data)?;
<a href=#236 id=236 data-nosnippet>236</a>    ///     let address_lookup_table_account = AddressLookupTableAccount {
<a href=#237 id=237 data-nosnippet>237</a>    ///         key: address_lookup_table_key,
<a href=#238 id=238 data-nosnippet>238</a>    ///         addresses: address_lookup_table.addresses.to_vec(),
<a href=#239 id=239 data-nosnippet>239</a>    ///     };
<a href=#240 id=240 data-nosnippet>240</a>    ///
<a href=#241 id=241 data-nosnippet>241</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#242 id=242 data-nosnippet>242</a>    ///     let tx = VersionedTransaction::try_new(
<a href=#243 id=243 data-nosnippet>243</a>    ///         VersionedMessage::V0(v0::Message::try_compile(
<a href=#244 id=244 data-nosnippet>244</a>    ///             &amp;payer.pubkey(),
<a href=#245 id=245 data-nosnippet>245</a>    ///             &amp;[instruction],
<a href=#246 id=246 data-nosnippet>246</a>    ///             &amp;[address_lookup_table_account],
<a href=#247 id=247 data-nosnippet>247</a>    ///             blockhash,
<a href=#248 id=248 data-nosnippet>248</a>    ///         )?),
<a href=#249 id=249 data-nosnippet>249</a>    ///         &amp;[payer],
<a href=#250 id=250 data-nosnippet>250</a>    ///     )?;
<a href=#251 id=251 data-nosnippet>251</a>    ///
<a href=#252 id=252 data-nosnippet>252</a>    ///     # assert!(tx.message.address_table_lookups().unwrap().len() &gt; 0);
<a href=#253 id=253 data-nosnippet>253</a>    ///     Ok(tx)
<a href=#254 id=254 data-nosnippet>254</a>    /// }
<a href=#255 id=255 data-nosnippet>255</a>    /// #
<a href=#256 id=256 data-nosnippet>256</a>    /// # let client = RpcClient::new(String::new());
<a href=#257 id=257 data-nosnippet>257</a>    /// # let payer = Keypair::new();
<a href=#258 id=258 data-nosnippet>258</a>    /// # let address_lookup_table_key = Pubkey::new_unique();
<a href=#259 id=259 data-nosnippet>259</a>    /// # let instruction = Instruction::new_with_bincode(Pubkey::new_unique(), &amp;(), vec![
<a href=#260 id=260 data-nosnippet>260</a>    /// #   AccountMeta::new(Pubkey::new_unique(), false),
<a href=#261 id=261 data-nosnippet>261</a>    /// # ]);
<a href=#262 id=262 data-nosnippet>262</a>    /// # create_tx_with_address_table_lookup(&amp;client, instruction, address_lookup_table_key, &amp;payer)?;
<a href=#263 id=263 data-nosnippet>263</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#264 id=264 data-nosnippet>264</a>    /// ```
<a href=#265 id=265 data-nosnippet>265</a>    </span><span class="kw">pub fn </span>try_compile(
<a href=#266 id=266 data-nosnippet>266</a>        payer: <span class="kw-2">&amp;</span>Pubkey,
<a href=#267 id=267 data-nosnippet>267</a>        instructions: <span class="kw-2">&amp;</span>[Instruction],
<a href=#268 id=268 data-nosnippet>268</a>        address_lookup_table_accounts: <span class="kw-2">&amp;</span>[AddressLookupTableAccount],
<a href=#269 id=269 data-nosnippet>269</a>        recent_blockhash: Hash,
<a href=#270 id=270 data-nosnippet>270</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, CompileError&gt; {
<a href=#271 id=271 data-nosnippet>271</a>        <span class="kw">let </span><span class="kw-2">mut </span>compiled_keys = CompiledKeys::compile(instructions, <span class="prelude-val">Some</span>(<span class="kw-2">*</span>payer));
<a href=#272 id=272 data-nosnippet>272</a>
<a href=#273 id=273 data-nosnippet>273</a>        <span class="kw">let </span><span class="kw-2">mut </span>address_table_lookups = Vec::with_capacity(address_lookup_table_accounts.len());
<a href=#274 id=274 data-nosnippet>274</a>        <span class="kw">let </span><span class="kw-2">mut </span>loaded_addresses_list = Vec::with_capacity(address_lookup_table_accounts.len());
<a href=#275 id=275 data-nosnippet>275</a>        <span class="kw">for </span>lookup_table_account <span class="kw">in </span>address_lookup_table_accounts {
<a href=#276 id=276 data-nosnippet>276</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>((lookup, loaded_addresses)) =
<a href=#277 id=277 data-nosnippet>277</a>                compiled_keys.try_extract_table_lookup(lookup_table_account)<span class="question-mark">?
<a href=#278 id=278 data-nosnippet>278</a>            </span>{
<a href=#279 id=279 data-nosnippet>279</a>                address_table_lookups.push(lookup);
<a href=#280 id=280 data-nosnippet>280</a>                loaded_addresses_list.push(loaded_addresses);
<a href=#281 id=281 data-nosnippet>281</a>            }
<a href=#282 id=282 data-nosnippet>282</a>        }
<a href=#283 id=283 data-nosnippet>283</a>
<a href=#284 id=284 data-nosnippet>284</a>        <span class="kw">let </span>(header, static_keys) = compiled_keys.try_into_message_components()<span class="question-mark">?</span>;
<a href=#285 id=285 data-nosnippet>285</a>        <span class="kw">let </span>dynamic_keys = loaded_addresses_list.into_iter().collect();
<a href=#286 id=286 data-nosnippet>286</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#287 id=287 data-nosnippet>287</a>        <span class="kw">let </span>instructions = account_keys.try_compile_instructions(instructions)<span class="question-mark">?</span>;
<a href=#288 id=288 data-nosnippet>288</a>
<a href=#289 id=289 data-nosnippet>289</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#290 id=290 data-nosnippet>290</a>            header,
<a href=#291 id=291 data-nosnippet>291</a>            account_keys: static_keys,
<a href=#292 id=292 data-nosnippet>292</a>            recent_blockhash,
<a href=#293 id=293 data-nosnippet>293</a>            instructions,
<a href=#294 id=294 data-nosnippet>294</a>            address_table_lookups,
<a href=#295 id=295 data-nosnippet>295</a>        })
<a href=#296 id=296 data-nosnippet>296</a>    }
<a href=#297 id=297 data-nosnippet>297</a>
<a href=#298 id=298 data-nosnippet>298</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#299 id=299 data-nosnippet>299</a>    </span><span class="doccomment">/// Serialize this message with a version #0 prefix using bincode encoding.
<a href=#300 id=300 data-nosnippet>300</a>    </span><span class="kw">pub fn </span>serialize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;u8&gt; {
<a href=#301 id=301 data-nosnippet>301</a>        bincode::serialize(<span class="kw-2">&amp;</span>(<span class="kw">crate</span>::MESSAGE_VERSION_PREFIX, <span class="self">self</span>)).unwrap()
<a href=#302 id=302 data-nosnippet>302</a>    }
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>    <span class="doccomment">/// Returns true if the account at the specified index is called as a program by an instruction
<a href=#305 id=305 data-nosnippet>305</a>    </span><span class="kw">pub fn </span>is_key_called_as_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#306 id=306 data-nosnippet>306</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(key_index) = u8::try_from(key_index) {
<a href=#307 id=307 data-nosnippet>307</a>            <span class="self">self</span>.instructions
<a href=#308 id=308 data-nosnippet>308</a>                .iter()
<a href=#309 id=309 data-nosnippet>309</a>                .any(|ix| ix.program_id_index == key_index)
<a href=#310 id=310 data-nosnippet>310</a>        } <span class="kw">else </span>{
<a href=#311 id=311 data-nosnippet>311</a>            <span class="bool-val">false
<a href=#312 id=312 data-nosnippet>312</a>        </span>}
<a href=#313 id=313 data-nosnippet>313</a>    }
<a href=#314 id=314 data-nosnippet>314</a>
<a href=#315 id=315 data-nosnippet>315</a>    <span class="doccomment">/// Returns true if the account at the specified index was requested to be
<a href=#316 id=316 data-nosnippet>316</a>    /// writable.  This method should not be used directly.
<a href=#317 id=317 data-nosnippet>317</a>    </span><span class="kw">fn </span>is_writable_index(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#318 id=318 data-nosnippet>318</a>        <span class="kw">let </span>header = <span class="kw-2">&amp;</span><span class="self">self</span>.header;
<a href=#319 id=319 data-nosnippet>319</a>        <span class="kw">let </span>num_account_keys = <span class="self">self</span>.account_keys.len();
<a href=#320 id=320 data-nosnippet>320</a>        <span class="kw">let </span>num_signed_accounts = usize::from(header.num_required_signatures);
<a href=#321 id=321 data-nosnippet>321</a>        <span class="kw">if </span>key_index &gt;= num_account_keys {
<a href=#322 id=322 data-nosnippet>322</a>            <span class="kw">let </span>loaded_addresses_index = key_index.saturating_sub(num_account_keys);
<a href=#323 id=323 data-nosnippet>323</a>            <span class="kw">let </span>num_writable_dynamic_addresses = <span class="self">self
<a href=#324 id=324 data-nosnippet>324</a>                </span>.address_table_lookups
<a href=#325 id=325 data-nosnippet>325</a>                .iter()
<a href=#326 id=326 data-nosnippet>326</a>                .map(|lookup| lookup.writable_indexes.len())
<a href=#327 id=327 data-nosnippet>327</a>                .sum();
<a href=#328 id=328 data-nosnippet>328</a>            loaded_addresses_index &lt; num_writable_dynamic_addresses
<a href=#329 id=329 data-nosnippet>329</a>        } <span class="kw">else if </span>key_index &gt;= num_signed_accounts {
<a href=#330 id=330 data-nosnippet>330</a>            <span class="kw">let </span>num_unsigned_accounts = num_account_keys.saturating_sub(num_signed_accounts);
<a href=#331 id=331 data-nosnippet>331</a>            <span class="kw">let </span>num_writable_unsigned_accounts = num_unsigned_accounts
<a href=#332 id=332 data-nosnippet>332</a>                .saturating_sub(usize::from(header.num_readonly_unsigned_accounts));
<a href=#333 id=333 data-nosnippet>333</a>            <span class="kw">let </span>unsigned_account_index = key_index.saturating_sub(num_signed_accounts);
<a href=#334 id=334 data-nosnippet>334</a>            unsigned_account_index &lt; num_writable_unsigned_accounts
<a href=#335 id=335 data-nosnippet>335</a>        } <span class="kw">else </span>{
<a href=#336 id=336 data-nosnippet>336</a>            <span class="kw">let </span>num_writable_signed_accounts = num_signed_accounts
<a href=#337 id=337 data-nosnippet>337</a>                .saturating_sub(usize::from(header.num_readonly_signed_accounts));
<a href=#338 id=338 data-nosnippet>338</a>            key_index &lt; num_writable_signed_accounts
<a href=#339 id=339 data-nosnippet>339</a>        }
<a href=#340 id=340 data-nosnippet>340</a>    }
<a href=#341 id=341 data-nosnippet>341</a>
<a href=#342 id=342 data-nosnippet>342</a>    <span class="doccomment">/// Returns true if any static account key is the bpf upgradeable loader
<a href=#343 id=343 data-nosnippet>343</a>    </span><span class="kw">fn </span>is_upgradeable_loader_in_static_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#344 id=344 data-nosnippet>344</a>        <span class="self">self</span>.account_keys
<a href=#345 id=345 data-nosnippet>345</a>            .iter()
<a href=#346 id=346 data-nosnippet>346</a>            .any(|<span class="kw-2">&amp;</span>key| key == bpf_loader_upgradeable::id())
<a href=#347 id=347 data-nosnippet>347</a>    }
<a href=#348 id=348 data-nosnippet>348</a>
<a href=#349 id=349 data-nosnippet>349</a>    <span class="doccomment">/// Returns true if the account at the specified index was requested as
<a href=#350 id=350 data-nosnippet>350</a>    /// writable. Before loading addresses, we can't demote write locks properly
<a href=#351 id=351 data-nosnippet>351</a>    /// so this should not be used by the runtime. The `reserved_account_keys`
<a href=#352 id=352 data-nosnippet>352</a>    /// param is optional to allow clients to approximate writability without
<a href=#353 id=353 data-nosnippet>353</a>    /// requiring fetching the latest set of reserved account keys.
<a href=#354 id=354 data-nosnippet>354</a>    </span><span class="kw">pub fn </span>is_maybe_writable(
<a href=#355 id=355 data-nosnippet>355</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#356 id=356 data-nosnippet>356</a>        key_index: usize,
<a href=#357 id=357 data-nosnippet>357</a>        reserved_account_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;&gt;,
<a href=#358 id=358 data-nosnippet>358</a>    ) -&gt; bool {
<a href=#359 id=359 data-nosnippet>359</a>        <span class="self">self</span>.is_writable_index(key_index)
<a href=#360 id=360 data-nosnippet>360</a>            &amp;&amp; !<span class="self">self</span>.is_account_maybe_reserved(key_index, reserved_account_keys)
<a href=#361 id=361 data-nosnippet>361</a>            &amp;&amp; !{
<a href=#362 id=362 data-nosnippet>362</a>                <span class="comment">// demote program ids
<a href=#363 id=363 data-nosnippet>363</a>                </span><span class="self">self</span>.is_key_called_as_program(key_index)
<a href=#364 id=364 data-nosnippet>364</a>                    &amp;&amp; !<span class="self">self</span>.is_upgradeable_loader_in_static_keys()
<a href=#365 id=365 data-nosnippet>365</a>            }
<a href=#366 id=366 data-nosnippet>366</a>    }
<a href=#367 id=367 data-nosnippet>367</a>
<a href=#368 id=368 data-nosnippet>368</a>    <span class="doccomment">/// Returns true if the account at the specified index is in the reserved
<a href=#369 id=369 data-nosnippet>369</a>    /// account keys set. Before loading addresses, we can't detect reserved
<a href=#370 id=370 data-nosnippet>370</a>    /// account keys properly so this shouldn't be used by the runtime.
<a href=#371 id=371 data-nosnippet>371</a>    </span><span class="kw">fn </span>is_account_maybe_reserved(
<a href=#372 id=372 data-nosnippet>372</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#373 id=373 data-nosnippet>373</a>        key_index: usize,
<a href=#374 id=374 data-nosnippet>374</a>        reserved_account_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;&gt;,
<a href=#375 id=375 data-nosnippet>375</a>    ) -&gt; bool {
<a href=#376 id=376 data-nosnippet>376</a>        <span class="kw">let </span><span class="kw-2">mut </span>is_maybe_reserved = <span class="bool-val">false</span>;
<a href=#377 id=377 data-nosnippet>377</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(reserved_account_keys) = reserved_account_keys {
<a href=#378 id=378 data-nosnippet>378</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(key) = <span class="self">self</span>.account_keys.get(key_index) {
<a href=#379 id=379 data-nosnippet>379</a>                is_maybe_reserved = reserved_account_keys.contains(key);
<a href=#380 id=380 data-nosnippet>380</a>            }
<a href=#381 id=381 data-nosnippet>381</a>        }
<a href=#382 id=382 data-nosnippet>382</a>        is_maybe_reserved
<a href=#383 id=383 data-nosnippet>383</a>    }
<a href=#384 id=384 data-nosnippet>384</a>}
<a href=#385 id=385 data-nosnippet>385</a>
<a href=#386 id=386 data-nosnippet>386</a><span class="attr">#[cfg(test)]
<a href=#387 id=387 data-nosnippet>387</a></span><span class="kw">mod </span>tests {
<a href=#388 id=388 data-nosnippet>388</a>    <span class="kw">use </span>{<span class="kw">super</span>::<span class="kw-2">*</span>, <span class="kw">crate</span>::VersionedMessage, solana_instruction::AccountMeta};
<a href=#389 id=389 data-nosnippet>389</a>
<a href=#390 id=390 data-nosnippet>390</a>    <span class="attr">#[test]
<a href=#391 id=391 data-nosnippet>391</a>    </span><span class="kw">fn </span>test_sanitize() {
<a href=#392 id=392 data-nosnippet>392</a>        <span class="macro">assert!</span>(Message {
<a href=#393 id=393 data-nosnippet>393</a>            header: MessageHeader {
<a href=#394 id=394 data-nosnippet>394</a>                num_required_signatures: <span class="number">1</span>,
<a href=#395 id=395 data-nosnippet>395</a>                ..MessageHeader::default()
<a href=#396 id=396 data-nosnippet>396</a>            },
<a href=#397 id=397 data-nosnippet>397</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#398 id=398 data-nosnippet>398</a>            ..Message::default()
<a href=#399 id=399 data-nosnippet>399</a>        }
<a href=#400 id=400 data-nosnippet>400</a>        .sanitize()
<a href=#401 id=401 data-nosnippet>401</a>        .is_ok());
<a href=#402 id=402 data-nosnippet>402</a>    }
<a href=#403 id=403 data-nosnippet>403</a>
<a href=#404 id=404 data-nosnippet>404</a>    <span class="attr">#[test]
<a href=#405 id=405 data-nosnippet>405</a>    </span><span class="kw">fn </span>test_sanitize_with_instruction() {
<a href=#406 id=406 data-nosnippet>406</a>        <span class="macro">assert!</span>(Message {
<a href=#407 id=407 data-nosnippet>407</a>            header: MessageHeader {
<a href=#408 id=408 data-nosnippet>408</a>                num_required_signatures: <span class="number">1</span>,
<a href=#409 id=409 data-nosnippet>409</a>                ..MessageHeader::default()
<a href=#410 id=410 data-nosnippet>410</a>            },
<a href=#411 id=411 data-nosnippet>411</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique(), Pubkey::new_unique()],
<a href=#412 id=412 data-nosnippet>412</a>            instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#413 id=413 data-nosnippet>413</a>                program_id_index: <span class="number">1</span>,
<a href=#414 id=414 data-nosnippet>414</a>                accounts: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#415 id=415 data-nosnippet>415</a>                data: <span class="macro">vec!</span>[]
<a href=#416 id=416 data-nosnippet>416</a>            }],
<a href=#417 id=417 data-nosnippet>417</a>            ..Message::default()
<a href=#418 id=418 data-nosnippet>418</a>        }
<a href=#419 id=419 data-nosnippet>419</a>        .sanitize()
<a href=#420 id=420 data-nosnippet>420</a>        .is_ok());
<a href=#421 id=421 data-nosnippet>421</a>    }
<a href=#422 id=422 data-nosnippet>422</a>
<a href=#423 id=423 data-nosnippet>423</a>    <span class="attr">#[test]
<a href=#424 id=424 data-nosnippet>424</a>    </span><span class="kw">fn </span>test_sanitize_with_table_lookup() {
<a href=#425 id=425 data-nosnippet>425</a>        <span class="macro">assert!</span>(Message {
<a href=#426 id=426 data-nosnippet>426</a>            header: MessageHeader {
<a href=#427 id=427 data-nosnippet>427</a>                num_required_signatures: <span class="number">1</span>,
<a href=#428 id=428 data-nosnippet>428</a>                ..MessageHeader::default()
<a href=#429 id=429 data-nosnippet>429</a>            },
<a href=#430 id=430 data-nosnippet>430</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#431 id=431 data-nosnippet>431</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#432 id=432 data-nosnippet>432</a>                account_key: Pubkey::new_unique(),
<a href=#433 id=433 data-nosnippet>433</a>                writable_indexes: <span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],
<a href=#434 id=434 data-nosnippet>434</a>                readonly_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#435 id=435 data-nosnippet>435</a>            }],
<a href=#436 id=436 data-nosnippet>436</a>            ..Message::default()
<a href=#437 id=437 data-nosnippet>437</a>        }
<a href=#438 id=438 data-nosnippet>438</a>        .sanitize()
<a href=#439 id=439 data-nosnippet>439</a>        .is_ok());
<a href=#440 id=440 data-nosnippet>440</a>    }
<a href=#441 id=441 data-nosnippet>441</a>
<a href=#442 id=442 data-nosnippet>442</a>    <span class="attr">#[test]
<a href=#443 id=443 data-nosnippet>443</a>    </span><span class="kw">fn </span>test_sanitize_with_table_lookup_and_ix_with_dynamic_program_id() {
<a href=#444 id=444 data-nosnippet>444</a>        <span class="kw">let </span>message = Message {
<a href=#445 id=445 data-nosnippet>445</a>            header: MessageHeader {
<a href=#446 id=446 data-nosnippet>446</a>                num_required_signatures: <span class="number">1</span>,
<a href=#447 id=447 data-nosnippet>447</a>                ..MessageHeader::default()
<a href=#448 id=448 data-nosnippet>448</a>            },
<a href=#449 id=449 data-nosnippet>449</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#450 id=450 data-nosnippet>450</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#451 id=451 data-nosnippet>451</a>                account_key: Pubkey::new_unique(),
<a href=#452 id=452 data-nosnippet>452</a>                writable_indexes: <span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],
<a href=#453 id=453 data-nosnippet>453</a>                readonly_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#454 id=454 data-nosnippet>454</a>            }],
<a href=#455 id=455 data-nosnippet>455</a>            instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#456 id=456 data-nosnippet>456</a>                program_id_index: <span class="number">4</span>,
<a href=#457 id=457 data-nosnippet>457</a>                accounts: <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],
<a href=#458 id=458 data-nosnippet>458</a>                data: <span class="macro">vec!</span>[],
<a href=#459 id=459 data-nosnippet>459</a>            }],
<a href=#460 id=460 data-nosnippet>460</a>            ..Message::default()
<a href=#461 id=461 data-nosnippet>461</a>        };
<a href=#462 id=462 data-nosnippet>462</a>
<a href=#463 id=463 data-nosnippet>463</a>        <span class="macro">assert!</span>(message.sanitize().is_err());
<a href=#464 id=464 data-nosnippet>464</a>    }
<a href=#465 id=465 data-nosnippet>465</a>
<a href=#466 id=466 data-nosnippet>466</a>    <span class="attr">#[test]
<a href=#467 id=467 data-nosnippet>467</a>    </span><span class="kw">fn </span>test_sanitize_with_table_lookup_and_ix_with_static_program_id() {
<a href=#468 id=468 data-nosnippet>468</a>        <span class="macro">assert!</span>(Message {
<a href=#469 id=469 data-nosnippet>469</a>            header: MessageHeader {
<a href=#470 id=470 data-nosnippet>470</a>                num_required_signatures: <span class="number">1</span>,
<a href=#471 id=471 data-nosnippet>471</a>                ..MessageHeader::default()
<a href=#472 id=472 data-nosnippet>472</a>            },
<a href=#473 id=473 data-nosnippet>473</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique(), Pubkey::new_unique()],
<a href=#474 id=474 data-nosnippet>474</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#475 id=475 data-nosnippet>475</a>                account_key: Pubkey::new_unique(),
<a href=#476 id=476 data-nosnippet>476</a>                writable_indexes: <span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],
<a href=#477 id=477 data-nosnippet>477</a>                readonly_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#478 id=478 data-nosnippet>478</a>            }],
<a href=#479 id=479 data-nosnippet>479</a>            instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#480 id=480 data-nosnippet>480</a>                program_id_index: <span class="number">1</span>,
<a href=#481 id=481 data-nosnippet>481</a>                accounts: <span class="macro">vec!</span>[<span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>],
<a href=#482 id=482 data-nosnippet>482</a>                data: <span class="macro">vec!</span>[]
<a href=#483 id=483 data-nosnippet>483</a>            }],
<a href=#484 id=484 data-nosnippet>484</a>            ..Message::default()
<a href=#485 id=485 data-nosnippet>485</a>        }
<a href=#486 id=486 data-nosnippet>486</a>        .sanitize()
<a href=#487 id=487 data-nosnippet>487</a>        .is_ok());
<a href=#488 id=488 data-nosnippet>488</a>    }
<a href=#489 id=489 data-nosnippet>489</a>
<a href=#490 id=490 data-nosnippet>490</a>    <span class="attr">#[test]
<a href=#491 id=491 data-nosnippet>491</a>    </span><span class="kw">fn </span>test_sanitize_without_signer() {
<a href=#492 id=492 data-nosnippet>492</a>        <span class="macro">assert!</span>(Message {
<a href=#493 id=493 data-nosnippet>493</a>            header: MessageHeader::default(),
<a href=#494 id=494 data-nosnippet>494</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#495 id=495 data-nosnippet>495</a>            ..Message::default()
<a href=#496 id=496 data-nosnippet>496</a>        }
<a href=#497 id=497 data-nosnippet>497</a>        .sanitize()
<a href=#498 id=498 data-nosnippet>498</a>        .is_err());
<a href=#499 id=499 data-nosnippet>499</a>    }
<a href=#500 id=500 data-nosnippet>500</a>
<a href=#501 id=501 data-nosnippet>501</a>    <span class="attr">#[test]
<a href=#502 id=502 data-nosnippet>502</a>    </span><span class="kw">fn </span>test_sanitize_without_writable_signer() {
<a href=#503 id=503 data-nosnippet>503</a>        <span class="macro">assert!</span>(Message {
<a href=#504 id=504 data-nosnippet>504</a>            header: MessageHeader {
<a href=#505 id=505 data-nosnippet>505</a>                num_required_signatures: <span class="number">1</span>,
<a href=#506 id=506 data-nosnippet>506</a>                num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#507 id=507 data-nosnippet>507</a>                ..MessageHeader::default()
<a href=#508 id=508 data-nosnippet>508</a>            },
<a href=#509 id=509 data-nosnippet>509</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#510 id=510 data-nosnippet>510</a>            ..Message::default()
<a href=#511 id=511 data-nosnippet>511</a>        }
<a href=#512 id=512 data-nosnippet>512</a>        .sanitize()
<a href=#513 id=513 data-nosnippet>513</a>        .is_err());
<a href=#514 id=514 data-nosnippet>514</a>    }
<a href=#515 id=515 data-nosnippet>515</a>
<a href=#516 id=516 data-nosnippet>516</a>    <span class="attr">#[test]
<a href=#517 id=517 data-nosnippet>517</a>    </span><span class="kw">fn </span>test_sanitize_with_empty_table_lookup() {
<a href=#518 id=518 data-nosnippet>518</a>        <span class="macro">assert!</span>(Message {
<a href=#519 id=519 data-nosnippet>519</a>            header: MessageHeader {
<a href=#520 id=520 data-nosnippet>520</a>                num_required_signatures: <span class="number">1</span>,
<a href=#521 id=521 data-nosnippet>521</a>                ..MessageHeader::default()
<a href=#522 id=522 data-nosnippet>522</a>            },
<a href=#523 id=523 data-nosnippet>523</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#524 id=524 data-nosnippet>524</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#525 id=525 data-nosnippet>525</a>                account_key: Pubkey::new_unique(),
<a href=#526 id=526 data-nosnippet>526</a>                writable_indexes: <span class="macro">vec!</span>[],
<a href=#527 id=527 data-nosnippet>527</a>                readonly_indexes: <span class="macro">vec!</span>[],
<a href=#528 id=528 data-nosnippet>528</a>            }],
<a href=#529 id=529 data-nosnippet>529</a>            ..Message::default()
<a href=#530 id=530 data-nosnippet>530</a>        }
<a href=#531 id=531 data-nosnippet>531</a>        .sanitize()
<a href=#532 id=532 data-nosnippet>532</a>        .is_err());
<a href=#533 id=533 data-nosnippet>533</a>    }
<a href=#534 id=534 data-nosnippet>534</a>
<a href=#535 id=535 data-nosnippet>535</a>    <span class="attr">#[test]
<a href=#536 id=536 data-nosnippet>536</a>    </span><span class="kw">fn </span>test_sanitize_with_max_account_keys() {
<a href=#537 id=537 data-nosnippet>537</a>        <span class="macro">assert!</span>(Message {
<a href=#538 id=538 data-nosnippet>538</a>            header: MessageHeader {
<a href=#539 id=539 data-nosnippet>539</a>                num_required_signatures: <span class="number">1</span>,
<a href=#540 id=540 data-nosnippet>540</a>                ..MessageHeader::default()
<a href=#541 id=541 data-nosnippet>541</a>            },
<a href=#542 id=542 data-nosnippet>542</a>            account_keys: (<span class="number">0</span>..=u8::MAX).map(|<span class="kw">_</span>| Pubkey::new_unique()).collect(),
<a href=#543 id=543 data-nosnippet>543</a>            ..Message::default()
<a href=#544 id=544 data-nosnippet>544</a>        }
<a href=#545 id=545 data-nosnippet>545</a>        .sanitize()
<a href=#546 id=546 data-nosnippet>546</a>        .is_ok());
<a href=#547 id=547 data-nosnippet>547</a>    }
<a href=#548 id=548 data-nosnippet>548</a>
<a href=#549 id=549 data-nosnippet>549</a>    <span class="attr">#[test]
<a href=#550 id=550 data-nosnippet>550</a>    </span><span class="kw">fn </span>test_sanitize_with_too_many_account_keys() {
<a href=#551 id=551 data-nosnippet>551</a>        <span class="macro">assert!</span>(Message {
<a href=#552 id=552 data-nosnippet>552</a>            header: MessageHeader {
<a href=#553 id=553 data-nosnippet>553</a>                num_required_signatures: <span class="number">1</span>,
<a href=#554 id=554 data-nosnippet>554</a>                ..MessageHeader::default()
<a href=#555 id=555 data-nosnippet>555</a>            },
<a href=#556 id=556 data-nosnippet>556</a>            account_keys: (<span class="number">0</span>..=<span class="number">256</span>).map(|<span class="kw">_</span>| Pubkey::new_unique()).collect(),
<a href=#557 id=557 data-nosnippet>557</a>            ..Message::default()
<a href=#558 id=558 data-nosnippet>558</a>        }
<a href=#559 id=559 data-nosnippet>559</a>        .sanitize()
<a href=#560 id=560 data-nosnippet>560</a>        .is_err());
<a href=#561 id=561 data-nosnippet>561</a>    }
<a href=#562 id=562 data-nosnippet>562</a>
<a href=#563 id=563 data-nosnippet>563</a>    <span class="attr">#[test]
<a href=#564 id=564 data-nosnippet>564</a>    </span><span class="kw">fn </span>test_sanitize_with_max_table_loaded_keys() {
<a href=#565 id=565 data-nosnippet>565</a>        <span class="macro">assert!</span>(Message {
<a href=#566 id=566 data-nosnippet>566</a>            header: MessageHeader {
<a href=#567 id=567 data-nosnippet>567</a>                num_required_signatures: <span class="number">1</span>,
<a href=#568 id=568 data-nosnippet>568</a>                ..MessageHeader::default()
<a href=#569 id=569 data-nosnippet>569</a>            },
<a href=#570 id=570 data-nosnippet>570</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#571 id=571 data-nosnippet>571</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#572 id=572 data-nosnippet>572</a>                account_key: Pubkey::new_unique(),
<a href=#573 id=573 data-nosnippet>573</a>                writable_indexes: (<span class="number">0</span>..=<span class="number">254</span>).step_by(<span class="number">2</span>).collect(),
<a href=#574 id=574 data-nosnippet>574</a>                readonly_indexes: (<span class="number">1</span>..=<span class="number">254</span>).step_by(<span class="number">2</span>).collect(),
<a href=#575 id=575 data-nosnippet>575</a>            }],
<a href=#576 id=576 data-nosnippet>576</a>            ..Message::default()
<a href=#577 id=577 data-nosnippet>577</a>        }
<a href=#578 id=578 data-nosnippet>578</a>        .sanitize()
<a href=#579 id=579 data-nosnippet>579</a>        .is_ok());
<a href=#580 id=580 data-nosnippet>580</a>    }
<a href=#581 id=581 data-nosnippet>581</a>
<a href=#582 id=582 data-nosnippet>582</a>    <span class="attr">#[test]
<a href=#583 id=583 data-nosnippet>583</a>    </span><span class="kw">fn </span>test_sanitize_with_too_many_table_loaded_keys() {
<a href=#584 id=584 data-nosnippet>584</a>        <span class="macro">assert!</span>(Message {
<a href=#585 id=585 data-nosnippet>585</a>            header: MessageHeader {
<a href=#586 id=586 data-nosnippet>586</a>                num_required_signatures: <span class="number">1</span>,
<a href=#587 id=587 data-nosnippet>587</a>                ..MessageHeader::default()
<a href=#588 id=588 data-nosnippet>588</a>            },
<a href=#589 id=589 data-nosnippet>589</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#590 id=590 data-nosnippet>590</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#591 id=591 data-nosnippet>591</a>                account_key: Pubkey::new_unique(),
<a href=#592 id=592 data-nosnippet>592</a>                writable_indexes: (<span class="number">0</span>..=<span class="number">255</span>).step_by(<span class="number">2</span>).collect(),
<a href=#593 id=593 data-nosnippet>593</a>                readonly_indexes: (<span class="number">1</span>..=<span class="number">255</span>).step_by(<span class="number">2</span>).collect(),
<a href=#594 id=594 data-nosnippet>594</a>            }],
<a href=#595 id=595 data-nosnippet>595</a>            ..Message::default()
<a href=#596 id=596 data-nosnippet>596</a>        }
<a href=#597 id=597 data-nosnippet>597</a>        .sanitize()
<a href=#598 id=598 data-nosnippet>598</a>        .is_err());
<a href=#599 id=599 data-nosnippet>599</a>    }
<a href=#600 id=600 data-nosnippet>600</a>
<a href=#601 id=601 data-nosnippet>601</a>    <span class="attr">#[test]
<a href=#602 id=602 data-nosnippet>602</a>    </span><span class="kw">fn </span>test_sanitize_with_invalid_ix_program_id() {
<a href=#603 id=603 data-nosnippet>603</a>        <span class="kw">let </span>message = Message {
<a href=#604 id=604 data-nosnippet>604</a>            header: MessageHeader {
<a href=#605 id=605 data-nosnippet>605</a>                num_required_signatures: <span class="number">1</span>,
<a href=#606 id=606 data-nosnippet>606</a>                ..MessageHeader::default()
<a href=#607 id=607 data-nosnippet>607</a>            },
<a href=#608 id=608 data-nosnippet>608</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#609 id=609 data-nosnippet>609</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#610 id=610 data-nosnippet>610</a>                account_key: Pubkey::new_unique(),
<a href=#611 id=611 data-nosnippet>611</a>                writable_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#612 id=612 data-nosnippet>612</a>                readonly_indexes: <span class="macro">vec!</span>[],
<a href=#613 id=613 data-nosnippet>613</a>            }],
<a href=#614 id=614 data-nosnippet>614</a>            instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#615 id=615 data-nosnippet>615</a>                program_id_index: <span class="number">2</span>,
<a href=#616 id=616 data-nosnippet>616</a>                accounts: <span class="macro">vec!</span>[],
<a href=#617 id=617 data-nosnippet>617</a>                data: <span class="macro">vec!</span>[],
<a href=#618 id=618 data-nosnippet>618</a>            }],
<a href=#619 id=619 data-nosnippet>619</a>            ..Message::default()
<a href=#620 id=620 data-nosnippet>620</a>        };
<a href=#621 id=621 data-nosnippet>621</a>
<a href=#622 id=622 data-nosnippet>622</a>        <span class="macro">assert!</span>(message.sanitize().is_err());
<a href=#623 id=623 data-nosnippet>623</a>    }
<a href=#624 id=624 data-nosnippet>624</a>
<a href=#625 id=625 data-nosnippet>625</a>    <span class="attr">#[test]
<a href=#626 id=626 data-nosnippet>626</a>    </span><span class="kw">fn </span>test_sanitize_with_invalid_ix_account() {
<a href=#627 id=627 data-nosnippet>627</a>        <span class="macro">assert!</span>(Message {
<a href=#628 id=628 data-nosnippet>628</a>            header: MessageHeader {
<a href=#629 id=629 data-nosnippet>629</a>                num_required_signatures: <span class="number">1</span>,
<a href=#630 id=630 data-nosnippet>630</a>                ..MessageHeader::default()
<a href=#631 id=631 data-nosnippet>631</a>            },
<a href=#632 id=632 data-nosnippet>632</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique(), Pubkey::new_unique()],
<a href=#633 id=633 data-nosnippet>633</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#634 id=634 data-nosnippet>634</a>                account_key: Pubkey::new_unique(),
<a href=#635 id=635 data-nosnippet>635</a>                writable_indexes: <span class="macro">vec!</span>[],
<a href=#636 id=636 data-nosnippet>636</a>                readonly_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#637 id=637 data-nosnippet>637</a>            }],
<a href=#638 id=638 data-nosnippet>638</a>            instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#639 id=639 data-nosnippet>639</a>                program_id_index: <span class="number">1</span>,
<a href=#640 id=640 data-nosnippet>640</a>                accounts: <span class="macro">vec!</span>[<span class="number">3</span>],
<a href=#641 id=641 data-nosnippet>641</a>                data: <span class="macro">vec!</span>[]
<a href=#642 id=642 data-nosnippet>642</a>            }],
<a href=#643 id=643 data-nosnippet>643</a>            ..Message::default()
<a href=#644 id=644 data-nosnippet>644</a>        }
<a href=#645 id=645 data-nosnippet>645</a>        .sanitize()
<a href=#646 id=646 data-nosnippet>646</a>        .is_err());
<a href=#647 id=647 data-nosnippet>647</a>    }
<a href=#648 id=648 data-nosnippet>648</a>
<a href=#649 id=649 data-nosnippet>649</a>    <span class="attr">#[test]
<a href=#650 id=650 data-nosnippet>650</a>    </span><span class="kw">fn </span>test_serialize() {
<a href=#651 id=651 data-nosnippet>651</a>        <span class="kw">let </span>message = Message::default();
<a href=#652 id=652 data-nosnippet>652</a>        <span class="kw">let </span>versioned_msg = VersionedMessage::V0(message.clone());
<a href=#653 id=653 data-nosnippet>653</a>        <span class="macro">assert_eq!</span>(message.serialize(), versioned_msg.serialize());
<a href=#654 id=654 data-nosnippet>654</a>    }
<a href=#655 id=655 data-nosnippet>655</a>
<a href=#656 id=656 data-nosnippet>656</a>    <span class="attr">#[test]
<a href=#657 id=657 data-nosnippet>657</a>    </span><span class="kw">fn </span>test_try_compile() {
<a href=#658 id=658 data-nosnippet>658</a>        <span class="kw">let </span><span class="kw-2">mut </span>keys = <span class="macro">vec!</span>[];
<a href=#659 id=659 data-nosnippet>659</a>        keys.resize_with(<span class="number">7</span>, Pubkey::new_unique);
<a href=#660 id=660 data-nosnippet>660</a>
<a href=#661 id=661 data-nosnippet>661</a>        <span class="kw">let </span>payer = keys[<span class="number">0</span>];
<a href=#662 id=662 data-nosnippet>662</a>        <span class="kw">let </span>program_id = keys[<span class="number">6</span>];
<a href=#663 id=663 data-nosnippet>663</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[Instruction {
<a href=#664 id=664 data-nosnippet>664</a>            program_id,
<a href=#665 id=665 data-nosnippet>665</a>            accounts: <span class="macro">vec!</span>[
<a href=#666 id=666 data-nosnippet>666</a>                AccountMeta::new(keys[<span class="number">1</span>], <span class="bool-val">true</span>),
<a href=#667 id=667 data-nosnippet>667</a>                AccountMeta::new_readonly(keys[<span class="number">2</span>], <span class="bool-val">true</span>),
<a href=#668 id=668 data-nosnippet>668</a>                AccountMeta::new(keys[<span class="number">3</span>], <span class="bool-val">false</span>),
<a href=#669 id=669 data-nosnippet>669</a>                AccountMeta::new(keys[<span class="number">4</span>], <span class="bool-val">false</span>), <span class="comment">// loaded from lut
<a href=#670 id=670 data-nosnippet>670</a>                </span>AccountMeta::new_readonly(keys[<span class="number">5</span>], <span class="bool-val">false</span>), <span class="comment">// loaded from lut
<a href=#671 id=671 data-nosnippet>671</a>            </span>],
<a href=#672 id=672 data-nosnippet>672</a>            data: <span class="macro">vec!</span>[],
<a href=#673 id=673 data-nosnippet>673</a>        }];
<a href=#674 id=674 data-nosnippet>674</a>        <span class="kw">let </span>address_lookup_table_accounts = <span class="macro">vec!</span>[
<a href=#675 id=675 data-nosnippet>675</a>            AddressLookupTableAccount {
<a href=#676 id=676 data-nosnippet>676</a>                key: Pubkey::new_unique(),
<a href=#677 id=677 data-nosnippet>677</a>                addresses: <span class="macro">vec!</span>[keys[<span class="number">4</span>], keys[<span class="number">5</span>], keys[<span class="number">6</span>]],
<a href=#678 id=678 data-nosnippet>678</a>            },
<a href=#679 id=679 data-nosnippet>679</a>            AddressLookupTableAccount {
<a href=#680 id=680 data-nosnippet>680</a>                key: Pubkey::new_unique(),
<a href=#681 id=681 data-nosnippet>681</a>                addresses: <span class="macro">vec!</span>[],
<a href=#682 id=682 data-nosnippet>682</a>            },
<a href=#683 id=683 data-nosnippet>683</a>        ];
<a href=#684 id=684 data-nosnippet>684</a>
<a href=#685 id=685 data-nosnippet>685</a>        <span class="kw">let </span>recent_blockhash = Hash::new_unique();
<a href=#686 id=686 data-nosnippet>686</a>        <span class="macro">assert_eq!</span>(
<a href=#687 id=687 data-nosnippet>687</a>            Message::try_compile(
<a href=#688 id=688 data-nosnippet>688</a>                <span class="kw-2">&amp;</span>payer,
<a href=#689 id=689 data-nosnippet>689</a>                <span class="kw-2">&amp;</span>instructions,
<a href=#690 id=690 data-nosnippet>690</a>                <span class="kw-2">&amp;</span>address_lookup_table_accounts,
<a href=#691 id=691 data-nosnippet>691</a>                recent_blockhash
<a href=#692 id=692 data-nosnippet>692</a>            ),
<a href=#693 id=693 data-nosnippet>693</a>            <span class="prelude-val">Ok</span>(Message {
<a href=#694 id=694 data-nosnippet>694</a>                header: MessageHeader {
<a href=#695 id=695 data-nosnippet>695</a>                    num_required_signatures: <span class="number">3</span>,
<a href=#696 id=696 data-nosnippet>696</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#697 id=697 data-nosnippet>697</a>                    num_readonly_unsigned_accounts: <span class="number">1
<a href=#698 id=698 data-nosnippet>698</a>                </span>},
<a href=#699 id=699 data-nosnippet>699</a>                recent_blockhash,
<a href=#700 id=700 data-nosnippet>700</a>                account_keys: <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>], keys[<span class="number">3</span>], program_id],
<a href=#701 id=701 data-nosnippet>701</a>                instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#702 id=702 data-nosnippet>702</a>                    program_id_index: <span class="number">4</span>,
<a href=#703 id=703 data-nosnippet>703</a>                    accounts: <span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">6</span>],
<a href=#704 id=704 data-nosnippet>704</a>                    data: <span class="macro">vec!</span>[],
<a href=#705 id=705 data-nosnippet>705</a>                },],
<a href=#706 id=706 data-nosnippet>706</a>                address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#707 id=707 data-nosnippet>707</a>                    account_key: address_lookup_table_accounts[<span class="number">0</span>].key,
<a href=#708 id=708 data-nosnippet>708</a>                    writable_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#709 id=709 data-nosnippet>709</a>                    readonly_indexes: <span class="macro">vec!</span>[<span class="number">1</span>],
<a href=#710 id=710 data-nosnippet>710</a>                }],
<a href=#711 id=711 data-nosnippet>711</a>            })
<a href=#712 id=712 data-nosnippet>712</a>        );
<a href=#713 id=713 data-nosnippet>713</a>    }
<a href=#714 id=714 data-nosnippet>714</a>
<a href=#715 id=715 data-nosnippet>715</a>    <span class="attr">#[test]
<a href=#716 id=716 data-nosnippet>716</a>    </span><span class="kw">fn </span>test_is_maybe_writable() {
<a href=#717 id=717 data-nosnippet>717</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#718 id=718 data-nosnippet>718</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#719 id=719 data-nosnippet>719</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#720 id=720 data-nosnippet>720</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#721 id=721 data-nosnippet>721</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#722 id=722 data-nosnippet>722</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#723 id=723 data-nosnippet>723</a>
<a href=#724 id=724 data-nosnippet>724</a>        <span class="kw">let </span>message = Message {
<a href=#725 id=725 data-nosnippet>725</a>            header: MessageHeader {
<a href=#726 id=726 data-nosnippet>726</a>                num_required_signatures: <span class="number">3</span>,
<a href=#727 id=727 data-nosnippet>727</a>                num_readonly_signed_accounts: <span class="number">2</span>,
<a href=#728 id=728 data-nosnippet>728</a>                num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#729 id=729 data-nosnippet>729</a>            },
<a href=#730 id=730 data-nosnippet>730</a>            account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3, key4, key5],
<a href=#731 id=731 data-nosnippet>731</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#732 id=732 data-nosnippet>732</a>                account_key: Pubkey::new_unique(),
<a href=#733 id=733 data-nosnippet>733</a>                writable_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#734 id=734 data-nosnippet>734</a>                readonly_indexes: <span class="macro">vec!</span>[<span class="number">1</span>],
<a href=#735 id=735 data-nosnippet>735</a>            }],
<a href=#736 id=736 data-nosnippet>736</a>            ..Message::default()
<a href=#737 id=737 data-nosnippet>737</a>        };
<a href=#738 id=738 data-nosnippet>738</a>
<a href=#739 id=739 data-nosnippet>739</a>        <span class="kw">let </span>reserved_account_keys = HashSet::from([key3]);
<a href=#740 id=740 data-nosnippet>740</a>
<a href=#741 id=741 data-nosnippet>741</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">0</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#742 id=742 data-nosnippet>742</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">1</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#743 id=743 data-nosnippet>743</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">2</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#744 id=744 data-nosnippet>744</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">3</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#745 id=745 data-nosnippet>745</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">3</span>, <span class="prelude-val">None</span>));
<a href=#746 id=746 data-nosnippet>746</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">4</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#747 id=747 data-nosnippet>747</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">5</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#748 id=748 data-nosnippet>748</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">6</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#749 id=749 data-nosnippet>749</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">7</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#750 id=750 data-nosnippet>750</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">8</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#751 id=751 data-nosnippet>751</a>    }
<a href=#752 id=752 data-nosnippet>752</a>
<a href=#753 id=753 data-nosnippet>753</a>    <span class="attr">#[test]
<a href=#754 id=754 data-nosnippet>754</a>    </span><span class="kw">fn </span>test_is_account_maybe_reserved() {
<a href=#755 id=755 data-nosnippet>755</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#756 id=756 data-nosnippet>756</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#757 id=757 data-nosnippet>757</a>
<a href=#758 id=758 data-nosnippet>758</a>        <span class="kw">let </span>message = Message {
<a href=#759 id=759 data-nosnippet>759</a>            account_keys: <span class="macro">vec!</span>[key0, key1],
<a href=#760 id=760 data-nosnippet>760</a>            address_table_lookups: <span class="macro">vec!</span>[MessageAddressTableLookup {
<a href=#761 id=761 data-nosnippet>761</a>                account_key: Pubkey::new_unique(),
<a href=#762 id=762 data-nosnippet>762</a>                writable_indexes: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#763 id=763 data-nosnippet>763</a>                readonly_indexes: <span class="macro">vec!</span>[<span class="number">1</span>],
<a href=#764 id=764 data-nosnippet>764</a>            }],
<a href=#765 id=765 data-nosnippet>765</a>            ..Message::default()
<a href=#766 id=766 data-nosnippet>766</a>        };
<a href=#767 id=767 data-nosnippet>767</a>
<a href=#768 id=768 data-nosnippet>768</a>        <span class="kw">let </span>reserved_account_keys = HashSet::from([key1]);
<a href=#769 id=769 data-nosnippet>769</a>
<a href=#770 id=770 data-nosnippet>770</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">0</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#771 id=771 data-nosnippet>771</a>        <span class="macro">assert!</span>(message.is_account_maybe_reserved(<span class="number">1</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#772 id=772 data-nosnippet>772</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">2</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#773 id=773 data-nosnippet>773</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">3</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#774 id=774 data-nosnippet>774</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">4</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#775 id=775 data-nosnippet>775</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">0</span>, <span class="prelude-val">None</span>));
<a href=#776 id=776 data-nosnippet>776</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">1</span>, <span class="prelude-val">None</span>));
<a href=#777 id=777 data-nosnippet>777</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">2</span>, <span class="prelude-val">None</span>));
<a href=#778 id=778 data-nosnippet>778</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">3</span>, <span class="prelude-val">None</span>));
<a href=#779 id=779 data-nosnippet>779</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">4</span>, <span class="prelude-val">None</span>));
<a href=#780 id=780 data-nosnippet>780</a>    }
<a href=#781 id=781 data-nosnippet>781</a>}</code></pre></div></section></main></body></html>