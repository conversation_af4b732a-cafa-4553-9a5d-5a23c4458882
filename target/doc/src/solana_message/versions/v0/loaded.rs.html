<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/versions/v0/loaded.rs`."><title>loaded.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../../../" data-static-root-path="../../../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../../static.files/storage-4e99c027.js"></script><script defer src="../../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../../src-files.js"></script><script defer src="../../../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/versions/v0/</div>loaded.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#2 id=2 data-nosnippet>2</a></span><span class="kw">use </span>serde_derive::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>{
<a href=#4 id=4 data-nosnippet>4</a>    <span class="kw">crate</span>::{v0, AccountKeys},
<a href=#5 id=5 data-nosnippet>5</a>    solana_pubkey::Pubkey,
<a href=#6 id=6 data-nosnippet>6</a>    solana_sdk_ids::bpf_loader_upgradeable,
<a href=#7 id=7 data-nosnippet>7</a>    std::{borrow::Cow, collections::HashSet},
<a href=#8 id=8 data-nosnippet>8</a>};
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a><span class="doccomment">/// Combination of a version #0 message and its loaded addresses
<a href=#11 id=11 data-nosnippet>11</a></span><span class="attr">#[derive(Debug, Clone, Eq, PartialEq)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>LoadedMessage&lt;<span class="lifetime">'a</span>&gt; {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="doccomment">/// Message which loaded a collection of lookup table addresses
<a href=#14 id=14 data-nosnippet>14</a>    </span><span class="kw">pub </span>message: Cow&lt;<span class="lifetime">'a</span>, v0::Message&gt;,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="doccomment">/// Addresses loaded with on-chain address lookup tables
<a href=#16 id=16 data-nosnippet>16</a>    </span><span class="kw">pub </span>loaded_addresses: Cow&lt;<span class="lifetime">'a</span>, LoadedAddresses&gt;,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="doccomment">/// List of boolean with same length as account_keys(), each boolean value indicates if
<a href=#18 id=18 data-nosnippet>18</a>    /// corresponding account key is writable or not.
<a href=#19 id=19 data-nosnippet>19</a>    </span><span class="kw">pub </span>is_writable_account_cache: Vec&lt;bool&gt;,
<a href=#20 id=20 data-nosnippet>20</a>}
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="doccomment">/// Collection of addresses loaded from on-chain lookup tables, split
<a href=#23 id=23 data-nosnippet>23</a>/// by readonly and writable.
<a href=#24 id=24 data-nosnippet>24</a></span><span class="attr">#[derive(Clone, Default, Debug, PartialEq, Eq)]
<a href=#25 id=25 data-nosnippet>25</a>#[cfg_attr(feature = <span class="string">"serde"</span>, derive(Deserialize, Serialize))]
<a href=#26 id=26 data-nosnippet>26</a></span><span class="kw">pub struct </span>LoadedAddresses {
<a href=#27 id=27 data-nosnippet>27</a>    <span class="doccomment">/// List of addresses for writable loaded accounts
<a href=#28 id=28 data-nosnippet>28</a>    </span><span class="kw">pub </span>writable: Vec&lt;Pubkey&gt;,
<a href=#29 id=29 data-nosnippet>29</a>    <span class="doccomment">/// List of addresses for read-only loaded accounts
<a href=#30 id=30 data-nosnippet>30</a>    </span><span class="kw">pub </span>readonly: Vec&lt;Pubkey&gt;,
<a href=#31 id=31 data-nosnippet>31</a>}
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a><span class="kw">impl </span>FromIterator&lt;LoadedAddresses&gt; <span class="kw">for </span>LoadedAddresses {
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">fn </span>from_iter&lt;T: IntoIterator&lt;Item = LoadedAddresses&gt;&gt;(iter: T) -&gt; <span class="self">Self </span>{
<a href=#35 id=35 data-nosnippet>35</a>        <span class="kw">let </span>(writable, readonly): (Vec&lt;Vec&lt;Pubkey&gt;&gt;, Vec&lt;Vec&lt;Pubkey&gt;&gt;) = iter
<a href=#36 id=36 data-nosnippet>36</a>            .into_iter()
<a href=#37 id=37 data-nosnippet>37</a>            .map(|addresses| (addresses.writable, addresses.readonly))
<a href=#38 id=38 data-nosnippet>38</a>            .unzip();
<a href=#39 id=39 data-nosnippet>39</a>        LoadedAddresses {
<a href=#40 id=40 data-nosnippet>40</a>            writable: writable.into_iter().flatten().collect(),
<a href=#41 id=41 data-nosnippet>41</a>            readonly: readonly.into_iter().flatten().collect(),
<a href=#42 id=42 data-nosnippet>42</a>        }
<a href=#43 id=43 data-nosnippet>43</a>    }
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="kw">impl </span>LoadedAddresses {
<a href=#47 id=47 data-nosnippet>47</a>    <span class="doccomment">/// Checks if there are no writable or readonly addresses
<a href=#48 id=48 data-nosnippet>48</a>    </span><span class="kw">pub fn </span>is_empty(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#49 id=49 data-nosnippet>49</a>        <span class="self">self</span>.len() == <span class="number">0
<a href=#50 id=50 data-nosnippet>50</a>    </span>}
<a href=#51 id=51 data-nosnippet>51</a>
<a href=#52 id=52 data-nosnippet>52</a>    <span class="doccomment">/// Combined length of loaded writable and readonly addresses
<a href=#53 id=53 data-nosnippet>53</a>    </span><span class="kw">pub fn </span>len(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; usize {
<a href=#54 id=54 data-nosnippet>54</a>        <span class="self">self</span>.writable.len().saturating_add(<span class="self">self</span>.readonly.len())
<a href=#55 id=55 data-nosnippet>55</a>    }
<a href=#56 id=56 data-nosnippet>56</a>}
<a href=#57 id=57 data-nosnippet>57</a>
<a href=#58 id=58 data-nosnippet>58</a><span class="kw">impl</span>&lt;<span class="lifetime">'a</span>&gt; LoadedMessage&lt;<span class="lifetime">'a</span>&gt; {
<a href=#59 id=59 data-nosnippet>59</a>    <span class="kw">pub fn </span>new(
<a href=#60 id=60 data-nosnippet>60</a>        message: v0::Message,
<a href=#61 id=61 data-nosnippet>61</a>        loaded_addresses: LoadedAddresses,
<a href=#62 id=62 data-nosnippet>62</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#63 id=63 data-nosnippet>63</a>    ) -&gt; <span class="self">Self </span>{
<a href=#64 id=64 data-nosnippet>64</a>        <span class="kw">let </span><span class="kw-2">mut </span>loaded_message = <span class="self">Self </span>{
<a href=#65 id=65 data-nosnippet>65</a>            message: Cow::Owned(message),
<a href=#66 id=66 data-nosnippet>66</a>            loaded_addresses: Cow::Owned(loaded_addresses),
<a href=#67 id=67 data-nosnippet>67</a>            is_writable_account_cache: Vec::default(),
<a href=#68 id=68 data-nosnippet>68</a>        };
<a href=#69 id=69 data-nosnippet>69</a>        loaded_message.set_is_writable_account_cache(reserved_account_keys);
<a href=#70 id=70 data-nosnippet>70</a>        loaded_message
<a href=#71 id=71 data-nosnippet>71</a>    }
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">pub fn </span>new_borrowed(
<a href=#74 id=74 data-nosnippet>74</a>        message: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>v0::Message,
<a href=#75 id=75 data-nosnippet>75</a>        loaded_addresses: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>LoadedAddresses,
<a href=#76 id=76 data-nosnippet>76</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#77 id=77 data-nosnippet>77</a>    ) -&gt; <span class="self">Self </span>{
<a href=#78 id=78 data-nosnippet>78</a>        <span class="kw">let </span><span class="kw-2">mut </span>loaded_message = <span class="self">Self </span>{
<a href=#79 id=79 data-nosnippet>79</a>            message: Cow::Borrowed(message),
<a href=#80 id=80 data-nosnippet>80</a>            loaded_addresses: Cow::Borrowed(loaded_addresses),
<a href=#81 id=81 data-nosnippet>81</a>            is_writable_account_cache: Vec::default(),
<a href=#82 id=82 data-nosnippet>82</a>        };
<a href=#83 id=83 data-nosnippet>83</a>        loaded_message.set_is_writable_account_cache(reserved_account_keys);
<a href=#84 id=84 data-nosnippet>84</a>        loaded_message
<a href=#85 id=85 data-nosnippet>85</a>    }
<a href=#86 id=86 data-nosnippet>86</a>
<a href=#87 id=87 data-nosnippet>87</a>    <span class="kw">fn </span>set_is_writable_account_cache(<span class="kw-2">&amp;mut </span><span class="self">self</span>, reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;) {
<a href=#88 id=88 data-nosnippet>88</a>        <span class="kw">let </span>is_writable_account_cache = <span class="self">self
<a href=#89 id=89 data-nosnippet>89</a>            </span>.account_keys()
<a href=#90 id=90 data-nosnippet>90</a>            .iter()
<a href=#91 id=91 data-nosnippet>91</a>            .enumerate()
<a href=#92 id=92 data-nosnippet>92</a>            .map(|(i, _key)| <span class="self">self</span>.is_writable_internal(i, reserved_account_keys))
<a href=#93 id=93 data-nosnippet>93</a>            .collect::&lt;Vec&lt;<span class="kw">_</span>&gt;&gt;();
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">let _ </span>= std::mem::replace(
<a href=#95 id=95 data-nosnippet>95</a>            <span class="kw-2">&amp;mut </span><span class="self">self</span>.is_writable_account_cache,
<a href=#96 id=96 data-nosnippet>96</a>            is_writable_account_cache,
<a href=#97 id=97 data-nosnippet>97</a>        );
<a href=#98 id=98 data-nosnippet>98</a>    }
<a href=#99 id=99 data-nosnippet>99</a>
<a href=#100 id=100 data-nosnippet>100</a>    <span class="doccomment">/// Returns the full list of static and dynamic account keys that are loaded for this message.
<a href=#101 id=101 data-nosnippet>101</a>    </span><span class="kw">pub fn </span>account_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; AccountKeys {
<a href=#102 id=102 data-nosnippet>102</a>        AccountKeys::new(<span class="kw-2">&amp;</span><span class="self">self</span>.message.account_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span><span class="self">self</span>.loaded_addresses))
<a href=#103 id=103 data-nosnippet>103</a>    }
<a href=#104 id=104 data-nosnippet>104</a>
<a href=#105 id=105 data-nosnippet>105</a>    <span class="doccomment">/// Returns the list of static account keys that are loaded for this message.
<a href=#106 id=106 data-nosnippet>106</a>    </span><span class="kw">pub fn </span>static_account_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[Pubkey] {
<a href=#107 id=107 data-nosnippet>107</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.message.account_keys
<a href=#108 id=108 data-nosnippet>108</a>    }
<a href=#109 id=109 data-nosnippet>109</a>
<a href=#110 id=110 data-nosnippet>110</a>    <span class="doccomment">/// Returns true if any account keys are duplicates
<a href=#111 id=111 data-nosnippet>111</a>    </span><span class="kw">pub fn </span>has_duplicates(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#112 id=112 data-nosnippet>112</a>        <span class="kw">let </span><span class="kw-2">mut </span>uniq = HashSet::new();
<a href=#113 id=113 data-nosnippet>113</a>        <span class="self">self</span>.account_keys().iter().any(|x| !uniq.insert(x))
<a href=#114 id=114 data-nosnippet>114</a>    }
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>    <span class="doccomment">/// Returns true if the account at the specified index was requested to be
<a href=#117 id=117 data-nosnippet>117</a>    /// writable.  This method should not be used directly.
<a href=#118 id=118 data-nosnippet>118</a>    </span><span class="kw">fn </span>is_writable_index(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#119 id=119 data-nosnippet>119</a>        <span class="kw">let </span>header = <span class="kw-2">&amp;</span><span class="self">self</span>.message.header;
<a href=#120 id=120 data-nosnippet>120</a>        <span class="kw">let </span>num_account_keys = <span class="self">self</span>.message.account_keys.len();
<a href=#121 id=121 data-nosnippet>121</a>        <span class="kw">let </span>num_signed_accounts = usize::from(header.num_required_signatures);
<a href=#122 id=122 data-nosnippet>122</a>        <span class="kw">if </span>key_index &gt;= num_account_keys {
<a href=#123 id=123 data-nosnippet>123</a>            <span class="kw">let </span>loaded_addresses_index = key_index.saturating_sub(num_account_keys);
<a href=#124 id=124 data-nosnippet>124</a>            loaded_addresses_index &lt; <span class="self">self</span>.loaded_addresses.writable.len()
<a href=#125 id=125 data-nosnippet>125</a>        } <span class="kw">else if </span>key_index &gt;= num_signed_accounts {
<a href=#126 id=126 data-nosnippet>126</a>            <span class="kw">let </span>num_unsigned_accounts = num_account_keys.saturating_sub(num_signed_accounts);
<a href=#127 id=127 data-nosnippet>127</a>            <span class="kw">let </span>num_writable_unsigned_accounts = num_unsigned_accounts
<a href=#128 id=128 data-nosnippet>128</a>                .saturating_sub(usize::from(header.num_readonly_unsigned_accounts));
<a href=#129 id=129 data-nosnippet>129</a>            <span class="kw">let </span>unsigned_account_index = key_index.saturating_sub(num_signed_accounts);
<a href=#130 id=130 data-nosnippet>130</a>            unsigned_account_index &lt; num_writable_unsigned_accounts
<a href=#131 id=131 data-nosnippet>131</a>        } <span class="kw">else </span>{
<a href=#132 id=132 data-nosnippet>132</a>            <span class="kw">let </span>num_writable_signed_accounts = num_signed_accounts
<a href=#133 id=133 data-nosnippet>133</a>                .saturating_sub(usize::from(header.num_readonly_signed_accounts));
<a href=#134 id=134 data-nosnippet>134</a>            key_index &lt; num_writable_signed_accounts
<a href=#135 id=135 data-nosnippet>135</a>        }
<a href=#136 id=136 data-nosnippet>136</a>    }
<a href=#137 id=137 data-nosnippet>137</a>
<a href=#138 id=138 data-nosnippet>138</a>    <span class="doccomment">/// Returns true if the account at the specified index was loaded as writable
<a href=#139 id=139 data-nosnippet>139</a>    </span><span class="kw">fn </span>is_writable_internal(
<a href=#140 id=140 data-nosnippet>140</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#141 id=141 data-nosnippet>141</a>        key_index: usize,
<a href=#142 id=142 data-nosnippet>142</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#143 id=143 data-nosnippet>143</a>    ) -&gt; bool {
<a href=#144 id=144 data-nosnippet>144</a>        <span class="kw">if </span><span class="self">self</span>.is_writable_index(key_index) {
<a href=#145 id=145 data-nosnippet>145</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(key) = <span class="self">self</span>.account_keys().get(key_index) {
<a href=#146 id=146 data-nosnippet>146</a>                <span class="kw">return </span>!(reserved_account_keys.contains(key) || <span class="self">self</span>.demote_program_id(key_index));
<a href=#147 id=147 data-nosnippet>147</a>            }
<a href=#148 id=148 data-nosnippet>148</a>        }
<a href=#149 id=149 data-nosnippet>149</a>        <span class="bool-val">false
<a href=#150 id=150 data-nosnippet>150</a>    </span>}
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>    <span class="kw">pub fn </span>is_writable(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#153 id=153 data-nosnippet>153</a>        <span class="kw-2">*</span><span class="self">self
<a href=#154 id=154 data-nosnippet>154</a>            </span>.is_writable_account_cache
<a href=#155 id=155 data-nosnippet>155</a>            .get(key_index)
<a href=#156 id=156 data-nosnippet>156</a>            .unwrap_or(<span class="kw-2">&amp;</span><span class="bool-val">false</span>)
<a href=#157 id=157 data-nosnippet>157</a>    }
<a href=#158 id=158 data-nosnippet>158</a>
<a href=#159 id=159 data-nosnippet>159</a>    <span class="kw">pub fn </span>is_signer(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#160 id=160 data-nosnippet>160</a>        i &lt; <span class="self">self</span>.message.header.num_required_signatures <span class="kw">as </span>usize
<a href=#161 id=161 data-nosnippet>161</a>    }
<a href=#162 id=162 data-nosnippet>162</a>
<a href=#163 id=163 data-nosnippet>163</a>    <span class="kw">pub fn </span>demote_program_id(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#164 id=164 data-nosnippet>164</a>        <span class="self">self</span>.is_key_called_as_program(i) &amp;&amp; !<span class="self">self</span>.is_upgradeable_loader_present()
<a href=#165 id=165 data-nosnippet>165</a>    }
<a href=#166 id=166 data-nosnippet>166</a>
<a href=#167 id=167 data-nosnippet>167</a>    <span class="doccomment">/// Returns true if the account at the specified index is called as a program by an instruction
<a href=#168 id=168 data-nosnippet>168</a>    </span><span class="kw">pub fn </span>is_key_called_as_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#169 id=169 data-nosnippet>169</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(key_index) = u8::try_from(key_index) {
<a href=#170 id=170 data-nosnippet>170</a>            <span class="self">self</span>.message
<a href=#171 id=171 data-nosnippet>171</a>                .instructions
<a href=#172 id=172 data-nosnippet>172</a>                .iter()
<a href=#173 id=173 data-nosnippet>173</a>                .any(|ix| ix.program_id_index == key_index)
<a href=#174 id=174 data-nosnippet>174</a>        } <span class="kw">else </span>{
<a href=#175 id=175 data-nosnippet>175</a>            <span class="bool-val">false
<a href=#176 id=176 data-nosnippet>176</a>        </span>}
<a href=#177 id=177 data-nosnippet>177</a>    }
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a>    <span class="doccomment">/// Returns true if any account is the bpf upgradeable loader
<a href=#180 id=180 data-nosnippet>180</a>    </span><span class="kw">pub fn </span>is_upgradeable_loader_present(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#181 id=181 data-nosnippet>181</a>        <span class="self">self</span>.account_keys()
<a href=#182 id=182 data-nosnippet>182</a>            .iter()
<a href=#183 id=183 data-nosnippet>183</a>            .any(|<span class="kw-2">&amp;</span>key| key == bpf_loader_upgradeable::id())
<a href=#184 id=184 data-nosnippet>184</a>    }
<a href=#185 id=185 data-nosnippet>185</a>}
<a href=#186 id=186 data-nosnippet>186</a>
<a href=#187 id=187 data-nosnippet>187</a><span class="attr">#[cfg(test)]
<a href=#188 id=188 data-nosnippet>188</a></span><span class="kw">mod </span>tests {
<a href=#189 id=189 data-nosnippet>189</a>    <span class="kw">use </span>{
<a href=#190 id=190 data-nosnippet>190</a>        <span class="kw">super</span>::<span class="kw-2">*</span>,
<a href=#191 id=191 data-nosnippet>191</a>        <span class="kw">crate</span>::{compiled_instruction::CompiledInstruction, MessageHeader},
<a href=#192 id=192 data-nosnippet>192</a>        itertools::Itertools,
<a href=#193 id=193 data-nosnippet>193</a>        solana_sdk_ids::{system_program, sysvar},
<a href=#194 id=194 data-nosnippet>194</a>    };
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>    <span class="kw">fn </span>check_test_loaded_message() -&gt; (LoadedMessage&lt;<span class="lifetime">'static</span>&gt;, [Pubkey; <span class="number">6</span>]) {
<a href=#197 id=197 data-nosnippet>197</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#198 id=198 data-nosnippet>198</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#199 id=199 data-nosnippet>199</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#200 id=200 data-nosnippet>200</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#202 id=202 data-nosnippet>202</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#203 id=203 data-nosnippet>203</a>
<a href=#204 id=204 data-nosnippet>204</a>        <span class="kw">let </span>message = LoadedMessage::new(
<a href=#205 id=205 data-nosnippet>205</a>            v0::Message {
<a href=#206 id=206 data-nosnippet>206</a>                header: MessageHeader {
<a href=#207 id=207 data-nosnippet>207</a>                    num_required_signatures: <span class="number">2</span>,
<a href=#208 id=208 data-nosnippet>208</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#209 id=209 data-nosnippet>209</a>                    num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#210 id=210 data-nosnippet>210</a>                },
<a href=#211 id=211 data-nosnippet>211</a>                account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3],
<a href=#212 id=212 data-nosnippet>212</a>                ..v0::Message::default()
<a href=#213 id=213 data-nosnippet>213</a>            },
<a href=#214 id=214 data-nosnippet>214</a>            LoadedAddresses {
<a href=#215 id=215 data-nosnippet>215</a>                writable: <span class="macro">vec!</span>[key4],
<a href=#216 id=216 data-nosnippet>216</a>                readonly: <span class="macro">vec!</span>[key5],
<a href=#217 id=217 data-nosnippet>217</a>            },
<a href=#218 id=218 data-nosnippet>218</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#219 id=219 data-nosnippet>219</a>        );
<a href=#220 id=220 data-nosnippet>220</a>
<a href=#221 id=221 data-nosnippet>221</a>        (message, [key0, key1, key2, key3, key4, key5])
<a href=#222 id=222 data-nosnippet>222</a>    }
<a href=#223 id=223 data-nosnippet>223</a>
<a href=#224 id=224 data-nosnippet>224</a>    <span class="attr">#[test]
<a href=#225 id=225 data-nosnippet>225</a>    </span><span class="kw">fn </span>test_has_duplicates() {
<a href=#226 id=226 data-nosnippet>226</a>        <span class="kw">let </span>message = check_test_loaded_message().<span class="number">0</span>;
<a href=#227 id=227 data-nosnippet>227</a>
<a href=#228 id=228 data-nosnippet>228</a>        <span class="macro">assert!</span>(!message.has_duplicates());
<a href=#229 id=229 data-nosnippet>229</a>    }
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>    <span class="attr">#[test]
<a href=#232 id=232 data-nosnippet>232</a>    </span><span class="kw">fn </span>test_has_duplicates_with_dupe_keys() {
<a href=#233 id=233 data-nosnippet>233</a>        <span class="kw">let </span>create_message_with_dupe_keys = |<span class="kw-2">mut </span>keys: Vec&lt;Pubkey&gt;| {
<a href=#234 id=234 data-nosnippet>234</a>            LoadedMessage::new(
<a href=#235 id=235 data-nosnippet>235</a>                v0::Message {
<a href=#236 id=236 data-nosnippet>236</a>                    account_keys: keys.split_off(<span class="number">2</span>),
<a href=#237 id=237 data-nosnippet>237</a>                    ..v0::Message::default()
<a href=#238 id=238 data-nosnippet>238</a>                },
<a href=#239 id=239 data-nosnippet>239</a>                LoadedAddresses {
<a href=#240 id=240 data-nosnippet>240</a>                    writable: keys.split_off(<span class="number">2</span>),
<a href=#241 id=241 data-nosnippet>241</a>                    readonly: keys,
<a href=#242 id=242 data-nosnippet>242</a>                },
<a href=#243 id=243 data-nosnippet>243</a>                <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#244 id=244 data-nosnippet>244</a>            )
<a href=#245 id=245 data-nosnippet>245</a>        };
<a href=#246 id=246 data-nosnippet>246</a>
<a href=#247 id=247 data-nosnippet>247</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#248 id=248 data-nosnippet>248</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#249 id=249 data-nosnippet>249</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#250 id=250 data-nosnippet>250</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#251 id=251 data-nosnippet>251</a>        <span class="kw">let </span>dupe_key = Pubkey::new_unique();
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>        <span class="kw">let </span>keys = <span class="macro">vec!</span>[key0, key1, key2, key3, dupe_key, dupe_key];
<a href=#254 id=254 data-nosnippet>254</a>        <span class="kw">let </span>keys_len = keys.len();
<a href=#255 id=255 data-nosnippet>255</a>        <span class="kw">for </span>keys <span class="kw">in </span>keys.into_iter().permutations(keys_len).unique() {
<a href=#256 id=256 data-nosnippet>256</a>            <span class="kw">let </span>message = create_message_with_dupe_keys(keys);
<a href=#257 id=257 data-nosnippet>257</a>            <span class="macro">assert!</span>(message.has_duplicates());
<a href=#258 id=258 data-nosnippet>258</a>        }
<a href=#259 id=259 data-nosnippet>259</a>    }
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>    <span class="attr">#[test]
<a href=#262 id=262 data-nosnippet>262</a>    </span><span class="kw">fn </span>test_is_writable_index() {
<a href=#263 id=263 data-nosnippet>263</a>        <span class="kw">let </span>message = check_test_loaded_message().<span class="number">0</span>;
<a href=#264 id=264 data-nosnippet>264</a>
<a href=#265 id=265 data-nosnippet>265</a>        <span class="macro">assert!</span>(message.is_writable_index(<span class="number">0</span>));
<a href=#266 id=266 data-nosnippet>266</a>        <span class="macro">assert!</span>(!message.is_writable_index(<span class="number">1</span>));
<a href=#267 id=267 data-nosnippet>267</a>        <span class="macro">assert!</span>(message.is_writable_index(<span class="number">2</span>));
<a href=#268 id=268 data-nosnippet>268</a>        <span class="macro">assert!</span>(!message.is_writable_index(<span class="number">3</span>));
<a href=#269 id=269 data-nosnippet>269</a>        <span class="macro">assert!</span>(message.is_writable_index(<span class="number">4</span>));
<a href=#270 id=270 data-nosnippet>270</a>        <span class="macro">assert!</span>(!message.is_writable_index(<span class="number">5</span>));
<a href=#271 id=271 data-nosnippet>271</a>    }
<a href=#272 id=272 data-nosnippet>272</a>
<a href=#273 id=273 data-nosnippet>273</a>    <span class="attr">#[test]
<a href=#274 id=274 data-nosnippet>274</a>    </span><span class="kw">fn </span>test_is_writable() {
<a href=#275 id=275 data-nosnippet>275</a>        <span class="kw">let </span>reserved_account_keys = HashSet::from_iter([sysvar::clock::id(), system_program::id()]);
<a href=#276 id=276 data-nosnippet>276</a>        <span class="kw">let </span>create_message_with_keys = |keys: Vec&lt;Pubkey&gt;| {
<a href=#277 id=277 data-nosnippet>277</a>            LoadedMessage::new(
<a href=#278 id=278 data-nosnippet>278</a>                v0::Message {
<a href=#279 id=279 data-nosnippet>279</a>                    header: MessageHeader {
<a href=#280 id=280 data-nosnippet>280</a>                        num_required_signatures: <span class="number">1</span>,
<a href=#281 id=281 data-nosnippet>281</a>                        num_readonly_signed_accounts: <span class="number">0</span>,
<a href=#282 id=282 data-nosnippet>282</a>                        num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#283 id=283 data-nosnippet>283</a>                    },
<a href=#284 id=284 data-nosnippet>284</a>                    account_keys: keys[..<span class="number">2</span>].to_vec(),
<a href=#285 id=285 data-nosnippet>285</a>                    ..v0::Message::default()
<a href=#286 id=286 data-nosnippet>286</a>                },
<a href=#287 id=287 data-nosnippet>287</a>                LoadedAddresses {
<a href=#288 id=288 data-nosnippet>288</a>                    writable: keys[<span class="number">2</span>..=<span class="number">2</span>].to_vec(),
<a href=#289 id=289 data-nosnippet>289</a>                    readonly: keys[<span class="number">3</span>..].to_vec(),
<a href=#290 id=290 data-nosnippet>290</a>                },
<a href=#291 id=291 data-nosnippet>291</a>                <span class="kw-2">&amp;</span>reserved_account_keys,
<a href=#292 id=292 data-nosnippet>292</a>            )
<a href=#293 id=293 data-nosnippet>293</a>        };
<a href=#294 id=294 data-nosnippet>294</a>
<a href=#295 id=295 data-nosnippet>295</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#296 id=296 data-nosnippet>296</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#297 id=297 data-nosnippet>297</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#298 id=298 data-nosnippet>298</a>        {
<a href=#299 id=299 data-nosnippet>299</a>            <span class="kw">let </span>message = create_message_with_keys(<span class="macro">vec!</span>[sysvar::clock::id(), key0, key1, key2]);
<a href=#300 id=300 data-nosnippet>300</a>            <span class="macro">assert!</span>(message.is_writable_index(<span class="number">0</span>));
<a href=#301 id=301 data-nosnippet>301</a>            <span class="macro">assert!</span>(!message.is_writable(<span class="number">0</span>));
<a href=#302 id=302 data-nosnippet>302</a>        }
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>        {
<a href=#305 id=305 data-nosnippet>305</a>            <span class="kw">let </span>message = create_message_with_keys(<span class="macro">vec!</span>[system_program::id(), key0, key1, key2]);
<a href=#306 id=306 data-nosnippet>306</a>            <span class="macro">assert!</span>(message.is_writable_index(<span class="number">0</span>));
<a href=#307 id=307 data-nosnippet>307</a>            <span class="macro">assert!</span>(!message.is_writable(<span class="number">0</span>));
<a href=#308 id=308 data-nosnippet>308</a>        }
<a href=#309 id=309 data-nosnippet>309</a>
<a href=#310 id=310 data-nosnippet>310</a>        {
<a href=#311 id=311 data-nosnippet>311</a>            <span class="kw">let </span>message = create_message_with_keys(<span class="macro">vec!</span>[key0, key1, system_program::id(), key2]);
<a href=#312 id=312 data-nosnippet>312</a>            <span class="macro">assert!</span>(message.is_writable_index(<span class="number">2</span>));
<a href=#313 id=313 data-nosnippet>313</a>            <span class="macro">assert!</span>(!message.is_writable(<span class="number">2</span>));
<a href=#314 id=314 data-nosnippet>314</a>        }
<a href=#315 id=315 data-nosnippet>315</a>    }
<a href=#316 id=316 data-nosnippet>316</a>
<a href=#317 id=317 data-nosnippet>317</a>    <span class="attr">#[test]
<a href=#318 id=318 data-nosnippet>318</a>    </span><span class="kw">fn </span>test_demote_writable_program() {
<a href=#319 id=319 data-nosnippet>319</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#320 id=320 data-nosnippet>320</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#321 id=321 data-nosnippet>321</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#322 id=322 data-nosnippet>322</a>        <span class="kw">let </span>message = LoadedMessage::new(
<a href=#323 id=323 data-nosnippet>323</a>            v0::Message {
<a href=#324 id=324 data-nosnippet>324</a>                header: MessageHeader {
<a href=#325 id=325 data-nosnippet>325</a>                    num_required_signatures: <span class="number">1</span>,
<a href=#326 id=326 data-nosnippet>326</a>                    num_readonly_signed_accounts: <span class="number">0</span>,
<a href=#327 id=327 data-nosnippet>327</a>                    num_readonly_unsigned_accounts: <span class="number">0</span>,
<a href=#328 id=328 data-nosnippet>328</a>                },
<a href=#329 id=329 data-nosnippet>329</a>                account_keys: <span class="macro">vec!</span>[key0],
<a href=#330 id=330 data-nosnippet>330</a>                instructions: <span class="macro">vec!</span>[CompiledInstruction {
<a href=#331 id=331 data-nosnippet>331</a>                    program_id_index: <span class="number">2</span>,
<a href=#332 id=332 data-nosnippet>332</a>                    accounts: <span class="macro">vec!</span>[<span class="number">1</span>],
<a href=#333 id=333 data-nosnippet>333</a>                    data: <span class="macro">vec!</span>[],
<a href=#334 id=334 data-nosnippet>334</a>                }],
<a href=#335 id=335 data-nosnippet>335</a>                ..v0::Message::default()
<a href=#336 id=336 data-nosnippet>336</a>            },
<a href=#337 id=337 data-nosnippet>337</a>            LoadedAddresses {
<a href=#338 id=338 data-nosnippet>338</a>                writable: <span class="macro">vec!</span>[key1, key2],
<a href=#339 id=339 data-nosnippet>339</a>                readonly: <span class="macro">vec!</span>[],
<a href=#340 id=340 data-nosnippet>340</a>            },
<a href=#341 id=341 data-nosnippet>341</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#342 id=342 data-nosnippet>342</a>        );
<a href=#343 id=343 data-nosnippet>343</a>
<a href=#344 id=344 data-nosnippet>344</a>        <span class="macro">assert!</span>(message.is_writable_index(<span class="number">2</span>));
<a href=#345 id=345 data-nosnippet>345</a>        <span class="macro">assert!</span>(!message.is_writable(<span class="number">2</span>));
<a href=#346 id=346 data-nosnippet>346</a>    }
<a href=#347 id=347 data-nosnippet>347</a>}</code></pre></div></section></main></body></html>