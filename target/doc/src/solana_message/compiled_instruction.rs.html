<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/compiled_instruction.rs`."><title>compiled_instruction.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>compiled_instruction.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#2 id=2 data-nosnippet>2</a></span><span class="kw">use </span>serde_derive::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="attr">#[cfg(feature = <span class="string">"frozen-abi"</span>)]
<a href=#4 id=4 data-nosnippet>4</a></span><span class="kw">use </span>solana_frozen_abi_macro::AbiExample;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>{solana_pubkey::Pubkey, solana_sanitize::Sanitize};
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="doccomment">/// A compact encoding of an instruction.
<a href=#8 id=8 data-nosnippet>8</a>///
<a href=#9 id=9 data-nosnippet>9</a>/// A `CompiledInstruction` is a component of a multi-instruction [`Message`],
<a href=#10 id=10 data-nosnippet>10</a>/// which is the core of a Solana transaction. It is created during the
<a href=#11 id=11 data-nosnippet>11</a>/// construction of `Message`. Most users will not interact with it directly.
<a href=#12 id=12 data-nosnippet>12</a>///
<a href=#13 id=13 data-nosnippet>13</a>/// [`Message`]: crate::Message
<a href=#14 id=14 data-nosnippet>14</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"frozen-abi"</span>, derive(AbiExample))]
<a href=#15 id=15 data-nosnippet>15</a>#[cfg_attr(
<a href=#16 id=16 data-nosnippet>16</a>    feature = <span class="string">"serde"</span>,
<a href=#17 id=17 data-nosnippet>17</a>    derive(Deserialize, Serialize),
<a href=#18 id=18 data-nosnippet>18</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#19 id=19 data-nosnippet>19</a>)]
<a href=#20 id=20 data-nosnippet>20</a>#[derive(Debug, PartialEq, Eq, Clone)]
<a href=#21 id=21 data-nosnippet>21</a></span><span class="kw">pub struct </span>CompiledInstruction {
<a href=#22 id=22 data-nosnippet>22</a>    <span class="doccomment">/// Index into the transaction keys array indicating the program account that executes this instruction.
<a href=#23 id=23 data-nosnippet>23</a>    </span><span class="kw">pub </span>program_id_index: u8,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="doccomment">/// Ordered indices into the transaction keys array indicating which accounts to pass to the program.
<a href=#25 id=25 data-nosnippet>25</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#26 id=26 data-nosnippet>26</a>    </span><span class="kw">pub </span>accounts: Vec&lt;u8&gt;,
<a href=#27 id=27 data-nosnippet>27</a>    <span class="doccomment">/// The program input data.
<a href=#28 id=28 data-nosnippet>28</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#29 id=29 data-nosnippet>29</a>    </span><span class="kw">pub </span>data: Vec&lt;u8&gt;,
<a href=#30 id=30 data-nosnippet>30</a>}
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a><span class="kw">impl </span>Sanitize <span class="kw">for </span>CompiledInstruction {}
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a><span class="kw">impl </span>CompiledInstruction {
<a href=#35 id=35 data-nosnippet>35</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#36 id=36 data-nosnippet>36</a>    </span><span class="kw">pub fn </span>new&lt;T: serde::Serialize&gt;(program_ids_index: u8, data: <span class="kw-2">&amp;</span>T, accounts: Vec&lt;u8&gt;) -&gt; <span class="self">Self </span>{
<a href=#37 id=37 data-nosnippet>37</a>        <span class="kw">let </span>data = bincode::serialize(data).unwrap();
<a href=#38 id=38 data-nosnippet>38</a>        <span class="self">Self </span>{
<a href=#39 id=39 data-nosnippet>39</a>            program_id_index: program_ids_index,
<a href=#40 id=40 data-nosnippet>40</a>            accounts,
<a href=#41 id=41 data-nosnippet>41</a>            data,
<a href=#42 id=42 data-nosnippet>42</a>        }
<a href=#43 id=43 data-nosnippet>43</a>    }
<a href=#44 id=44 data-nosnippet>44</a>
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">pub fn </span>new_from_raw_parts(program_id_index: u8, data: Vec&lt;u8&gt;, accounts: Vec&lt;u8&gt;) -&gt; <span class="self">Self </span>{
<a href=#46 id=46 data-nosnippet>46</a>        <span class="self">Self </span>{
<a href=#47 id=47 data-nosnippet>47</a>            program_id_index,
<a href=#48 id=48 data-nosnippet>48</a>            accounts,
<a href=#49 id=49 data-nosnippet>49</a>            data,
<a href=#50 id=50 data-nosnippet>50</a>        }
<a href=#51 id=51 data-nosnippet>51</a>    }
<a href=#52 id=52 data-nosnippet>52</a>
<a href=#53 id=53 data-nosnippet>53</a>    <span class="kw">pub fn </span>program_id&lt;<span class="lifetime">'a</span>&gt;(<span class="kw-2">&amp;</span><span class="self">self</span>, program_ids: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>[Pubkey]) -&gt; <span class="kw-2">&amp;</span><span class="lifetime">'a </span>Pubkey {
<a href=#54 id=54 data-nosnippet>54</a>        <span class="kw-2">&amp;</span>program_ids[<span class="self">self</span>.program_id_index <span class="kw">as </span>usize]
<a href=#55 id=55 data-nosnippet>55</a>    }
<a href=#56 id=56 data-nosnippet>56</a>}</code></pre></div></section></main></body></html>