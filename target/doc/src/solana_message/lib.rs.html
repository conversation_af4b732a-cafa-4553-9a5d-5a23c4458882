<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/lib.rs`."><title>lib.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>lib.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#![cfg_attr(docsrs, feature(doc_auto_cfg))]
<a href=#2 id=2 data-nosnippet>2</a>#![cfg_attr(feature = <span class="string">"frozen-abi"</span>, feature(min_specialization))]
<a href=#3 id=3 data-nosnippet>3</a></span><span class="doccomment">//! Sequences of [`Instruction`]s executed within a single transaction.
<a href=#4 id=4 data-nosnippet>4</a>//!
<a href=#5 id=5 data-nosnippet>5</a>//! [`Instruction`]: https://docs.rs/solana-instruction/latest/solana_instruction/struct.Instruction.html
<a href=#6 id=6 data-nosnippet>6</a>//!
<a href=#7 id=7 data-nosnippet>7</a>//! In Solana, programs execute instructions, and clients submit sequences
<a href=#8 id=8 data-nosnippet>8</a>//! of instructions to the network to be atomically executed as [`Transaction`]s.
<a href=#9 id=9 data-nosnippet>9</a>//!
<a href=#10 id=10 data-nosnippet>10</a>//! [`Transaction`]: https://docs.rs/solana-sdk/latest/solana-sdk/transaction/struct.Transaction.html
<a href=#11 id=11 data-nosnippet>11</a>//!
<a href=#12 id=12 data-nosnippet>12</a>//! A [`Message`] is the compact internal encoding of a transaction, as
<a href=#13 id=13 data-nosnippet>13</a>//! transmitted across the network and stored in, and operated on, by the
<a href=#14 id=14 data-nosnippet>14</a>//! runtime. It contains a flat array of all accounts accessed by all
<a href=#15 id=15 data-nosnippet>15</a>//! instructions in the message, a [`MessageHeader`] that describes the layout
<a href=#16 id=16 data-nosnippet>16</a>//! of that account array, a [recent blockhash], and a compact encoding of the
<a href=#17 id=17 data-nosnippet>17</a>//! message's instructions.
<a href=#18 id=18 data-nosnippet>18</a>//!
<a href=#19 id=19 data-nosnippet>19</a>//! [recent blockhash]: https://solana.com/docs/core/transactions#recent-blockhash
<a href=#20 id=20 data-nosnippet>20</a>//!
<a href=#21 id=21 data-nosnippet>21</a>//! Clients most often deal with `Instruction`s and `Transaction`s, with
<a href=#22 id=22 data-nosnippet>22</a>//! `Message`s being created by `Transaction` constructors.
<a href=#23 id=23 data-nosnippet>23</a>//!
<a href=#24 id=24 data-nosnippet>24</a>//! To ensure reliable network delivery, serialized messages must fit into the
<a href=#25 id=25 data-nosnippet>25</a>//! IPv6 MTU size, conservatively assumed to be 1280 bytes. Thus constrained,
<a href=#26 id=26 data-nosnippet>26</a>//! care must be taken in the amount of data consumed by instructions, and the
<a href=#27 id=27 data-nosnippet>27</a>//! number of accounts they require to function.
<a href=#28 id=28 data-nosnippet>28</a>//!
<a href=#29 id=29 data-nosnippet>29</a>//! This module defines two versions of `Message` in their own modules:
<a href=#30 id=30 data-nosnippet>30</a>//! [`legacy`] and [`v0`]. `legacy` is reexported here and is the current
<a href=#31 id=31 data-nosnippet>31</a>//! version as of Solana 1.10.0. `v0` is a [future message format] that encodes
<a href=#32 id=32 data-nosnippet>32</a>//! more account keys into a transaction than the legacy format. The
<a href=#33 id=33 data-nosnippet>33</a>//! [`VersionedMessage`] type is a thin wrapper around either message version.
<a href=#34 id=34 data-nosnippet>34</a>//!
<a href=#35 id=35 data-nosnippet>35</a>//! [future message format]: https://docs.solanalabs.com/proposals/versioned-transactions
<a href=#36 id=36 data-nosnippet>36</a>//!
<a href=#37 id=37 data-nosnippet>37</a>//! Despite living in the `solana-program` crate, there is no way to access the
<a href=#38 id=38 data-nosnippet>38</a>//! runtime's messages from within a Solana program, and only the legacy message
<a href=#39 id=39 data-nosnippet>39</a>//! types continue to be exposed to Solana programs, for backwards compatibility
<a href=#40 id=40 data-nosnippet>40</a>//! reasons.
<a href=#41 id=41 data-nosnippet>41</a>
<a href=#42 id=42 data-nosnippet>42</a></span><span class="kw">pub mod </span>compiled_instruction;
<a href=#43 id=43 data-nosnippet>43</a><span class="kw">mod </span>compiled_keys;
<a href=#44 id=44 data-nosnippet>44</a><span class="kw">pub mod </span>inner_instruction;
<a href=#45 id=45 data-nosnippet>45</a><span class="kw">pub mod </span>legacy;
<a href=#46 id=46 data-nosnippet>46</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#47 id=47 data-nosnippet>47</a></span><span class="kw">use </span>serde_derive::{Deserialize, Serialize};
<a href=#48 id=48 data-nosnippet>48</a><span class="attr">#[cfg(feature = <span class="string">"frozen-abi"</span>)]
<a href=#49 id=49 data-nosnippet>49</a></span><span class="kw">use </span>solana_frozen_abi_macro::AbiExample;
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a><span class="attr">#[cfg(not(target_os = <span class="string">"solana"</span>))]
<a href=#52 id=52 data-nosnippet>52</a>#[path = <span class="string">""</span>]
<a href=#53 id=53 data-nosnippet>53</a></span><span class="kw">mod </span>non_bpf_modules {
<a href=#54 id=54 data-nosnippet>54</a>    <span class="kw">mod </span>account_keys;
<a href=#55 id=55 data-nosnippet>55</a>    <span class="kw">mod </span>address_loader;
<a href=#56 id=56 data-nosnippet>56</a>    <span class="kw">mod </span>sanitized;
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">mod </span>versions;
<a href=#58 id=58 data-nosnippet>58</a>
<a href=#59 id=59 data-nosnippet>59</a>    <span class="kw">pub use </span>{account_keys::<span class="kw-2">*</span>, address_loader::<span class="kw-2">*</span>, sanitized::<span class="kw-2">*</span>, versions::<span class="kw-2">*</span>};
<a href=#60 id=60 data-nosnippet>60</a>}
<a href=#61 id=61 data-nosnippet>61</a>
<a href=#62 id=62 data-nosnippet>62</a><span class="attr">#[cfg(not(target_os = <span class="string">"solana"</span>))]
<a href=#63 id=63 data-nosnippet>63</a></span><span class="kw">pub use </span>non_bpf_modules::<span class="kw-2">*</span>;
<a href=#64 id=64 data-nosnippet>64</a><span class="kw">pub use </span>{compiled_keys::CompileError, legacy::Message};
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a><span class="doccomment">/// The length of a message header in bytes.
<a href=#67 id=67 data-nosnippet>67</a></span><span class="kw">pub const </span>MESSAGE_HEADER_LENGTH: usize = <span class="number">3</span>;
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a><span class="doccomment">/// Describes the organization of a `Message`'s account keys.
<a href=#70 id=70 data-nosnippet>70</a>///
<a href=#71 id=71 data-nosnippet>71</a>/// Every [`Instruction`] specifies which accounts it may reference, or
<a href=#72 id=72 data-nosnippet>72</a>/// otherwise requires specific permissions of. Those specifications are:
<a href=#73 id=73 data-nosnippet>73</a>/// whether the account is read-only, or read-write; and whether the account
<a href=#74 id=74 data-nosnippet>74</a>/// must have signed the transaction containing the instruction.
<a href=#75 id=75 data-nosnippet>75</a>///
<a href=#76 id=76 data-nosnippet>76</a>/// Whereas individual `Instruction`s contain a list of all accounts they may
<a href=#77 id=77 data-nosnippet>77</a>/// access, along with their required permissions, a `Message` contains a
<a href=#78 id=78 data-nosnippet>78</a>/// single shared flat list of _all_ accounts required by _all_ instructions in
<a href=#79 id=79 data-nosnippet>79</a>/// a transaction. When building a `Message`, this flat list is created and
<a href=#80 id=80 data-nosnippet>80</a>/// `Instruction`s are converted to [`CompiledInstruction`]s. Those
<a href=#81 id=81 data-nosnippet>81</a>/// `CompiledInstruction`s then reference by index the accounts they require in
<a href=#82 id=82 data-nosnippet>82</a>/// the single shared account list.
<a href=#83 id=83 data-nosnippet>83</a>///
<a href=#84 id=84 data-nosnippet>84</a>/// [`Instruction`]: https://docs.rs/solana-instruction/latest/solana_instruction/struct.Instruction.html
<a href=#85 id=85 data-nosnippet>85</a>/// [`CompiledInstruction`]: crate::compiled_instruction::CompiledInstruction
<a href=#86 id=86 data-nosnippet>86</a>///
<a href=#87 id=87 data-nosnippet>87</a>/// The shared account list is ordered by the permissions required of the accounts:
<a href=#88 id=88 data-nosnippet>88</a>///
<a href=#89 id=89 data-nosnippet>89</a>/// - accounts that are writable and signers
<a href=#90 id=90 data-nosnippet>90</a>/// - accounts that are read-only and signers
<a href=#91 id=91 data-nosnippet>91</a>/// - accounts that are writable and not signers
<a href=#92 id=92 data-nosnippet>92</a>/// - accounts that are read-only and not signers
<a href=#93 id=93 data-nosnippet>93</a>///
<a href=#94 id=94 data-nosnippet>94</a>/// Given this ordering, the fields of `MessageHeader` describe which accounts
<a href=#95 id=95 data-nosnippet>95</a>/// in a transaction require which permissions.
<a href=#96 id=96 data-nosnippet>96</a>///
<a href=#97 id=97 data-nosnippet>97</a>/// When multiple transactions access the same read-only accounts, the runtime
<a href=#98 id=98 data-nosnippet>98</a>/// may process them in parallel, in a single [PoH] entry. Transactions that
<a href=#99 id=99 data-nosnippet>99</a>/// access the same read-write accounts are processed sequentially.
<a href=#100 id=100 data-nosnippet>100</a>///
<a href=#101 id=101 data-nosnippet>101</a>/// [PoH]: https://docs.solanalabs.com/consensus/synchronization
<a href=#102 id=102 data-nosnippet>102</a></span><span class="attr">#[cfg_attr(feature = <span class="string">"frozen-abi"</span>, derive(AbiExample))]
<a href=#103 id=103 data-nosnippet>103</a>#[cfg_attr(
<a href=#104 id=104 data-nosnippet>104</a>    feature = <span class="string">"serde"</span>,
<a href=#105 id=105 data-nosnippet>105</a>    derive(Deserialize, Serialize),
<a href=#106 id=106 data-nosnippet>106</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#107 id=107 data-nosnippet>107</a>)]
<a href=#108 id=108 data-nosnippet>108</a>#[derive(Default, Debug, PartialEq, Eq, Clone, Copy)]
<a href=#109 id=109 data-nosnippet>109</a></span><span class="kw">pub struct </span>MessageHeader {
<a href=#110 id=110 data-nosnippet>110</a>    <span class="doccomment">/// The number of signatures required for this message to be considered
<a href=#111 id=111 data-nosnippet>111</a>    /// valid. The signers of those signatures must match the first
<a href=#112 id=112 data-nosnippet>112</a>    /// `num_required_signatures` of [`Message::account_keys`].
<a href=#113 id=113 data-nosnippet>113</a>    </span><span class="comment">// NOTE: Serialization-related changes must be paired with the direct read at sigverify.
<a href=#114 id=114 data-nosnippet>114</a>    </span><span class="kw">pub </span>num_required_signatures: u8,
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>    <span class="doccomment">/// The last `num_readonly_signed_accounts` of the signed keys are read-only
<a href=#117 id=117 data-nosnippet>117</a>    /// accounts.
<a href=#118 id=118 data-nosnippet>118</a>    </span><span class="kw">pub </span>num_readonly_signed_accounts: u8,
<a href=#119 id=119 data-nosnippet>119</a>
<a href=#120 id=120 data-nosnippet>120</a>    <span class="doccomment">/// The last `num_readonly_unsigned_accounts` of the unsigned keys are
<a href=#121 id=121 data-nosnippet>121</a>    /// read-only accounts.
<a href=#122 id=122 data-nosnippet>122</a>    </span><span class="kw">pub </span>num_readonly_unsigned_accounts: u8,
<a href=#123 id=123 data-nosnippet>123</a>}
<a href=#124 id=124 data-nosnippet>124</a>
<a href=#125 id=125 data-nosnippet>125</a><span class="doccomment">/// The definition of address lookup table accounts.
<a href=#126 id=126 data-nosnippet>126</a>///
<a href=#127 id=127 data-nosnippet>127</a>/// As used by the `crate::v0` message format.
<a href=#128 id=128 data-nosnippet>128</a></span><span class="attr">#[derive(Debug, PartialEq, Eq, Clone)]
<a href=#129 id=129 data-nosnippet>129</a></span><span class="kw">pub struct </span>AddressLookupTableAccount {
<a href=#130 id=130 data-nosnippet>130</a>    <span class="kw">pub </span>key: solana_pubkey::Pubkey,
<a href=#131 id=131 data-nosnippet>131</a>    <span class="kw">pub </span>addresses: Vec&lt;solana_pubkey::Pubkey&gt;,
<a href=#132 id=132 data-nosnippet>132</a>}</code></pre></div></section></main></body></html>