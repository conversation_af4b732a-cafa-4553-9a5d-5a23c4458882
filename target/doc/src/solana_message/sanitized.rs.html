<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/sanitized.rs`."><title>sanitized.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>sanitized.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="attr">#[deprecated(
<a href=#2 id=2 data-nosnippet>2</a>    since = <span class="string">"2.1.0"</span>,
<a href=#3 id=3 data-nosnippet>3</a>    note = <span class="string">"Use solana_transaction_error::SanitizeMessageError instead"
<a href=#4 id=4 data-nosnippet>4</a></span>)]
<a href=#5 id=5 data-nosnippet>5</a></span><span class="kw">pub use </span>solana_transaction_error::SanitizeMessageError;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>{
<a href=#7 id=7 data-nosnippet>7</a>    <span class="kw">crate</span>::{
<a href=#8 id=8 data-nosnippet>8</a>        compiled_instruction::CompiledInstruction,
<a href=#9 id=9 data-nosnippet>9</a>        legacy,
<a href=#10 id=10 data-nosnippet>10</a>        v0::{<span class="self">self</span>, LoadedAddresses},
<a href=#11 id=11 data-nosnippet>11</a>        AccountKeys, AddressLoader, MessageHeader, SanitizedVersionedMessage, VersionedMessage,
<a href=#12 id=12 data-nosnippet>12</a>    },
<a href=#13 id=13 data-nosnippet>13</a>    solana_hash::Hash,
<a href=#14 id=14 data-nosnippet>14</a>    solana_instruction::{BorrowedAccountMeta, BorrowedInstruction},
<a href=#15 id=15 data-nosnippet>15</a>    solana_pubkey::Pubkey,
<a href=#16 id=16 data-nosnippet>16</a>    solana_sanitize::Sanitize,
<a href=#17 id=17 data-nosnippet>17</a>    solana_sdk_ids::{ed25519_program, secp256k1_program, secp256r1_program},
<a href=#18 id=18 data-nosnippet>18</a>    std::{borrow::Cow, collections::HashSet, convert::TryFrom},
<a href=#19 id=19 data-nosnippet>19</a>};
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a><span class="comment">// inlined to avoid solana_nonce dep
<a href=#22 id=22 data-nosnippet>22</a></span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#23 id=23 data-nosnippet>23</a></span><span class="kw">const </span>NONCED_TX_MARKER_IX_INDEX: u8 = <span class="number">0</span>;
<a href=#24 id=24 data-nosnippet>24</a><span class="attr">#[cfg(test)]
<a href=#25 id=25 data-nosnippet>25</a></span><span class="macro">static_assertions::const_assert_eq!</span>(
<a href=#26 id=26 data-nosnippet>26</a>    NONCED_TX_MARKER_IX_INDEX,
<a href=#27 id=27 data-nosnippet>27</a>    solana_nonce::NONCED_TX_MARKER_IX_INDEX
<a href=#28 id=28 data-nosnippet>28</a>);
<a href=#29 id=29 data-nosnippet>29</a>
<a href=#30 id=30 data-nosnippet>30</a><span class="attr">#[derive(Debug, Clone, Eq, PartialEq)]
<a href=#31 id=31 data-nosnippet>31</a></span><span class="kw">pub struct </span>LegacyMessage&lt;<span class="lifetime">'a</span>&gt; {
<a href=#32 id=32 data-nosnippet>32</a>    <span class="doccomment">/// Legacy message
<a href=#33 id=33 data-nosnippet>33</a>    </span><span class="kw">pub </span>message: Cow&lt;<span class="lifetime">'a</span>, legacy::Message&gt;,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="doccomment">/// List of boolean with same length as account_keys(), each boolean value indicates if
<a href=#35 id=35 data-nosnippet>35</a>    /// corresponding account key is writable or not.
<a href=#36 id=36 data-nosnippet>36</a>    </span><span class="kw">pub </span>is_writable_account_cache: Vec&lt;bool&gt;,
<a href=#37 id=37 data-nosnippet>37</a>}
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a><span class="kw">impl </span>LegacyMessage&lt;<span class="lifetime">'_</span>&gt; {
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">pub fn </span>new(message: legacy::Message, reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;) -&gt; <span class="self">Self </span>{
<a href=#41 id=41 data-nosnippet>41</a>        <span class="kw">let </span>is_writable_account_cache = message
<a href=#42 id=42 data-nosnippet>42</a>            .account_keys
<a href=#43 id=43 data-nosnippet>43</a>            .iter()
<a href=#44 id=44 data-nosnippet>44</a>            .enumerate()
<a href=#45 id=45 data-nosnippet>45</a>            .map(|(i, _key)| {
<a href=#46 id=46 data-nosnippet>46</a>                message.is_writable_index(i)
<a href=#47 id=47 data-nosnippet>47</a>                    &amp;&amp; !reserved_account_keys.contains(<span class="kw-2">&amp;</span>message.account_keys[i])
<a href=#48 id=48 data-nosnippet>48</a>                    &amp;&amp; !message.demote_program_id(i)
<a href=#49 id=49 data-nosnippet>49</a>            })
<a href=#50 id=50 data-nosnippet>50</a>            .collect::&lt;Vec&lt;<span class="kw">_</span>&gt;&gt;();
<a href=#51 id=51 data-nosnippet>51</a>        <span class="self">Self </span>{
<a href=#52 id=52 data-nosnippet>52</a>            message: Cow::Owned(message),
<a href=#53 id=53 data-nosnippet>53</a>            is_writable_account_cache,
<a href=#54 id=54 data-nosnippet>54</a>        }
<a href=#55 id=55 data-nosnippet>55</a>    }
<a href=#56 id=56 data-nosnippet>56</a>
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">pub fn </span>has_duplicates(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#58 id=58 data-nosnippet>58</a>        <span class="self">self</span>.message.has_duplicates()
<a href=#59 id=59 data-nosnippet>59</a>    }
<a href=#60 id=60 data-nosnippet>60</a>
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub fn </span>is_key_called_as_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#62 id=62 data-nosnippet>62</a>        <span class="self">self</span>.message.is_key_called_as_program(key_index)
<a href=#63 id=63 data-nosnippet>63</a>    }
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a>    <span class="doccomment">/// Inspect all message keys for the bpf upgradeable loader
<a href=#66 id=66 data-nosnippet>66</a>    </span><span class="kw">pub fn </span>is_upgradeable_loader_present(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#67 id=67 data-nosnippet>67</a>        <span class="self">self</span>.message.is_upgradeable_loader_present()
<a href=#68 id=68 data-nosnippet>68</a>    }
<a href=#69 id=69 data-nosnippet>69</a>
<a href=#70 id=70 data-nosnippet>70</a>    <span class="doccomment">/// Returns the full list of account keys.
<a href=#71 id=71 data-nosnippet>71</a>    </span><span class="kw">pub fn </span>account_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; AccountKeys {
<a href=#72 id=72 data-nosnippet>72</a>        AccountKeys::new(<span class="kw-2">&amp;</span><span class="self">self</span>.message.account_keys, <span class="prelude-val">None</span>)
<a href=#73 id=73 data-nosnippet>73</a>    }
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a>    <span class="kw">pub fn </span>is_writable(<span class="kw-2">&amp;</span><span class="self">self</span>, index: usize) -&gt; bool {
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw-2">*</span><span class="self">self</span>.is_writable_account_cache.get(index).unwrap_or(<span class="kw-2">&amp;</span><span class="bool-val">false</span>)
<a href=#77 id=77 data-nosnippet>77</a>    }
<a href=#78 id=78 data-nosnippet>78</a>}
<a href=#79 id=79 data-nosnippet>79</a>
<a href=#80 id=80 data-nosnippet>80</a><span class="doccomment">/// Sanitized message of a transaction.
<a href=#81 id=81 data-nosnippet>81</a></span><span class="attr">#[derive(Debug, Clone, Eq, PartialEq)]
<a href=#82 id=82 data-nosnippet>82</a></span><span class="kw">pub enum </span>SanitizedMessage {
<a href=#83 id=83 data-nosnippet>83</a>    <span class="doccomment">/// Sanitized legacy message
<a href=#84 id=84 data-nosnippet>84</a>    </span>Legacy(LegacyMessage&lt;<span class="lifetime">'static</span>&gt;),
<a href=#85 id=85 data-nosnippet>85</a>    <span class="doccomment">/// Sanitized version #0 message with dynamically loaded addresses
<a href=#86 id=86 data-nosnippet>86</a>    </span>V0(v0::LoadedMessage&lt;<span class="lifetime">'static</span>&gt;),
<a href=#87 id=87 data-nosnippet>87</a>}
<a href=#88 id=88 data-nosnippet>88</a>
<a href=#89 id=89 data-nosnippet>89</a><span class="kw">impl </span>SanitizedMessage {
<a href=#90 id=90 data-nosnippet>90</a>    <span class="doccomment">/// Create a sanitized message from a sanitized versioned message.
<a href=#91 id=91 data-nosnippet>91</a>    /// If the input message uses address tables, attempt to look up the
<a href=#92 id=92 data-nosnippet>92</a>    /// address for each table index.
<a href=#93 id=93 data-nosnippet>93</a>    </span><span class="kw">pub fn </span>try_new(
<a href=#94 id=94 data-nosnippet>94</a>        sanitized_msg: SanitizedVersionedMessage,
<a href=#95 id=95 data-nosnippet>95</a>        address_loader: <span class="kw">impl </span>AddressLoader,
<a href=#96 id=96 data-nosnippet>96</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#97 id=97 data-nosnippet>97</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, SanitizeMessageError&gt; {
<a href=#98 id=98 data-nosnippet>98</a>        <span class="prelude-val">Ok</span>(<span class="kw">match </span>sanitized_msg.message {
<a href=#99 id=99 data-nosnippet>99</a>            VersionedMessage::Legacy(message) =&gt; {
<a href=#100 id=100 data-nosnippet>100</a>                SanitizedMessage::Legacy(LegacyMessage::new(message, reserved_account_keys))
<a href=#101 id=101 data-nosnippet>101</a>            }
<a href=#102 id=102 data-nosnippet>102</a>            VersionedMessage::V0(message) =&gt; {
<a href=#103 id=103 data-nosnippet>103</a>                <span class="kw">let </span>loaded_addresses =
<a href=#104 id=104 data-nosnippet>104</a>                    address_loader.load_addresses(<span class="kw-2">&amp;</span>message.address_table_lookups)<span class="question-mark">?</span>;
<a href=#105 id=105 data-nosnippet>105</a>                SanitizedMessage::V0(v0::LoadedMessage::new(
<a href=#106 id=106 data-nosnippet>106</a>                    message,
<a href=#107 id=107 data-nosnippet>107</a>                    loaded_addresses,
<a href=#108 id=108 data-nosnippet>108</a>                    reserved_account_keys,
<a href=#109 id=109 data-nosnippet>109</a>                ))
<a href=#110 id=110 data-nosnippet>110</a>            }
<a href=#111 id=111 data-nosnippet>111</a>        })
<a href=#112 id=112 data-nosnippet>112</a>    }
<a href=#113 id=113 data-nosnippet>113</a>
<a href=#114 id=114 data-nosnippet>114</a>    <span class="doccomment">/// Create a sanitized legacy message
<a href=#115 id=115 data-nosnippet>115</a>    </span><span class="kw">pub fn </span>try_from_legacy_message(
<a href=#116 id=116 data-nosnippet>116</a>        message: legacy::Message,
<a href=#117 id=117 data-nosnippet>117</a>        reserved_account_keys: <span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;,
<a href=#118 id=118 data-nosnippet>118</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>, SanitizeMessageError&gt; {
<a href=#119 id=119 data-nosnippet>119</a>        message.sanitize()<span class="question-mark">?</span>;
<a href=#120 id=120 data-nosnippet>120</a>        <span class="prelude-val">Ok</span>(<span class="self">Self</span>::Legacy(LegacyMessage::new(
<a href=#121 id=121 data-nosnippet>121</a>            message,
<a href=#122 id=122 data-nosnippet>122</a>            reserved_account_keys,
<a href=#123 id=123 data-nosnippet>123</a>        )))
<a href=#124 id=124 data-nosnippet>124</a>    }
<a href=#125 id=125 data-nosnippet>125</a>
<a href=#126 id=126 data-nosnippet>126</a>    <span class="doccomment">/// Return true if this message contains duplicate account keys
<a href=#127 id=127 data-nosnippet>127</a>    </span><span class="kw">pub fn </span>has_duplicates(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#128 id=128 data-nosnippet>128</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#129 id=129 data-nosnippet>129</a>            SanitizedMessage::Legacy(message) =&gt; message.has_duplicates(),
<a href=#130 id=130 data-nosnippet>130</a>            SanitizedMessage::V0(message) =&gt; message.has_duplicates(),
<a href=#131 id=131 data-nosnippet>131</a>        }
<a href=#132 id=132 data-nosnippet>132</a>    }
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a>    <span class="doccomment">/// Message header which identifies the number of signer and writable or
<a href=#135 id=135 data-nosnippet>135</a>    /// readonly accounts
<a href=#136 id=136 data-nosnippet>136</a>    </span><span class="kw">pub fn </span>header(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>MessageHeader {
<a href=#137 id=137 data-nosnippet>137</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#138 id=138 data-nosnippet>138</a>            <span class="self">Self</span>::Legacy(legacy_message) =&gt; <span class="kw-2">&amp;</span>legacy_message.message.header,
<a href=#139 id=139 data-nosnippet>139</a>            <span class="self">Self</span>::V0(loaded_msg) =&gt; <span class="kw-2">&amp;</span>loaded_msg.message.header,
<a href=#140 id=140 data-nosnippet>140</a>        }
<a href=#141 id=141 data-nosnippet>141</a>    }
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>    <span class="doccomment">/// Returns a legacy message if this sanitized message wraps one
<a href=#144 id=144 data-nosnippet>144</a>    </span><span class="kw">pub fn </span>legacy_message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>legacy::Message&gt; {
<a href=#145 id=145 data-nosnippet>145</a>        <span class="kw">if let </span><span class="self">Self</span>::Legacy(legacy_message) = <span class="kw-2">&amp;</span><span class="self">self </span>{
<a href=#146 id=146 data-nosnippet>146</a>            <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>legacy_message.message)
<a href=#147 id=147 data-nosnippet>147</a>        } <span class="kw">else </span>{
<a href=#148 id=148 data-nosnippet>148</a>            <span class="prelude-val">None
<a href=#149 id=149 data-nosnippet>149</a>        </span>}
<a href=#150 id=150 data-nosnippet>150</a>    }
<a href=#151 id=151 data-nosnippet>151</a>
<a href=#152 id=152 data-nosnippet>152</a>    <span class="doccomment">/// Returns the fee payer for the transaction
<a href=#153 id=153 data-nosnippet>153</a>    </span><span class="kw">pub fn </span>fee_payer(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Pubkey {
<a href=#154 id=154 data-nosnippet>154</a>        <span class="self">self</span>.account_keys()
<a href=#155 id=155 data-nosnippet>155</a>            .get(<span class="number">0</span>)
<a href=#156 id=156 data-nosnippet>156</a>            .expect(<span class="string">"sanitized messages always have a fee payer at index 0"</span>)
<a href=#157 id=157 data-nosnippet>157</a>    }
<a href=#158 id=158 data-nosnippet>158</a>
<a href=#159 id=159 data-nosnippet>159</a>    <span class="doccomment">/// The hash of a recent block, used for timing out a transaction
<a href=#160 id=160 data-nosnippet>160</a>    </span><span class="kw">pub fn </span>recent_blockhash(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Hash {
<a href=#161 id=161 data-nosnippet>161</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#162 id=162 data-nosnippet>162</a>            <span class="self">Self</span>::Legacy(legacy_message) =&gt; <span class="kw-2">&amp;</span>legacy_message.message.recent_blockhash,
<a href=#163 id=163 data-nosnippet>163</a>            <span class="self">Self</span>::V0(loaded_msg) =&gt; <span class="kw-2">&amp;</span>loaded_msg.message.recent_blockhash,
<a href=#164 id=164 data-nosnippet>164</a>        }
<a href=#165 id=165 data-nosnippet>165</a>    }
<a href=#166 id=166 data-nosnippet>166</a>
<a href=#167 id=167 data-nosnippet>167</a>    <span class="doccomment">/// Program instructions that will be executed in sequence and committed in
<a href=#168 id=168 data-nosnippet>168</a>    /// one atomic transaction if all succeed.
<a href=#169 id=169 data-nosnippet>169</a>    </span><span class="kw">pub fn </span>instructions(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[CompiledInstruction] {
<a href=#170 id=170 data-nosnippet>170</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#171 id=171 data-nosnippet>171</a>            <span class="self">Self</span>::Legacy(legacy_message) =&gt; <span class="kw-2">&amp;</span>legacy_message.message.instructions,
<a href=#172 id=172 data-nosnippet>172</a>            <span class="self">Self</span>::V0(loaded_msg) =&gt; <span class="kw-2">&amp;</span>loaded_msg.message.instructions,
<a href=#173 id=173 data-nosnippet>173</a>        }
<a href=#174 id=174 data-nosnippet>174</a>    }
<a href=#175 id=175 data-nosnippet>175</a>
<a href=#176 id=176 data-nosnippet>176</a>    <span class="doccomment">/// Program instructions iterator which includes each instruction's program
<a href=#177 id=177 data-nosnippet>177</a>    /// id.
<a href=#178 id=178 data-nosnippet>178</a>    </span><span class="kw">pub fn </span>program_instructions_iter(
<a href=#179 id=179 data-nosnippet>179</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#180 id=180 data-nosnippet>180</a>    ) -&gt; <span class="kw">impl </span>Iterator&lt;Item = (<span class="kw-2">&amp;</span>Pubkey, <span class="kw-2">&amp;</span>CompiledInstruction)&gt; + Clone {
<a href=#181 id=181 data-nosnippet>181</a>        <span class="self">self</span>.instructions().iter().map(<span class="kw">move </span>|ix| {
<a href=#182 id=182 data-nosnippet>182</a>            (
<a href=#183 id=183 data-nosnippet>183</a>                <span class="self">self</span>.account_keys()
<a href=#184 id=184 data-nosnippet>184</a>                    .get(usize::from(ix.program_id_index))
<a href=#185 id=185 data-nosnippet>185</a>                    .expect(<span class="string">"program id index is sanitized"</span>),
<a href=#186 id=186 data-nosnippet>186</a>                ix,
<a href=#187 id=187 data-nosnippet>187</a>            )
<a href=#188 id=188 data-nosnippet>188</a>        })
<a href=#189 id=189 data-nosnippet>189</a>    }
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a>    <span class="doccomment">/// Return the list of statically included account keys.
<a href=#192 id=192 data-nosnippet>192</a>    </span><span class="kw">pub fn </span>static_account_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[Pubkey] {
<a href=#193 id=193 data-nosnippet>193</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#194 id=194 data-nosnippet>194</a>            <span class="self">Self</span>::Legacy(legacy_message) =&gt; <span class="kw-2">&amp;</span>legacy_message.message.account_keys,
<a href=#195 id=195 data-nosnippet>195</a>            <span class="self">Self</span>::V0(loaded_msg) =&gt; <span class="kw-2">&amp;</span>loaded_msg.message.account_keys,
<a href=#196 id=196 data-nosnippet>196</a>        }
<a href=#197 id=197 data-nosnippet>197</a>    }
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>    <span class="doccomment">/// Returns the list of account keys that are loaded for this message.
<a href=#200 id=200 data-nosnippet>200</a>    </span><span class="kw">pub fn </span>account_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; AccountKeys {
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#202 id=202 data-nosnippet>202</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.account_keys(),
<a href=#203 id=203 data-nosnippet>203</a>            <span class="self">Self</span>::V0(message) =&gt; message.account_keys(),
<a href=#204 id=204 data-nosnippet>204</a>        }
<a href=#205 id=205 data-nosnippet>205</a>    }
<a href=#206 id=206 data-nosnippet>206</a>
<a href=#207 id=207 data-nosnippet>207</a>    <span class="doccomment">/// Returns the list of account keys used for account lookup tables.
<a href=#208 id=208 data-nosnippet>208</a>    </span><span class="kw">pub fn </span>message_address_table_lookups(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[v0::MessageAddressTableLookup] {
<a href=#209 id=209 data-nosnippet>209</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#210 id=210 data-nosnippet>210</a>            <span class="self">Self</span>::Legacy(_message) =&gt; <span class="kw-2">&amp;</span>[],
<a href=#211 id=211 data-nosnippet>211</a>            <span class="self">Self</span>::V0(message) =&gt; <span class="kw-2">&amp;</span>message.message.address_table_lookups,
<a href=#212 id=212 data-nosnippet>212</a>        }
<a href=#213 id=213 data-nosnippet>213</a>    }
<a href=#214 id=214 data-nosnippet>214</a>
<a href=#215 id=215 data-nosnippet>215</a>    <span class="doccomment">/// Returns true if the account at the specified index is an input to some
<a href=#216 id=216 data-nosnippet>216</a>    /// program instruction in this message.
<a href=#217 id=217 data-nosnippet>217</a>    </span><span class="attr">#[deprecated(since = <span class="string">"2.0.0"</span>, note = <span class="string">"Please use `is_instruction_account` instead"</span>)]
<a href=#218 id=218 data-nosnippet>218</a>    </span><span class="kw">pub fn </span>is_key_passed_to_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#219 id=219 data-nosnippet>219</a>        <span class="self">self</span>.is_instruction_account(key_index)
<a href=#220 id=220 data-nosnippet>220</a>    }
<a href=#221 id=221 data-nosnippet>221</a>
<a href=#222 id=222 data-nosnippet>222</a>    <span class="doccomment">/// Returns true if the account at the specified index is an input to some
<a href=#223 id=223 data-nosnippet>223</a>    /// program instruction in this message.
<a href=#224 id=224 data-nosnippet>224</a>    </span><span class="kw">pub fn </span>is_instruction_account(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#225 id=225 data-nosnippet>225</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(key_index) = u8::try_from(key_index) {
<a href=#226 id=226 data-nosnippet>226</a>            <span class="self">self</span>.instructions()
<a href=#227 id=227 data-nosnippet>227</a>                .iter()
<a href=#228 id=228 data-nosnippet>228</a>                .any(|ix| ix.accounts.contains(<span class="kw-2">&amp;</span>key_index))
<a href=#229 id=229 data-nosnippet>229</a>        } <span class="kw">else </span>{
<a href=#230 id=230 data-nosnippet>230</a>            <span class="bool-val">false
<a href=#231 id=231 data-nosnippet>231</a>        </span>}
<a href=#232 id=232 data-nosnippet>232</a>    }
<a href=#233 id=233 data-nosnippet>233</a>
<a href=#234 id=234 data-nosnippet>234</a>    <span class="doccomment">/// Returns true if the account at the specified index is invoked as a
<a href=#235 id=235 data-nosnippet>235</a>    /// program in this message.
<a href=#236 id=236 data-nosnippet>236</a>    </span><span class="kw">pub fn </span>is_invoked(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#237 id=237 data-nosnippet>237</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#238 id=238 data-nosnippet>238</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.is_key_called_as_program(key_index),
<a href=#239 id=239 data-nosnippet>239</a>            <span class="self">Self</span>::V0(message) =&gt; message.is_key_called_as_program(key_index),
<a href=#240 id=240 data-nosnippet>240</a>        }
<a href=#241 id=241 data-nosnippet>241</a>    }
<a href=#242 id=242 data-nosnippet>242</a>
<a href=#243 id=243 data-nosnippet>243</a>    <span class="doccomment">/// Returns true if the account at the specified index is not invoked as a
<a href=#244 id=244 data-nosnippet>244</a>    /// program or, if invoked, is passed to a program.
<a href=#245 id=245 data-nosnippet>245</a>    </span><span class="attr">#[deprecated(
<a href=#246 id=246 data-nosnippet>246</a>        since = <span class="string">"2.0.0"</span>,
<a href=#247 id=247 data-nosnippet>247</a>        note = <span class="string">"Please use `is_invoked` and `is_instruction_account` instead"
<a href=#248 id=248 data-nosnippet>248</a>    </span>)]
<a href=#249 id=249 data-nosnippet>249</a>    </span><span class="kw">pub fn </span>is_non_loader_key(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#250 id=250 data-nosnippet>250</a>        !<span class="self">self</span>.is_invoked(key_index) || <span class="self">self</span>.is_instruction_account(key_index)
<a href=#251 id=251 data-nosnippet>251</a>    }
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>    <span class="doccomment">/// Returns true if the account at the specified index is writable by the
<a href=#254 id=254 data-nosnippet>254</a>    /// instructions in this message.
<a href=#255 id=255 data-nosnippet>255</a>    </span><span class="kw">pub fn </span>is_writable(<span class="kw-2">&amp;</span><span class="self">self</span>, index: usize) -&gt; bool {
<a href=#256 id=256 data-nosnippet>256</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#257 id=257 data-nosnippet>257</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.is_writable(index),
<a href=#258 id=258 data-nosnippet>258</a>            <span class="self">Self</span>::V0(message) =&gt; message.is_writable(index),
<a href=#259 id=259 data-nosnippet>259</a>        }
<a href=#260 id=260 data-nosnippet>260</a>    }
<a href=#261 id=261 data-nosnippet>261</a>
<a href=#262 id=262 data-nosnippet>262</a>    <span class="doccomment">/// Returns true if the account at the specified index signed this
<a href=#263 id=263 data-nosnippet>263</a>    /// message.
<a href=#264 id=264 data-nosnippet>264</a>    </span><span class="kw">pub fn </span>is_signer(<span class="kw-2">&amp;</span><span class="self">self</span>, index: usize) -&gt; bool {
<a href=#265 id=265 data-nosnippet>265</a>        index &lt; usize::from(<span class="self">self</span>.header().num_required_signatures)
<a href=#266 id=266 data-nosnippet>266</a>    }
<a href=#267 id=267 data-nosnippet>267</a>
<a href=#268 id=268 data-nosnippet>268</a>    <span class="doccomment">/// Return the resolved addresses for this message if it has any.
<a href=#269 id=269 data-nosnippet>269</a>    </span><span class="kw">fn </span>loaded_lookup_table_addresses(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>LoadedAddresses&gt; {
<a href=#270 id=270 data-nosnippet>270</a>        <span class="kw">match </span><span class="kw-2">&amp;</span><span class="self">self </span>{
<a href=#271 id=271 data-nosnippet>271</a>            SanitizedMessage::V0(message) =&gt; <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>message.loaded_addresses),
<a href=#272 id=272 data-nosnippet>272</a>            <span class="kw">_ </span>=&gt; <span class="prelude-val">None</span>,
<a href=#273 id=273 data-nosnippet>273</a>        }
<a href=#274 id=274 data-nosnippet>274</a>    }
<a href=#275 id=275 data-nosnippet>275</a>
<a href=#276 id=276 data-nosnippet>276</a>    <span class="doccomment">/// Return the number of readonly accounts loaded by this message.
<a href=#277 id=277 data-nosnippet>277</a>    </span><span class="kw">pub fn </span>num_readonly_accounts(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; usize {
<a href=#278 id=278 data-nosnippet>278</a>        <span class="kw">let </span>loaded_readonly_addresses = <span class="self">self
<a href=#279 id=279 data-nosnippet>279</a>            </span>.loaded_lookup_table_addresses()
<a href=#280 id=280 data-nosnippet>280</a>            .map(|keys| keys.readonly.len())
<a href=#281 id=281 data-nosnippet>281</a>            .unwrap_or_default();
<a href=#282 id=282 data-nosnippet>282</a>        loaded_readonly_addresses
<a href=#283 id=283 data-nosnippet>283</a>            .saturating_add(usize::from(<span class="self">self</span>.header().num_readonly_signed_accounts))
<a href=#284 id=284 data-nosnippet>284</a>            .saturating_add(usize::from(<span class="self">self</span>.header().num_readonly_unsigned_accounts))
<a href=#285 id=285 data-nosnippet>285</a>    }
<a href=#286 id=286 data-nosnippet>286</a>
<a href=#287 id=287 data-nosnippet>287</a>    <span class="doccomment">/// Decompile message instructions without cloning account keys
<a href=#288 id=288 data-nosnippet>288</a>    </span><span class="kw">pub fn </span>decompile_instructions(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;BorrowedInstruction&gt; {
<a href=#289 id=289 data-nosnippet>289</a>        <span class="kw">let </span>account_keys = <span class="self">self</span>.account_keys();
<a href=#290 id=290 data-nosnippet>290</a>        <span class="self">self</span>.program_instructions_iter()
<a href=#291 id=291 data-nosnippet>291</a>            .map(|(program_id, instruction)| {
<a href=#292 id=292 data-nosnippet>292</a>                <span class="kw">let </span>accounts = instruction
<a href=#293 id=293 data-nosnippet>293</a>                    .accounts
<a href=#294 id=294 data-nosnippet>294</a>                    .iter()
<a href=#295 id=295 data-nosnippet>295</a>                    .map(|account_index| {
<a href=#296 id=296 data-nosnippet>296</a>                        <span class="kw">let </span>account_index = <span class="kw-2">*</span>account_index <span class="kw">as </span>usize;
<a href=#297 id=297 data-nosnippet>297</a>                        BorrowedAccountMeta {
<a href=#298 id=298 data-nosnippet>298</a>                            is_signer: <span class="self">self</span>.is_signer(account_index),
<a href=#299 id=299 data-nosnippet>299</a>                            is_writable: <span class="self">self</span>.is_writable(account_index),
<a href=#300 id=300 data-nosnippet>300</a>                            pubkey: account_keys.get(account_index).unwrap(),
<a href=#301 id=301 data-nosnippet>301</a>                        }
<a href=#302 id=302 data-nosnippet>302</a>                    })
<a href=#303 id=303 data-nosnippet>303</a>                    .collect();
<a href=#304 id=304 data-nosnippet>304</a>
<a href=#305 id=305 data-nosnippet>305</a>                BorrowedInstruction {
<a href=#306 id=306 data-nosnippet>306</a>                    accounts,
<a href=#307 id=307 data-nosnippet>307</a>                    data: <span class="kw-2">&amp;</span>instruction.data,
<a href=#308 id=308 data-nosnippet>308</a>                    program_id,
<a href=#309 id=309 data-nosnippet>309</a>                }
<a href=#310 id=310 data-nosnippet>310</a>            })
<a href=#311 id=311 data-nosnippet>311</a>            .collect()
<a href=#312 id=312 data-nosnippet>312</a>    }
<a href=#313 id=313 data-nosnippet>313</a>
<a href=#314 id=314 data-nosnippet>314</a>    <span class="doccomment">/// Inspect all message keys for the bpf upgradeable loader
<a href=#315 id=315 data-nosnippet>315</a>    </span><span class="kw">pub fn </span>is_upgradeable_loader_present(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#316 id=316 data-nosnippet>316</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#317 id=317 data-nosnippet>317</a>            <span class="self">Self</span>::Legacy(message) =&gt; message.is_upgradeable_loader_present(),
<a href=#318 id=318 data-nosnippet>318</a>            <span class="self">Self</span>::V0(message) =&gt; message.is_upgradeable_loader_present(),
<a href=#319 id=319 data-nosnippet>319</a>        }
<a href=#320 id=320 data-nosnippet>320</a>    }
<a href=#321 id=321 data-nosnippet>321</a>
<a href=#322 id=322 data-nosnippet>322</a>    <span class="doccomment">/// Get a list of signers for the instruction at the given index
<a href=#323 id=323 data-nosnippet>323</a>    </span><span class="kw">pub fn </span>get_ix_signers(<span class="kw-2">&amp;</span><span class="self">self</span>, ix_index: usize) -&gt; <span class="kw">impl </span>Iterator&lt;Item = <span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#324 id=324 data-nosnippet>324</a>        <span class="self">self</span>.instructions()
<a href=#325 id=325 data-nosnippet>325</a>            .get(ix_index)
<a href=#326 id=326 data-nosnippet>326</a>            .into_iter()
<a href=#327 id=327 data-nosnippet>327</a>            .flat_map(|ix| {
<a href=#328 id=328 data-nosnippet>328</a>                ix.accounts
<a href=#329 id=329 data-nosnippet>329</a>                    .iter()
<a href=#330 id=330 data-nosnippet>330</a>                    .copied()
<a href=#331 id=331 data-nosnippet>331</a>                    .map(usize::from)
<a href=#332 id=332 data-nosnippet>332</a>                    .filter(|index| <span class="self">self</span>.is_signer(<span class="kw-2">*</span>index))
<a href=#333 id=333 data-nosnippet>333</a>                    .filter_map(|signer_index| <span class="self">self</span>.account_keys().get(signer_index))
<a href=#334 id=334 data-nosnippet>334</a>            })
<a href=#335 id=335 data-nosnippet>335</a>    }
<a href=#336 id=336 data-nosnippet>336</a>
<a href=#337 id=337 data-nosnippet>337</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#338 id=338 data-nosnippet>338</a>    </span><span class="doccomment">/// If the message uses a durable nonce, return the pubkey of the nonce account
<a href=#339 id=339 data-nosnippet>339</a>    </span><span class="kw">pub fn </span>get_durable_nonce(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#340 id=340 data-nosnippet>340</a>        <span class="self">self</span>.instructions()
<a href=#341 id=341 data-nosnippet>341</a>            .get(NONCED_TX_MARKER_IX_INDEX <span class="kw">as </span>usize)
<a href=#342 id=342 data-nosnippet>342</a>            .filter(
<a href=#343 id=343 data-nosnippet>343</a>                |ix| <span class="kw">match </span><span class="self">self</span>.account_keys().get(ix.program_id_index <span class="kw">as </span>usize) {
<a href=#344 id=344 data-nosnippet>344</a>                    <span class="prelude-val">Some</span>(program_id) =&gt; solana_sdk_ids::system_program::check_id(program_id),
<a href=#345 id=345 data-nosnippet>345</a>                    <span class="kw">_ </span>=&gt; <span class="bool-val">false</span>,
<a href=#346 id=346 data-nosnippet>346</a>                },
<a href=#347 id=347 data-nosnippet>347</a>            )
<a href=#348 id=348 data-nosnippet>348</a>            .filter(|ix| {
<a href=#349 id=349 data-nosnippet>349</a>                <span class="macro">matches!</span>(
<a href=#350 id=350 data-nosnippet>350</a>                    solana_bincode::limited_deserialize(
<a href=#351 id=351 data-nosnippet>351</a>                        <span class="kw-2">&amp;</span>ix.data, <span class="number">4 </span><span class="comment">/* serialized size of AdvanceNonceAccount */
<a href=#352 id=352 data-nosnippet>352</a>                    </span>),
<a href=#353 id=353 data-nosnippet>353</a>                    <span class="prelude-val">Ok</span>(solana_system_interface::instruction::SystemInstruction::AdvanceNonceAccount)
<a href=#354 id=354 data-nosnippet>354</a>                )
<a href=#355 id=355 data-nosnippet>355</a>            })
<a href=#356 id=356 data-nosnippet>356</a>            .and_then(|ix| {
<a href=#357 id=357 data-nosnippet>357</a>                ix.accounts.first().and_then(|idx| {
<a href=#358 id=358 data-nosnippet>358</a>                    <span class="kw">let </span>idx = <span class="kw-2">*</span>idx <span class="kw">as </span>usize;
<a href=#359 id=359 data-nosnippet>359</a>                    <span class="kw">if </span>!<span class="self">self</span>.is_writable(idx) {
<a href=#360 id=360 data-nosnippet>360</a>                        <span class="prelude-val">None
<a href=#361 id=361 data-nosnippet>361</a>                    </span>} <span class="kw">else </span>{
<a href=#362 id=362 data-nosnippet>362</a>                        <span class="self">self</span>.account_keys().get(idx)
<a href=#363 id=363 data-nosnippet>363</a>                    }
<a href=#364 id=364 data-nosnippet>364</a>                })
<a href=#365 id=365 data-nosnippet>365</a>            })
<a href=#366 id=366 data-nosnippet>366</a>    }
<a href=#367 id=367 data-nosnippet>367</a>
<a href=#368 id=368 data-nosnippet>368</a>    <span class="attr">#[deprecated(
<a href=#369 id=369 data-nosnippet>369</a>        since = <span class="string">"2.1.0"</span>,
<a href=#370 id=370 data-nosnippet>370</a>        note = <span class="string">"Please use `SanitizedMessage::num_total_signatures` instead."
<a href=#371 id=371 data-nosnippet>371</a>    </span>)]
<a href=#372 id=372 data-nosnippet>372</a>    </span><span class="kw">pub fn </span>num_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#373 id=373 data-nosnippet>373</a>        <span class="self">self</span>.num_total_signatures()
<a href=#374 id=374 data-nosnippet>374</a>    }
<a href=#375 id=375 data-nosnippet>375</a>
<a href=#376 id=376 data-nosnippet>376</a>    <span class="doccomment">/// Returns the total number of signatures in the message.
<a href=#377 id=377 data-nosnippet>377</a>    /// This includes required transaction signatures as well as any
<a href=#378 id=378 data-nosnippet>378</a>    /// pre-compile signatures that are attached in instructions.
<a href=#379 id=379 data-nosnippet>379</a>    </span><span class="kw">pub fn </span>num_total_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#380 id=380 data-nosnippet>380</a>        <span class="self">self</span>.get_signature_details().total_signatures()
<a href=#381 id=381 data-nosnippet>381</a>    }
<a href=#382 id=382 data-nosnippet>382</a>
<a href=#383 id=383 data-nosnippet>383</a>    <span class="doccomment">/// Returns the number of requested write-locks in this message.
<a href=#384 id=384 data-nosnippet>384</a>    /// This does not consider if write-locks are demoted.
<a href=#385 id=385 data-nosnippet>385</a>    </span><span class="kw">pub fn </span>num_write_locks(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#386 id=386 data-nosnippet>386</a>        <span class="self">self</span>.account_keys()
<a href=#387 id=387 data-nosnippet>387</a>            .len()
<a href=#388 id=388 data-nosnippet>388</a>            .saturating_sub(<span class="self">self</span>.num_readonly_accounts()) <span class="kw">as </span>u64
<a href=#389 id=389 data-nosnippet>389</a>    }
<a href=#390 id=390 data-nosnippet>390</a>
<a href=#391 id=391 data-nosnippet>391</a>    <span class="doccomment">/// return detailed signature counts
<a href=#392 id=392 data-nosnippet>392</a>    </span><span class="kw">pub fn </span>get_signature_details(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; TransactionSignatureDetails {
<a href=#393 id=393 data-nosnippet>393</a>        <span class="kw">let </span><span class="kw-2">mut </span>transaction_signature_details = TransactionSignatureDetails {
<a href=#394 id=394 data-nosnippet>394</a>            num_transaction_signatures: u64::from(<span class="self">self</span>.header().num_required_signatures),
<a href=#395 id=395 data-nosnippet>395</a>            ..TransactionSignatureDetails::default()
<a href=#396 id=396 data-nosnippet>396</a>        };
<a href=#397 id=397 data-nosnippet>397</a>
<a href=#398 id=398 data-nosnippet>398</a>        <span class="comment">// counting the number of pre-processor operations separately
<a href=#399 id=399 data-nosnippet>399</a>        </span><span class="kw">for </span>(program_id, instruction) <span class="kw">in </span><span class="self">self</span>.program_instructions_iter() {
<a href=#400 id=400 data-nosnippet>400</a>            <span class="kw">if </span>secp256k1_program::check_id(program_id) {
<a href=#401 id=401 data-nosnippet>401</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(num_verifies) = instruction.data.first() {
<a href=#402 id=402 data-nosnippet>402</a>                    transaction_signature_details.num_secp256k1_instruction_signatures =
<a href=#403 id=403 data-nosnippet>403</a>                        transaction_signature_details
<a href=#404 id=404 data-nosnippet>404</a>                            .num_secp256k1_instruction_signatures
<a href=#405 id=405 data-nosnippet>405</a>                            .saturating_add(u64::from(<span class="kw-2">*</span>num_verifies));
<a href=#406 id=406 data-nosnippet>406</a>                }
<a href=#407 id=407 data-nosnippet>407</a>            } <span class="kw">else if </span>ed25519_program::check_id(program_id) {
<a href=#408 id=408 data-nosnippet>408</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(num_verifies) = instruction.data.first() {
<a href=#409 id=409 data-nosnippet>409</a>                    transaction_signature_details.num_ed25519_instruction_signatures =
<a href=#410 id=410 data-nosnippet>410</a>                        transaction_signature_details
<a href=#411 id=411 data-nosnippet>411</a>                            .num_ed25519_instruction_signatures
<a href=#412 id=412 data-nosnippet>412</a>                            .saturating_add(u64::from(<span class="kw-2">*</span>num_verifies));
<a href=#413 id=413 data-nosnippet>413</a>                }
<a href=#414 id=414 data-nosnippet>414</a>            } <span class="kw">else if </span>secp256r1_program::check_id(program_id) {
<a href=#415 id=415 data-nosnippet>415</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(num_verifies) = instruction.data.first() {
<a href=#416 id=416 data-nosnippet>416</a>                    transaction_signature_details.num_secp256r1_instruction_signatures =
<a href=#417 id=417 data-nosnippet>417</a>                        transaction_signature_details
<a href=#418 id=418 data-nosnippet>418</a>                            .num_secp256r1_instruction_signatures
<a href=#419 id=419 data-nosnippet>419</a>                            .saturating_add(u64::from(<span class="kw-2">*</span>num_verifies));
<a href=#420 id=420 data-nosnippet>420</a>                }
<a href=#421 id=421 data-nosnippet>421</a>            }
<a href=#422 id=422 data-nosnippet>422</a>        }
<a href=#423 id=423 data-nosnippet>423</a>
<a href=#424 id=424 data-nosnippet>424</a>        transaction_signature_details
<a href=#425 id=425 data-nosnippet>425</a>    }
<a href=#426 id=426 data-nosnippet>426</a>}
<a href=#427 id=427 data-nosnippet>427</a>
<a href=#428 id=428 data-nosnippet>428</a><span class="doccomment">/// Transaction signature details including the number of transaction signatures
<a href=#429 id=429 data-nosnippet>429</a>/// and precompile signatures.
<a href=#430 id=430 data-nosnippet>430</a></span><span class="attr">#[derive(Clone, Debug, Default)]
<a href=#431 id=431 data-nosnippet>431</a></span><span class="kw">pub struct </span>TransactionSignatureDetails {
<a href=#432 id=432 data-nosnippet>432</a>    num_transaction_signatures: u64,
<a href=#433 id=433 data-nosnippet>433</a>    num_secp256k1_instruction_signatures: u64,
<a href=#434 id=434 data-nosnippet>434</a>    num_ed25519_instruction_signatures: u64,
<a href=#435 id=435 data-nosnippet>435</a>    num_secp256r1_instruction_signatures: u64,
<a href=#436 id=436 data-nosnippet>436</a>}
<a href=#437 id=437 data-nosnippet>437</a>
<a href=#438 id=438 data-nosnippet>438</a><span class="kw">impl </span>TransactionSignatureDetails {
<a href=#439 id=439 data-nosnippet>439</a>    <span class="kw">pub const fn </span>new(
<a href=#440 id=440 data-nosnippet>440</a>        num_transaction_signatures: u64,
<a href=#441 id=441 data-nosnippet>441</a>        num_secp256k1_instruction_signatures: u64,
<a href=#442 id=442 data-nosnippet>442</a>        num_ed25519_instruction_signatures: u64,
<a href=#443 id=443 data-nosnippet>443</a>        num_secp256r1_instruction_signatures: u64,
<a href=#444 id=444 data-nosnippet>444</a>    ) -&gt; <span class="self">Self </span>{
<a href=#445 id=445 data-nosnippet>445</a>        <span class="self">Self </span>{
<a href=#446 id=446 data-nosnippet>446</a>            num_transaction_signatures,
<a href=#447 id=447 data-nosnippet>447</a>            num_secp256k1_instruction_signatures,
<a href=#448 id=448 data-nosnippet>448</a>            num_ed25519_instruction_signatures,
<a href=#449 id=449 data-nosnippet>449</a>            num_secp256r1_instruction_signatures,
<a href=#450 id=450 data-nosnippet>450</a>        }
<a href=#451 id=451 data-nosnippet>451</a>    }
<a href=#452 id=452 data-nosnippet>452</a>
<a href=#453 id=453 data-nosnippet>453</a>    <span class="doccomment">/// return total number of signature, treating pre-processor operations as signature
<a href=#454 id=454 data-nosnippet>454</a>    </span><span class="kw">pub fn </span>total_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#455 id=455 data-nosnippet>455</a>        <span class="self">self</span>.num_transaction_signatures
<a href=#456 id=456 data-nosnippet>456</a>            .saturating_add(<span class="self">self</span>.num_secp256k1_instruction_signatures)
<a href=#457 id=457 data-nosnippet>457</a>            .saturating_add(<span class="self">self</span>.num_ed25519_instruction_signatures)
<a href=#458 id=458 data-nosnippet>458</a>            .saturating_add(<span class="self">self</span>.num_secp256r1_instruction_signatures)
<a href=#459 id=459 data-nosnippet>459</a>    }
<a href=#460 id=460 data-nosnippet>460</a>
<a href=#461 id=461 data-nosnippet>461</a>    <span class="doccomment">/// return the number of transaction signatures
<a href=#462 id=462 data-nosnippet>462</a>    </span><span class="kw">pub fn </span>num_transaction_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#463 id=463 data-nosnippet>463</a>        <span class="self">self</span>.num_transaction_signatures
<a href=#464 id=464 data-nosnippet>464</a>    }
<a href=#465 id=465 data-nosnippet>465</a>
<a href=#466 id=466 data-nosnippet>466</a>    <span class="doccomment">/// return the number of secp256k1 instruction signatures
<a href=#467 id=467 data-nosnippet>467</a>    </span><span class="kw">pub fn </span>num_secp256k1_instruction_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#468 id=468 data-nosnippet>468</a>        <span class="self">self</span>.num_secp256k1_instruction_signatures
<a href=#469 id=469 data-nosnippet>469</a>    }
<a href=#470 id=470 data-nosnippet>470</a>
<a href=#471 id=471 data-nosnippet>471</a>    <span class="doccomment">/// return the number of ed25519 instruction signatures
<a href=#472 id=472 data-nosnippet>472</a>    </span><span class="kw">pub fn </span>num_ed25519_instruction_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#473 id=473 data-nosnippet>473</a>        <span class="self">self</span>.num_ed25519_instruction_signatures
<a href=#474 id=474 data-nosnippet>474</a>    }
<a href=#475 id=475 data-nosnippet>475</a>
<a href=#476 id=476 data-nosnippet>476</a>    <span class="doccomment">/// return the number of secp256r1 instruction signatures
<a href=#477 id=477 data-nosnippet>477</a>    </span><span class="kw">pub fn </span>num_secp256r1_instruction_signatures(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; u64 {
<a href=#478 id=478 data-nosnippet>478</a>        <span class="self">self</span>.num_secp256r1_instruction_signatures
<a href=#479 id=479 data-nosnippet>479</a>    }
<a href=#480 id=480 data-nosnippet>480</a>}
<a href=#481 id=481 data-nosnippet>481</a>
<a href=#482 id=482 data-nosnippet>482</a><span class="attr">#[cfg(test)]
<a href=#483 id=483 data-nosnippet>483</a></span><span class="kw">mod </span>tests {
<a href=#484 id=484 data-nosnippet>484</a>    <span class="kw">use </span>{<span class="kw">super</span>::<span class="kw-2">*</span>, <span class="kw">crate</span>::v0, std::collections::HashSet};
<a href=#485 id=485 data-nosnippet>485</a>
<a href=#486 id=486 data-nosnippet>486</a>    <span class="attr">#[test]
<a href=#487 id=487 data-nosnippet>487</a>    </span><span class="kw">fn </span>test_try_from_legacy_message() {
<a href=#488 id=488 data-nosnippet>488</a>        <span class="kw">let </span>legacy_message_with_no_signers = legacy::Message {
<a href=#489 id=489 data-nosnippet>489</a>            account_keys: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#490 id=490 data-nosnippet>490</a>            ..legacy::Message::default()
<a href=#491 id=491 data-nosnippet>491</a>        };
<a href=#492 id=492 data-nosnippet>492</a>
<a href=#493 id=493 data-nosnippet>493</a>        <span class="macro">assert_eq!</span>(
<a href=#494 id=494 data-nosnippet>494</a>            SanitizedMessage::try_from_legacy_message(
<a href=#495 id=495 data-nosnippet>495</a>                legacy_message_with_no_signers,
<a href=#496 id=496 data-nosnippet>496</a>                <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#497 id=497 data-nosnippet>497</a>            )
<a href=#498 id=498 data-nosnippet>498</a>            .err(),
<a href=#499 id=499 data-nosnippet>499</a>            <span class="prelude-val">Some</span>(SanitizeMessageError::IndexOutOfBounds),
<a href=#500 id=500 data-nosnippet>500</a>        );
<a href=#501 id=501 data-nosnippet>501</a>    }
<a href=#502 id=502 data-nosnippet>502</a>
<a href=#503 id=503 data-nosnippet>503</a>    <span class="attr">#[test]
<a href=#504 id=504 data-nosnippet>504</a>    </span><span class="kw">fn </span>test_is_non_loader_key() {
<a href=#505 id=505 data-nosnippet>505</a>        <span class="attr">#![allow(deprecated)]
<a href=#506 id=506 data-nosnippet>506</a>        </span><span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#507 id=507 data-nosnippet>507</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#508 id=508 data-nosnippet>508</a>        <span class="kw">let </span>loader_key = Pubkey::new_unique();
<a href=#509 id=509 data-nosnippet>509</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[
<a href=#510 id=510 data-nosnippet>510</a>            CompiledInstruction::new(<span class="number">1</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>]),
<a href=#511 id=511 data-nosnippet>511</a>            CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>]),
<a href=#512 id=512 data-nosnippet>512</a>        ];
<a href=#513 id=513 data-nosnippet>513</a>
<a href=#514 id=514 data-nosnippet>514</a>        <span class="kw">let </span>message = SanitizedMessage::try_from_legacy_message(
<a href=#515 id=515 data-nosnippet>515</a>            legacy::Message::new_with_compiled_instructions(
<a href=#516 id=516 data-nosnippet>516</a>                <span class="number">1</span>,
<a href=#517 id=517 data-nosnippet>517</a>                <span class="number">0</span>,
<a href=#518 id=518 data-nosnippet>518</a>                <span class="number">2</span>,
<a href=#519 id=519 data-nosnippet>519</a>                <span class="macro">vec!</span>[key0, key1, loader_key],
<a href=#520 id=520 data-nosnippet>520</a>                Hash::default(),
<a href=#521 id=521 data-nosnippet>521</a>                instructions,
<a href=#522 id=522 data-nosnippet>522</a>            ),
<a href=#523 id=523 data-nosnippet>523</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#524 id=524 data-nosnippet>524</a>        )
<a href=#525 id=525 data-nosnippet>525</a>        .unwrap();
<a href=#526 id=526 data-nosnippet>526</a>
<a href=#527 id=527 data-nosnippet>527</a>        <span class="macro">assert!</span>(message.is_non_loader_key(<span class="number">0</span>));
<a href=#528 id=528 data-nosnippet>528</a>        <span class="macro">assert!</span>(message.is_non_loader_key(<span class="number">1</span>));
<a href=#529 id=529 data-nosnippet>529</a>        <span class="macro">assert!</span>(!message.is_non_loader_key(<span class="number">2</span>));
<a href=#530 id=530 data-nosnippet>530</a>    }
<a href=#531 id=531 data-nosnippet>531</a>
<a href=#532 id=532 data-nosnippet>532</a>    <span class="attr">#[test]
<a href=#533 id=533 data-nosnippet>533</a>    </span><span class="kw">fn </span>test_num_readonly_accounts() {
<a href=#534 id=534 data-nosnippet>534</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#535 id=535 data-nosnippet>535</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#536 id=536 data-nosnippet>536</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#537 id=537 data-nosnippet>537</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#538 id=538 data-nosnippet>538</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#539 id=539 data-nosnippet>539</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#540 id=540 data-nosnippet>540</a>
<a href=#541 id=541 data-nosnippet>541</a>        <span class="kw">let </span>legacy_message = SanitizedMessage::try_from_legacy_message(
<a href=#542 id=542 data-nosnippet>542</a>            legacy::Message {
<a href=#543 id=543 data-nosnippet>543</a>                header: MessageHeader {
<a href=#544 id=544 data-nosnippet>544</a>                    num_required_signatures: <span class="number">2</span>,
<a href=#545 id=545 data-nosnippet>545</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#546 id=546 data-nosnippet>546</a>                    num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#547 id=547 data-nosnippet>547</a>                },
<a href=#548 id=548 data-nosnippet>548</a>                account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3],
<a href=#549 id=549 data-nosnippet>549</a>                ..legacy::Message::default()
<a href=#550 id=550 data-nosnippet>550</a>            },
<a href=#551 id=551 data-nosnippet>551</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#552 id=552 data-nosnippet>552</a>        )
<a href=#553 id=553 data-nosnippet>553</a>        .unwrap();
<a href=#554 id=554 data-nosnippet>554</a>
<a href=#555 id=555 data-nosnippet>555</a>        <span class="macro">assert_eq!</span>(legacy_message.num_readonly_accounts(), <span class="number">2</span>);
<a href=#556 id=556 data-nosnippet>556</a>
<a href=#557 id=557 data-nosnippet>557</a>        <span class="kw">let </span>v0_message = SanitizedMessage::V0(v0::LoadedMessage::new(
<a href=#558 id=558 data-nosnippet>558</a>            v0::Message {
<a href=#559 id=559 data-nosnippet>559</a>                header: MessageHeader {
<a href=#560 id=560 data-nosnippet>560</a>                    num_required_signatures: <span class="number">2</span>,
<a href=#561 id=561 data-nosnippet>561</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#562 id=562 data-nosnippet>562</a>                    num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#563 id=563 data-nosnippet>563</a>                },
<a href=#564 id=564 data-nosnippet>564</a>                account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3],
<a href=#565 id=565 data-nosnippet>565</a>                ..v0::Message::default()
<a href=#566 id=566 data-nosnippet>566</a>            },
<a href=#567 id=567 data-nosnippet>567</a>            LoadedAddresses {
<a href=#568 id=568 data-nosnippet>568</a>                writable: <span class="macro">vec!</span>[key4],
<a href=#569 id=569 data-nosnippet>569</a>                readonly: <span class="macro">vec!</span>[key5],
<a href=#570 id=570 data-nosnippet>570</a>            },
<a href=#571 id=571 data-nosnippet>571</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#572 id=572 data-nosnippet>572</a>        ));
<a href=#573 id=573 data-nosnippet>573</a>
<a href=#574 id=574 data-nosnippet>574</a>        <span class="macro">assert_eq!</span>(v0_message.num_readonly_accounts(), <span class="number">3</span>);
<a href=#575 id=575 data-nosnippet>575</a>    }
<a href=#576 id=576 data-nosnippet>576</a>
<a href=#577 id=577 data-nosnippet>577</a>    <span class="attr">#[test]
<a href=#578 id=578 data-nosnippet>578</a>    </span><span class="kw">fn </span>test_get_ix_signers() {
<a href=#579 id=579 data-nosnippet>579</a>        <span class="kw">let </span>signer0 = Pubkey::new_unique();
<a href=#580 id=580 data-nosnippet>580</a>        <span class="kw">let </span>signer1 = Pubkey::new_unique();
<a href=#581 id=581 data-nosnippet>581</a>        <span class="kw">let </span>non_signer = Pubkey::new_unique();
<a href=#582 id=582 data-nosnippet>582</a>        <span class="kw">let </span>loader_key = Pubkey::new_unique();
<a href=#583 id=583 data-nosnippet>583</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[
<a href=#584 id=584 data-nosnippet>584</a>            CompiledInstruction::new(<span class="number">3</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">2</span>, <span class="number">0</span>]),
<a href=#585 id=585 data-nosnippet>585</a>            CompiledInstruction::new(<span class="number">3</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>]),
<a href=#586 id=586 data-nosnippet>586</a>            CompiledInstruction::new(<span class="number">3</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">0</span>]),
<a href=#587 id=587 data-nosnippet>587</a>        ];
<a href=#588 id=588 data-nosnippet>588</a>
<a href=#589 id=589 data-nosnippet>589</a>        <span class="kw">let </span>message = SanitizedMessage::try_from_legacy_message(
<a href=#590 id=590 data-nosnippet>590</a>            legacy::Message::new_with_compiled_instructions(
<a href=#591 id=591 data-nosnippet>591</a>                <span class="number">2</span>,
<a href=#592 id=592 data-nosnippet>592</a>                <span class="number">1</span>,
<a href=#593 id=593 data-nosnippet>593</a>                <span class="number">2</span>,
<a href=#594 id=594 data-nosnippet>594</a>                <span class="macro">vec!</span>[signer0, signer1, non_signer, loader_key],
<a href=#595 id=595 data-nosnippet>595</a>                Hash::default(),
<a href=#596 id=596 data-nosnippet>596</a>                instructions,
<a href=#597 id=597 data-nosnippet>597</a>            ),
<a href=#598 id=598 data-nosnippet>598</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#599 id=599 data-nosnippet>599</a>        )
<a href=#600 id=600 data-nosnippet>600</a>        .unwrap();
<a href=#601 id=601 data-nosnippet>601</a>
<a href=#602 id=602 data-nosnippet>602</a>        <span class="macro">assert_eq!</span>(
<a href=#603 id=603 data-nosnippet>603</a>            message.get_ix_signers(<span class="number">0</span>).collect::&lt;HashSet&lt;<span class="kw">_</span>&gt;&gt;(),
<a href=#604 id=604 data-nosnippet>604</a>            HashSet::from_iter([<span class="kw-2">&amp;</span>signer0])
<a href=#605 id=605 data-nosnippet>605</a>        );
<a href=#606 id=606 data-nosnippet>606</a>        <span class="macro">assert_eq!</span>(
<a href=#607 id=607 data-nosnippet>607</a>            message.get_ix_signers(<span class="number">1</span>).collect::&lt;HashSet&lt;<span class="kw">_</span>&gt;&gt;(),
<a href=#608 id=608 data-nosnippet>608</a>            HashSet::from_iter([<span class="kw-2">&amp;</span>signer0, <span class="kw-2">&amp;</span>signer1])
<a href=#609 id=609 data-nosnippet>609</a>        );
<a href=#610 id=610 data-nosnippet>610</a>        <span class="macro">assert_eq!</span>(
<a href=#611 id=611 data-nosnippet>611</a>            message.get_ix_signers(<span class="number">2</span>).collect::&lt;HashSet&lt;<span class="kw">_</span>&gt;&gt;(),
<a href=#612 id=612 data-nosnippet>612</a>            HashSet::from_iter([<span class="kw-2">&amp;</span>signer0])
<a href=#613 id=613 data-nosnippet>613</a>        );
<a href=#614 id=614 data-nosnippet>614</a>        <span class="macro">assert_eq!</span>(
<a href=#615 id=615 data-nosnippet>615</a>            message.get_ix_signers(<span class="number">3</span>).collect::&lt;HashSet&lt;<span class="kw">_</span>&gt;&gt;(),
<a href=#616 id=616 data-nosnippet>616</a>            HashSet::default()
<a href=#617 id=617 data-nosnippet>617</a>        );
<a href=#618 id=618 data-nosnippet>618</a>    }
<a href=#619 id=619 data-nosnippet>619</a>
<a href=#620 id=620 data-nosnippet>620</a>    <span class="attr">#[test]
<a href=#621 id=621 data-nosnippet>621</a>    #[allow(clippy::get_first)]
<a href=#622 id=622 data-nosnippet>622</a>    </span><span class="kw">fn </span>test_is_writable_account_cache() {
<a href=#623 id=623 data-nosnippet>623</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#624 id=624 data-nosnippet>624</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#625 id=625 data-nosnippet>625</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#626 id=626 data-nosnippet>626</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#627 id=627 data-nosnippet>627</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#628 id=628 data-nosnippet>628</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#629 id=629 data-nosnippet>629</a>
<a href=#630 id=630 data-nosnippet>630</a>        <span class="kw">let </span>legacy_message = SanitizedMessage::try_from_legacy_message(
<a href=#631 id=631 data-nosnippet>631</a>            legacy::Message {
<a href=#632 id=632 data-nosnippet>632</a>                header: MessageHeader {
<a href=#633 id=633 data-nosnippet>633</a>                    num_required_signatures: <span class="number">2</span>,
<a href=#634 id=634 data-nosnippet>634</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#635 id=635 data-nosnippet>635</a>                    num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#636 id=636 data-nosnippet>636</a>                },
<a href=#637 id=637 data-nosnippet>637</a>                account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3],
<a href=#638 id=638 data-nosnippet>638</a>                ..legacy::Message::default()
<a href=#639 id=639 data-nosnippet>639</a>            },
<a href=#640 id=640 data-nosnippet>640</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#641 id=641 data-nosnippet>641</a>        )
<a href=#642 id=642 data-nosnippet>642</a>        .unwrap();
<a href=#643 id=643 data-nosnippet>643</a>        <span class="kw">match </span>legacy_message {
<a href=#644 id=644 data-nosnippet>644</a>            SanitizedMessage::Legacy(message) =&gt; {
<a href=#645 id=645 data-nosnippet>645</a>                <span class="macro">assert_eq!</span>(
<a href=#646 id=646 data-nosnippet>646</a>                    message.is_writable_account_cache.len(),
<a href=#647 id=647 data-nosnippet>647</a>                    message.account_keys().len()
<a href=#648 id=648 data-nosnippet>648</a>                );
<a href=#649 id=649 data-nosnippet>649</a>                <span class="macro">assert!</span>(message.is_writable_account_cache.get(<span class="number">0</span>).unwrap());
<a href=#650 id=650 data-nosnippet>650</a>                <span class="macro">assert!</span>(!message.is_writable_account_cache.get(<span class="number">1</span>).unwrap());
<a href=#651 id=651 data-nosnippet>651</a>                <span class="macro">assert!</span>(message.is_writable_account_cache.get(<span class="number">2</span>).unwrap());
<a href=#652 id=652 data-nosnippet>652</a>                <span class="macro">assert!</span>(!message.is_writable_account_cache.get(<span class="number">3</span>).unwrap());
<a href=#653 id=653 data-nosnippet>653</a>            }
<a href=#654 id=654 data-nosnippet>654</a>            <span class="kw">_ </span>=&gt; {
<a href=#655 id=655 data-nosnippet>655</a>                <span class="macro">panic!</span>(<span class="string">"Expect to be SanitizedMessage::LegacyMessage"</span>)
<a href=#656 id=656 data-nosnippet>656</a>            }
<a href=#657 id=657 data-nosnippet>657</a>        }
<a href=#658 id=658 data-nosnippet>658</a>
<a href=#659 id=659 data-nosnippet>659</a>        <span class="kw">let </span>v0_message = SanitizedMessage::V0(v0::LoadedMessage::new(
<a href=#660 id=660 data-nosnippet>660</a>            v0::Message {
<a href=#661 id=661 data-nosnippet>661</a>                header: MessageHeader {
<a href=#662 id=662 data-nosnippet>662</a>                    num_required_signatures: <span class="number">2</span>,
<a href=#663 id=663 data-nosnippet>663</a>                    num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#664 id=664 data-nosnippet>664</a>                    num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#665 id=665 data-nosnippet>665</a>                },
<a href=#666 id=666 data-nosnippet>666</a>                account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3],
<a href=#667 id=667 data-nosnippet>667</a>                ..v0::Message::default()
<a href=#668 id=668 data-nosnippet>668</a>            },
<a href=#669 id=669 data-nosnippet>669</a>            LoadedAddresses {
<a href=#670 id=670 data-nosnippet>670</a>                writable: <span class="macro">vec!</span>[key4],
<a href=#671 id=671 data-nosnippet>671</a>                readonly: <span class="macro">vec!</span>[key5],
<a href=#672 id=672 data-nosnippet>672</a>            },
<a href=#673 id=673 data-nosnippet>673</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#674 id=674 data-nosnippet>674</a>        ));
<a href=#675 id=675 data-nosnippet>675</a>        <span class="kw">match </span>v0_message {
<a href=#676 id=676 data-nosnippet>676</a>            SanitizedMessage::V0(message) =&gt; {
<a href=#677 id=677 data-nosnippet>677</a>                <span class="macro">assert_eq!</span>(
<a href=#678 id=678 data-nosnippet>678</a>                    message.is_writable_account_cache.len(),
<a href=#679 id=679 data-nosnippet>679</a>                    message.account_keys().len()
<a href=#680 id=680 data-nosnippet>680</a>                );
<a href=#681 id=681 data-nosnippet>681</a>                <span class="macro">assert!</span>(message.is_writable_account_cache.get(<span class="number">0</span>).unwrap());
<a href=#682 id=682 data-nosnippet>682</a>                <span class="macro">assert!</span>(!message.is_writable_account_cache.get(<span class="number">1</span>).unwrap());
<a href=#683 id=683 data-nosnippet>683</a>                <span class="macro">assert!</span>(message.is_writable_account_cache.get(<span class="number">2</span>).unwrap());
<a href=#684 id=684 data-nosnippet>684</a>                <span class="macro">assert!</span>(!message.is_writable_account_cache.get(<span class="number">3</span>).unwrap());
<a href=#685 id=685 data-nosnippet>685</a>                <span class="macro">assert!</span>(message.is_writable_account_cache.get(<span class="number">4</span>).unwrap());
<a href=#686 id=686 data-nosnippet>686</a>                <span class="macro">assert!</span>(!message.is_writable_account_cache.get(<span class="number">5</span>).unwrap());
<a href=#687 id=687 data-nosnippet>687</a>            }
<a href=#688 id=688 data-nosnippet>688</a>            <span class="kw">_ </span>=&gt; {
<a href=#689 id=689 data-nosnippet>689</a>                <span class="macro">panic!</span>(<span class="string">"Expect to be SanitizedMessage::V0"</span>)
<a href=#690 id=690 data-nosnippet>690</a>            }
<a href=#691 id=691 data-nosnippet>691</a>        }
<a href=#692 id=692 data-nosnippet>692</a>    }
<a href=#693 id=693 data-nosnippet>693</a>
<a href=#694 id=694 data-nosnippet>694</a>    <span class="attr">#[test]
<a href=#695 id=695 data-nosnippet>695</a>    </span><span class="kw">fn </span>test_get_signature_details() {
<a href=#696 id=696 data-nosnippet>696</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#697 id=697 data-nosnippet>697</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#698 id=698 data-nosnippet>698</a>        <span class="kw">let </span>loader_key = Pubkey::new_unique();
<a href=#699 id=699 data-nosnippet>699</a>
<a href=#700 id=700 data-nosnippet>700</a>        <span class="kw">let </span>loader_instr = CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>]);
<a href=#701 id=701 data-nosnippet>701</a>        <span class="kw">let </span>mock_secp256k1_instr = CompiledInstruction::new(<span class="number">3</span>, <span class="kw-2">&amp;</span>[<span class="number">1u8</span>; <span class="number">10</span>], <span class="macro">vec!</span>[]);
<a href=#702 id=702 data-nosnippet>702</a>        <span class="kw">let </span>mock_ed25519_instr = CompiledInstruction::new(<span class="number">4</span>, <span class="kw-2">&amp;</span>[<span class="number">5u8</span>; <span class="number">10</span>], <span class="macro">vec!</span>[]);
<a href=#703 id=703 data-nosnippet>703</a>
<a href=#704 id=704 data-nosnippet>704</a>        <span class="kw">let </span>message = SanitizedMessage::try_from_legacy_message(
<a href=#705 id=705 data-nosnippet>705</a>            legacy::Message::new_with_compiled_instructions(
<a href=#706 id=706 data-nosnippet>706</a>                <span class="number">2</span>,
<a href=#707 id=707 data-nosnippet>707</a>                <span class="number">1</span>,
<a href=#708 id=708 data-nosnippet>708</a>                <span class="number">2</span>,
<a href=#709 id=709 data-nosnippet>709</a>                <span class="macro">vec!</span>[
<a href=#710 id=710 data-nosnippet>710</a>                    key0,
<a href=#711 id=711 data-nosnippet>711</a>                    key1,
<a href=#712 id=712 data-nosnippet>712</a>                    loader_key,
<a href=#713 id=713 data-nosnippet>713</a>                    secp256k1_program::id(),
<a href=#714 id=714 data-nosnippet>714</a>                    ed25519_program::id(),
<a href=#715 id=715 data-nosnippet>715</a>                ],
<a href=#716 id=716 data-nosnippet>716</a>                Hash::default(),
<a href=#717 id=717 data-nosnippet>717</a>                <span class="macro">vec!</span>[
<a href=#718 id=718 data-nosnippet>718</a>                    loader_instr,
<a href=#719 id=719 data-nosnippet>719</a>                    mock_secp256k1_instr.clone(),
<a href=#720 id=720 data-nosnippet>720</a>                    mock_ed25519_instr,
<a href=#721 id=721 data-nosnippet>721</a>                    mock_secp256k1_instr,
<a href=#722 id=722 data-nosnippet>722</a>                ],
<a href=#723 id=723 data-nosnippet>723</a>            ),
<a href=#724 id=724 data-nosnippet>724</a>            <span class="kw-2">&amp;</span>HashSet::new(),
<a href=#725 id=725 data-nosnippet>725</a>        )
<a href=#726 id=726 data-nosnippet>726</a>        .unwrap();
<a href=#727 id=727 data-nosnippet>727</a>
<a href=#728 id=728 data-nosnippet>728</a>        <span class="kw">let </span>signature_details = message.get_signature_details();
<a href=#729 id=729 data-nosnippet>729</a>        <span class="comment">// expect 2 required transaction signatures
<a href=#730 id=730 data-nosnippet>730</a>        </span><span class="macro">assert_eq!</span>(<span class="number">2</span>, signature_details.num_transaction_signatures);
<a href=#731 id=731 data-nosnippet>731</a>        <span class="comment">// expect 2 secp256k1 instruction signatures - 1 for each mock_secp2561k1_instr
<a href=#732 id=732 data-nosnippet>732</a>        </span><span class="macro">assert_eq!</span>(<span class="number">2</span>, signature_details.num_secp256k1_instruction_signatures);
<a href=#733 id=733 data-nosnippet>733</a>        <span class="comment">// expect 5 ed25519 instruction signatures from mock_ed25519_instr
<a href=#734 id=734 data-nosnippet>734</a>        </span><span class="macro">assert_eq!</span>(<span class="number">5</span>, signature_details.num_ed25519_instruction_signatures);
<a href=#735 id=735 data-nosnippet>735</a>    }
<a href=#736 id=736 data-nosnippet>736</a>
<a href=#737 id=737 data-nosnippet>737</a>    <span class="attr">#[test]
<a href=#738 id=738 data-nosnippet>738</a>    </span><span class="kw">fn </span>test_static_account_keys() {
<a href=#739 id=739 data-nosnippet>739</a>        <span class="kw">let </span>keys = <span class="macro">vec!</span>[
<a href=#740 id=740 data-nosnippet>740</a>            Pubkey::new_unique(),
<a href=#741 id=741 data-nosnippet>741</a>            Pubkey::new_unique(),
<a href=#742 id=742 data-nosnippet>742</a>            Pubkey::new_unique(),
<a href=#743 id=743 data-nosnippet>743</a>        ];
<a href=#744 id=744 data-nosnippet>744</a>
<a href=#745 id=745 data-nosnippet>745</a>        <span class="kw">let </span>header = MessageHeader {
<a href=#746 id=746 data-nosnippet>746</a>            num_required_signatures: <span class="number">2</span>,
<a href=#747 id=747 data-nosnippet>747</a>            num_readonly_signed_accounts: <span class="number">1</span>,
<a href=#748 id=748 data-nosnippet>748</a>            num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#749 id=749 data-nosnippet>749</a>        };
<a href=#750 id=750 data-nosnippet>750</a>
<a href=#751 id=751 data-nosnippet>751</a>        <span class="kw">let </span>legacy_message = SanitizedMessage::try_from_legacy_message(
<a href=#752 id=752 data-nosnippet>752</a>            legacy::Message {
<a href=#753 id=753 data-nosnippet>753</a>                header,
<a href=#754 id=754 data-nosnippet>754</a>                account_keys: keys.clone(),
<a href=#755 id=755 data-nosnippet>755</a>                ..legacy::Message::default()
<a href=#756 id=756 data-nosnippet>756</a>            },
<a href=#757 id=757 data-nosnippet>757</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#758 id=758 data-nosnippet>758</a>        )
<a href=#759 id=759 data-nosnippet>759</a>        .unwrap();
<a href=#760 id=760 data-nosnippet>760</a>        <span class="macro">assert_eq!</span>(legacy_message.static_account_keys(), <span class="kw-2">&amp;</span>keys);
<a href=#761 id=761 data-nosnippet>761</a>
<a href=#762 id=762 data-nosnippet>762</a>        <span class="kw">let </span>v0_message = SanitizedMessage::V0(v0::LoadedMessage::new(
<a href=#763 id=763 data-nosnippet>763</a>            v0::Message {
<a href=#764 id=764 data-nosnippet>764</a>                header,
<a href=#765 id=765 data-nosnippet>765</a>                account_keys: keys.clone(),
<a href=#766 id=766 data-nosnippet>766</a>                ..v0::Message::default()
<a href=#767 id=767 data-nosnippet>767</a>            },
<a href=#768 id=768 data-nosnippet>768</a>            LoadedAddresses {
<a href=#769 id=769 data-nosnippet>769</a>                writable: <span class="macro">vec!</span>[],
<a href=#770 id=770 data-nosnippet>770</a>                readonly: <span class="macro">vec!</span>[],
<a href=#771 id=771 data-nosnippet>771</a>            },
<a href=#772 id=772 data-nosnippet>772</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#773 id=773 data-nosnippet>773</a>        ));
<a href=#774 id=774 data-nosnippet>774</a>        <span class="macro">assert_eq!</span>(v0_message.static_account_keys(), <span class="kw-2">&amp;</span>keys);
<a href=#775 id=775 data-nosnippet>775</a>
<a href=#776 id=776 data-nosnippet>776</a>        <span class="kw">let </span>v0_message = SanitizedMessage::V0(v0::LoadedMessage::new(
<a href=#777 id=777 data-nosnippet>777</a>            v0::Message {
<a href=#778 id=778 data-nosnippet>778</a>                header,
<a href=#779 id=779 data-nosnippet>779</a>                account_keys: keys.clone(),
<a href=#780 id=780 data-nosnippet>780</a>                ..v0::Message::default()
<a href=#781 id=781 data-nosnippet>781</a>            },
<a href=#782 id=782 data-nosnippet>782</a>            LoadedAddresses {
<a href=#783 id=783 data-nosnippet>783</a>                writable: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#784 id=784 data-nosnippet>784</a>                readonly: <span class="macro">vec!</span>[Pubkey::new_unique()],
<a href=#785 id=785 data-nosnippet>785</a>            },
<a href=#786 id=786 data-nosnippet>786</a>            <span class="kw-2">&amp;</span>HashSet::default(),
<a href=#787 id=787 data-nosnippet>787</a>        ));
<a href=#788 id=788 data-nosnippet>788</a>        <span class="macro">assert_eq!</span>(v0_message.static_account_keys(), <span class="kw-2">&amp;</span>keys);
<a href=#789 id=789 data-nosnippet>789</a>    }
<a href=#790 id=790 data-nosnippet>790</a>}</code></pre></div></section></main></body></html>