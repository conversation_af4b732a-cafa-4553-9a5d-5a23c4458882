<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/address_loader.rs`."><title>address_loader.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>address_loader.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::v0::{LoadedAddresses, MessageAddressTableLookup};
<a href=#2 id=2 data-nosnippet>2</a><span class="attr">#[deprecated(
<a href=#3 id=3 data-nosnippet>3</a>    since = <span class="string">"2.1.0"</span>,
<a href=#4 id=4 data-nosnippet>4</a>    note = <span class="string">"Use solana_transaction_error::AddressLoaderError instead"
<a href=#5 id=5 data-nosnippet>5</a></span>)]
<a href=#6 id=6 data-nosnippet>6</a></span><span class="kw">pub use </span>solana_transaction_error::AddressLoaderError;
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub trait </span>AddressLoader: Clone {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">fn </span>load_addresses(
<a href=#10 id=10 data-nosnippet>10</a>        <span class="self">self</span>,
<a href=#11 id=11 data-nosnippet>11</a>        lookups: <span class="kw-2">&amp;</span>[MessageAddressTableLookup],
<a href=#12 id=12 data-nosnippet>12</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;LoadedAddresses, AddressLoaderError&gt;;
<a href=#13 id=13 data-nosnippet>13</a>}
<a href=#14 id=14 data-nosnippet>14</a>
<a href=#15 id=15 data-nosnippet>15</a><span class="attr">#[derive(Clone)]
<a href=#16 id=16 data-nosnippet>16</a></span><span class="kw">pub enum </span>SimpleAddressLoader {
<a href=#17 id=17 data-nosnippet>17</a>    Disabled,
<a href=#18 id=18 data-nosnippet>18</a>    Enabled(LoadedAddresses),
<a href=#19 id=19 data-nosnippet>19</a>}
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a><span class="kw">impl </span>AddressLoader <span class="kw">for </span>SimpleAddressLoader {
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">fn </span>load_addresses(
<a href=#23 id=23 data-nosnippet>23</a>        <span class="self">self</span>,
<a href=#24 id=24 data-nosnippet>24</a>        _lookups: <span class="kw-2">&amp;</span>[MessageAddressTableLookup],
<a href=#25 id=25 data-nosnippet>25</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;LoadedAddresses, AddressLoaderError&gt; {
<a href=#26 id=26 data-nosnippet>26</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#27 id=27 data-nosnippet>27</a>            <span class="self">Self</span>::Disabled =&gt; <span class="prelude-val">Err</span>(AddressLoaderError::Disabled),
<a href=#28 id=28 data-nosnippet>28</a>            <span class="self">Self</span>::Enabled(loaded_addresses) =&gt; <span class="prelude-val">Ok</span>(loaded_addresses),
<a href=#29 id=29 data-nosnippet>29</a>        }
<a href=#30 id=30 data-nosnippet>30</a>    }
<a href=#31 id=31 data-nosnippet>31</a>}</code></pre></div></section></main></body></html>