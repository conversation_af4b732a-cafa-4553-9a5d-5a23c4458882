<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/account_keys.rs`."><title>account_keys.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>account_keys.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>{
<a href=#2 id=2 data-nosnippet>2</a>    <span class="kw">crate</span>::{compiled_instruction::CompiledInstruction, v0::LoadedAddresses, CompileError},
<a href=#3 id=3 data-nosnippet>3</a>    solana_instruction::Instruction,
<a href=#4 id=4 data-nosnippet>4</a>    solana_pubkey::Pubkey,
<a href=#5 id=5 data-nosnippet>5</a>    std::{collections::BTreeMap, iter::zip, ops::Index},
<a href=#6 id=6 data-nosnippet>6</a>};
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="doccomment">/// Collection of static and dynamically loaded keys used to load accounts
<a href=#9 id=9 data-nosnippet>9</a>/// during transaction processing.
<a href=#10 id=10 data-nosnippet>10</a></span><span class="attr">#[derive(Clone, Default, Debug, Eq)]
<a href=#11 id=11 data-nosnippet>11</a></span><span class="kw">pub struct </span>AccountKeys&lt;<span class="lifetime">'a</span>&gt; {
<a href=#12 id=12 data-nosnippet>12</a>    static_keys: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>[Pubkey],
<a href=#13 id=13 data-nosnippet>13</a>    dynamic_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>LoadedAddresses&gt;,
<a href=#14 id=14 data-nosnippet>14</a>}
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a><span class="kw">impl </span>Index&lt;usize&gt; <span class="kw">for </span>AccountKeys&lt;<span class="lifetime">'_</span>&gt; {
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">type </span>Output = Pubkey;
<a href=#18 id=18 data-nosnippet>18</a>    <span class="attr">#[inline]
<a href=#19 id=19 data-nosnippet>19</a>    </span><span class="kw">fn </span>index(<span class="kw-2">&amp;</span><span class="self">self</span>, index: usize) -&gt; <span class="kw-2">&amp;</span><span class="self">Self</span>::Output {
<a href=#20 id=20 data-nosnippet>20</a>        <span class="self">self</span>.get(index).expect(<span class="string">"index is invalid"</span>)
<a href=#21 id=21 data-nosnippet>21</a>    }
<a href=#22 id=22 data-nosnippet>22</a>}
<a href=#23 id=23 data-nosnippet>23</a>
<a href=#24 id=24 data-nosnippet>24</a><span class="kw">impl</span>&lt;<span class="lifetime">'a</span>&gt; AccountKeys&lt;<span class="lifetime">'a</span>&gt; {
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub fn </span>new(static_keys: <span class="kw-2">&amp;</span><span class="lifetime">'a </span>[Pubkey], dynamic_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>LoadedAddresses&gt;) -&gt; <span class="self">Self </span>{
<a href=#26 id=26 data-nosnippet>26</a>        <span class="self">Self </span>{
<a href=#27 id=27 data-nosnippet>27</a>            static_keys,
<a href=#28 id=28 data-nosnippet>28</a>            dynamic_keys,
<a href=#29 id=29 data-nosnippet>29</a>        }
<a href=#30 id=30 data-nosnippet>30</a>    }
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a>    <span class="doccomment">/// Returns an iterator of account key segments. The ordering of segments
<a href=#33 id=33 data-nosnippet>33</a>    /// affects how account indexes from compiled instructions are resolved and
<a href=#34 id=34 data-nosnippet>34</a>    /// so should not be changed.
<a href=#35 id=35 data-nosnippet>35</a>    </span><span class="attr">#[inline]
<a href=#36 id=36 data-nosnippet>36</a>    </span><span class="kw">fn </span>key_segment_iter(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw">impl </span>Iterator&lt;Item = <span class="kw-2">&amp;</span><span class="lifetime">'a </span>[Pubkey]&gt; + Clone {
<a href=#37 id=37 data-nosnippet>37</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(dynamic_keys) = <span class="self">self</span>.dynamic_keys {
<a href=#38 id=38 data-nosnippet>38</a>            [
<a href=#39 id=39 data-nosnippet>39</a>                <span class="self">self</span>.static_keys,
<a href=#40 id=40 data-nosnippet>40</a>                <span class="kw-2">&amp;</span>dynamic_keys.writable,
<a href=#41 id=41 data-nosnippet>41</a>                <span class="kw-2">&amp;</span>dynamic_keys.readonly,
<a href=#42 id=42 data-nosnippet>42</a>            ]
<a href=#43 id=43 data-nosnippet>43</a>            .into_iter()
<a href=#44 id=44 data-nosnippet>44</a>        } <span class="kw">else </span>{
<a href=#45 id=45 data-nosnippet>45</a>            <span class="comment">// empty segments added for branch type compatibility
<a href=#46 id=46 data-nosnippet>46</a>            </span>[<span class="self">self</span>.static_keys, <span class="kw-2">&amp;</span>[], <span class="kw-2">&amp;</span>[]].into_iter()
<a href=#47 id=47 data-nosnippet>47</a>        }
<a href=#48 id=48 data-nosnippet>48</a>    }
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a>    <span class="doccomment">/// Returns the address of the account at the specified index of the list of
<a href=#51 id=51 data-nosnippet>51</a>    /// message account keys constructed from static keys, followed by dynamically
<a href=#52 id=52 data-nosnippet>52</a>    /// loaded writable addresses, and lastly the list of dynamically loaded
<a href=#53 id=53 data-nosnippet>53</a>    /// readonly addresses.
<a href=#54 id=54 data-nosnippet>54</a>    </span><span class="attr">#[inline]
<a href=#55 id=55 data-nosnippet>55</a>    </span><span class="kw">pub fn </span>get(<span class="kw-2">&amp;</span><span class="self">self</span>, <span class="kw-2">mut </span>index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span><span class="lifetime">'a </span>Pubkey&gt; {
<a href=#56 id=56 data-nosnippet>56</a>        <span class="kw">for </span>key_segment <span class="kw">in </span><span class="self">self</span>.key_segment_iter() {
<a href=#57 id=57 data-nosnippet>57</a>            <span class="kw">if </span>index &lt; key_segment.len() {
<a href=#58 id=58 data-nosnippet>58</a>                <span class="kw">return </span><span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>key_segment[index]);
<a href=#59 id=59 data-nosnippet>59</a>            }
<a href=#60 id=60 data-nosnippet>60</a>            index = index.saturating_sub(key_segment.len());
<a href=#61 id=61 data-nosnippet>61</a>        }
<a href=#62 id=62 data-nosnippet>62</a>
<a href=#63 id=63 data-nosnippet>63</a>        <span class="prelude-val">None
<a href=#64 id=64 data-nosnippet>64</a>    </span>}
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a>    <span class="doccomment">/// Returns the total length of loaded accounts for a message
<a href=#67 id=67 data-nosnippet>67</a>    </span><span class="attr">#[inline]
<a href=#68 id=68 data-nosnippet>68</a>    </span><span class="kw">pub fn </span>len(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; usize {
<a href=#69 id=69 data-nosnippet>69</a>        <span class="kw">let </span><span class="kw-2">mut </span>len = <span class="number">0usize</span>;
<a href=#70 id=70 data-nosnippet>70</a>        <span class="kw">for </span>key_segment <span class="kw">in </span><span class="self">self</span>.key_segment_iter() {
<a href=#71 id=71 data-nosnippet>71</a>            len = len.saturating_add(key_segment.len());
<a href=#72 id=72 data-nosnippet>72</a>        }
<a href=#73 id=73 data-nosnippet>73</a>        len
<a href=#74 id=74 data-nosnippet>74</a>    }
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>    <span class="doccomment">/// Returns true if this collection of account keys is empty
<a href=#77 id=77 data-nosnippet>77</a>    </span><span class="kw">pub fn </span>is_empty(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#78 id=78 data-nosnippet>78</a>        <span class="self">self</span>.len() == <span class="number">0
<a href=#79 id=79 data-nosnippet>79</a>    </span>}
<a href=#80 id=80 data-nosnippet>80</a>
<a href=#81 id=81 data-nosnippet>81</a>    <span class="doccomment">/// Iterator for the addresses of the loaded accounts for a message
<a href=#82 id=82 data-nosnippet>82</a>    </span><span class="attr">#[inline]
<a href=#83 id=83 data-nosnippet>83</a>    </span><span class="kw">pub fn </span>iter(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw">impl </span>Iterator&lt;Item = <span class="kw-2">&amp;</span><span class="lifetime">'a </span>Pubkey&gt; + Clone {
<a href=#84 id=84 data-nosnippet>84</a>        <span class="self">self</span>.key_segment_iter().flatten()
<a href=#85 id=85 data-nosnippet>85</a>    }
<a href=#86 id=86 data-nosnippet>86</a>
<a href=#87 id=87 data-nosnippet>87</a>    <span class="doccomment">/// Compile instructions using the order of account keys to determine
<a href=#88 id=88 data-nosnippet>88</a>    /// compiled instruction account indexes.
<a href=#89 id=89 data-nosnippet>89</a>    ///
<a href=#90 id=90 data-nosnippet>90</a>    /// # Panics
<a href=#91 id=91 data-nosnippet>91</a>    ///
<a href=#92 id=92 data-nosnippet>92</a>    /// Panics when compiling fails. See [`AccountKeys::try_compile_instructions`]
<a href=#93 id=93 data-nosnippet>93</a>    /// for a full description of failure scenarios.
<a href=#94 id=94 data-nosnippet>94</a>    </span><span class="kw">pub fn </span>compile_instructions(<span class="kw-2">&amp;</span><span class="self">self</span>, instructions: <span class="kw-2">&amp;</span>[Instruction]) -&gt; Vec&lt;CompiledInstruction&gt; {
<a href=#95 id=95 data-nosnippet>95</a>        <span class="self">self</span>.try_compile_instructions(instructions)
<a href=#96 id=96 data-nosnippet>96</a>            .expect(<span class="string">"compilation failure"</span>)
<a href=#97 id=97 data-nosnippet>97</a>    }
<a href=#98 id=98 data-nosnippet>98</a>
<a href=#99 id=99 data-nosnippet>99</a>    <span class="doccomment">/// Compile instructions using the order of account keys to determine
<a href=#100 id=100 data-nosnippet>100</a>    /// compiled instruction account indexes.
<a href=#101 id=101 data-nosnippet>101</a>    ///
<a href=#102 id=102 data-nosnippet>102</a>    /// # Errors
<a href=#103 id=103 data-nosnippet>103</a>    ///
<a href=#104 id=104 data-nosnippet>104</a>    /// Compilation will fail if any `instructions` use account keys which are not
<a href=#105 id=105 data-nosnippet>105</a>    /// present in this account key collection.
<a href=#106 id=106 data-nosnippet>106</a>    ///
<a href=#107 id=107 data-nosnippet>107</a>    /// Compilation will fail if any `instructions` use account keys which are located
<a href=#108 id=108 data-nosnippet>108</a>    /// at an index which cannot be cast to a `u8` without overflow.
<a href=#109 id=109 data-nosnippet>109</a>    </span><span class="kw">pub fn </span>try_compile_instructions(
<a href=#110 id=110 data-nosnippet>110</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#111 id=111 data-nosnippet>111</a>        instructions: <span class="kw-2">&amp;</span>[Instruction],
<a href=#112 id=112 data-nosnippet>112</a>    ) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;CompiledInstruction&gt;, CompileError&gt; {
<a href=#113 id=113 data-nosnippet>113</a>        <span class="kw">let </span><span class="kw-2">mut </span>account_index_map = BTreeMap::&lt;<span class="kw-2">&amp;</span>Pubkey, u8&gt;::new();
<a href=#114 id=114 data-nosnippet>114</a>        <span class="kw">for </span>(index, key) <span class="kw">in </span><span class="self">self</span>.iter().enumerate() {
<a href=#115 id=115 data-nosnippet>115</a>            <span class="kw">let </span>index = u8::try_from(index).map_err(|<span class="kw">_</span>| CompileError::AccountIndexOverflow)<span class="question-mark">?</span>;
<a href=#116 id=116 data-nosnippet>116</a>            account_index_map.insert(key, index);
<a href=#117 id=117 data-nosnippet>117</a>        }
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>        <span class="kw">let </span>get_account_index = |key: <span class="kw-2">&amp;</span>Pubkey| -&gt; <span class="prelude-ty">Result</span>&lt;u8, CompileError&gt; {
<a href=#120 id=120 data-nosnippet>120</a>            account_index_map
<a href=#121 id=121 data-nosnippet>121</a>                .get(key)
<a href=#122 id=122 data-nosnippet>122</a>                .cloned()
<a href=#123 id=123 data-nosnippet>123</a>                .ok_or(CompileError::UnknownInstructionKey(<span class="kw-2">*</span>key))
<a href=#124 id=124 data-nosnippet>124</a>        };
<a href=#125 id=125 data-nosnippet>125</a>
<a href=#126 id=126 data-nosnippet>126</a>        instructions
<a href=#127 id=127 data-nosnippet>127</a>            .iter()
<a href=#128 id=128 data-nosnippet>128</a>            .map(|ix| {
<a href=#129 id=129 data-nosnippet>129</a>                <span class="kw">let </span>accounts: Vec&lt;u8&gt; = ix
<a href=#130 id=130 data-nosnippet>130</a>                    .accounts
<a href=#131 id=131 data-nosnippet>131</a>                    .iter()
<a href=#132 id=132 data-nosnippet>132</a>                    .map(|account_meta| get_account_index(<span class="kw-2">&amp;</span>account_meta.pubkey))
<a href=#133 id=133 data-nosnippet>133</a>                    .collect::&lt;<span class="prelude-ty">Result</span>&lt;Vec&lt;u8&gt;, CompileError&gt;&gt;()<span class="question-mark">?</span>;
<a href=#134 id=134 data-nosnippet>134</a>
<a href=#135 id=135 data-nosnippet>135</a>                <span class="prelude-val">Ok</span>(CompiledInstruction {
<a href=#136 id=136 data-nosnippet>136</a>                    program_id_index: get_account_index(<span class="kw-2">&amp;</span>ix.program_id)<span class="question-mark">?</span>,
<a href=#137 id=137 data-nosnippet>137</a>                    data: ix.data.clone(),
<a href=#138 id=138 data-nosnippet>138</a>                    accounts,
<a href=#139 id=139 data-nosnippet>139</a>                })
<a href=#140 id=140 data-nosnippet>140</a>            })
<a href=#141 id=141 data-nosnippet>141</a>            .collect()
<a href=#142 id=142 data-nosnippet>142</a>    }
<a href=#143 id=143 data-nosnippet>143</a>}
<a href=#144 id=144 data-nosnippet>144</a>
<a href=#145 id=145 data-nosnippet>145</a><span class="kw">impl </span>PartialEq <span class="kw">for </span>AccountKeys&lt;<span class="lifetime">'_</span>&gt; {
<a href=#146 id=146 data-nosnippet>146</a>    <span class="kw">fn </span>eq(<span class="kw-2">&amp;</span><span class="self">self</span>, other: <span class="kw-2">&amp;</span><span class="self">Self</span>) -&gt; bool {
<a href=#147 id=147 data-nosnippet>147</a>        zip(<span class="self">self</span>.iter(), other.iter()).all(|(a, b)| a == b)
<a href=#148 id=148 data-nosnippet>148</a>    }
<a href=#149 id=149 data-nosnippet>149</a>}
<a href=#150 id=150 data-nosnippet>150</a>
<a href=#151 id=151 data-nosnippet>151</a><span class="attr">#[cfg(test)]
<a href=#152 id=152 data-nosnippet>152</a></span><span class="kw">mod </span>tests {
<a href=#153 id=153 data-nosnippet>153</a>    <span class="kw">use </span>{<span class="kw">super</span>::<span class="kw-2">*</span>, solana_instruction::AccountMeta};
<a href=#154 id=154 data-nosnippet>154</a>
<a href=#155 id=155 data-nosnippet>155</a>    <span class="kw">fn </span>test_account_keys() -&gt; [Pubkey; <span class="number">6</span>] {
<a href=#156 id=156 data-nosnippet>156</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#157 id=157 data-nosnippet>157</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#158 id=158 data-nosnippet>158</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#159 id=159 data-nosnippet>159</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#160 id=160 data-nosnippet>160</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#161 id=161 data-nosnippet>161</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#162 id=162 data-nosnippet>162</a>
<a href=#163 id=163 data-nosnippet>163</a>        [key0, key1, key2, key3, key4, key5]
<a href=#164 id=164 data-nosnippet>164</a>    }
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>    <span class="attr">#[test]
<a href=#167 id=167 data-nosnippet>167</a>    </span><span class="kw">fn </span>test_key_segment_iter() {
<a href=#168 id=168 data-nosnippet>168</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>]];
<a href=#171 id=171 data-nosnippet>171</a>        <span class="kw">let </span>dynamic_keys = LoadedAddresses {
<a href=#172 id=172 data-nosnippet>172</a>            writable: <span class="macro">vec!</span>[keys[<span class="number">3</span>], keys[<span class="number">4</span>]],
<a href=#173 id=173 data-nosnippet>173</a>            readonly: <span class="macro">vec!</span>[keys[<span class="number">5</span>]],
<a href=#174 id=174 data-nosnippet>174</a>        };
<a href=#175 id=175 data-nosnippet>175</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#176 id=176 data-nosnippet>176</a>
<a href=#177 id=177 data-nosnippet>177</a>        <span class="kw">let </span>expected_segments = [
<a href=#178 id=178 data-nosnippet>178</a>            <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>]],
<a href=#179 id=179 data-nosnippet>179</a>            <span class="macro">vec!</span>[keys[<span class="number">3</span>], keys[<span class="number">4</span>]],
<a href=#180 id=180 data-nosnippet>180</a>            <span class="macro">vec!</span>[keys[<span class="number">5</span>]],
<a href=#181 id=181 data-nosnippet>181</a>        ];
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a>        <span class="macro">assert!</span>(account_keys.key_segment_iter().eq(expected_segments.iter()));
<a href=#184 id=184 data-nosnippet>184</a>    }
<a href=#185 id=185 data-nosnippet>185</a>
<a href=#186 id=186 data-nosnippet>186</a>    <span class="attr">#[test]
<a href=#187 id=187 data-nosnippet>187</a>    </span><span class="kw">fn </span>test_len() {
<a href=#188 id=188 data-nosnippet>188</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#189 id=189 data-nosnippet>189</a>
<a href=#190 id=190 data-nosnippet>190</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>], keys[<span class="number">3</span>], keys[<span class="number">4</span>], keys[<span class="number">5</span>]];
<a href=#191 id=191 data-nosnippet>191</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">None</span>);
<a href=#192 id=192 data-nosnippet>192</a>
<a href=#193 id=193 data-nosnippet>193</a>        <span class="macro">assert_eq!</span>(account_keys.len(), keys.len());
<a href=#194 id=194 data-nosnippet>194</a>    }
<a href=#195 id=195 data-nosnippet>195</a>
<a href=#196 id=196 data-nosnippet>196</a>    <span class="attr">#[test]
<a href=#197 id=197 data-nosnippet>197</a>    </span><span class="kw">fn </span>test_len_with_dynamic_keys() {
<a href=#198 id=198 data-nosnippet>198</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#199 id=199 data-nosnippet>199</a>
<a href=#200 id=200 data-nosnippet>200</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>]];
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw">let </span>dynamic_keys = LoadedAddresses {
<a href=#202 id=202 data-nosnippet>202</a>            writable: <span class="macro">vec!</span>[keys[<span class="number">3</span>], keys[<span class="number">4</span>]],
<a href=#203 id=203 data-nosnippet>203</a>            readonly: <span class="macro">vec!</span>[keys[<span class="number">5</span>]],
<a href=#204 id=204 data-nosnippet>204</a>        };
<a href=#205 id=205 data-nosnippet>205</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#206 id=206 data-nosnippet>206</a>
<a href=#207 id=207 data-nosnippet>207</a>        <span class="macro">assert_eq!</span>(account_keys.len(), keys.len());
<a href=#208 id=208 data-nosnippet>208</a>    }
<a href=#209 id=209 data-nosnippet>209</a>
<a href=#210 id=210 data-nosnippet>210</a>    <span class="attr">#[test]
<a href=#211 id=211 data-nosnippet>211</a>    </span><span class="kw">fn </span>test_iter() {
<a href=#212 id=212 data-nosnippet>212</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#213 id=213 data-nosnippet>213</a>
<a href=#214 id=214 data-nosnippet>214</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>], keys[<span class="number">3</span>], keys[<span class="number">4</span>], keys[<span class="number">5</span>]];
<a href=#215 id=215 data-nosnippet>215</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">None</span>);
<a href=#216 id=216 data-nosnippet>216</a>
<a href=#217 id=217 data-nosnippet>217</a>        <span class="macro">assert!</span>(account_keys.iter().eq(keys.iter()));
<a href=#218 id=218 data-nosnippet>218</a>    }
<a href=#219 id=219 data-nosnippet>219</a>
<a href=#220 id=220 data-nosnippet>220</a>    <span class="attr">#[test]
<a href=#221 id=221 data-nosnippet>221</a>    </span><span class="kw">fn </span>test_iter_with_dynamic_keys() {
<a href=#222 id=222 data-nosnippet>222</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#223 id=223 data-nosnippet>223</a>
<a href=#224 id=224 data-nosnippet>224</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>]];
<a href=#225 id=225 data-nosnippet>225</a>        <span class="kw">let </span>dynamic_keys = LoadedAddresses {
<a href=#226 id=226 data-nosnippet>226</a>            writable: <span class="macro">vec!</span>[keys[<span class="number">3</span>], keys[<span class="number">4</span>]],
<a href=#227 id=227 data-nosnippet>227</a>            readonly: <span class="macro">vec!</span>[keys[<span class="number">5</span>]],
<a href=#228 id=228 data-nosnippet>228</a>        };
<a href=#229 id=229 data-nosnippet>229</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>        <span class="macro">assert!</span>(account_keys.iter().eq(keys.iter()));
<a href=#232 id=232 data-nosnippet>232</a>    }
<a href=#233 id=233 data-nosnippet>233</a>
<a href=#234 id=234 data-nosnippet>234</a>    <span class="attr">#[test]
<a href=#235 id=235 data-nosnippet>235</a>    </span><span class="kw">fn </span>test_get() {
<a href=#236 id=236 data-nosnippet>236</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#237 id=237 data-nosnippet>237</a>
<a href=#238 id=238 data-nosnippet>238</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>], keys[<span class="number">3</span>]];
<a href=#239 id=239 data-nosnippet>239</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">None</span>);
<a href=#240 id=240 data-nosnippet>240</a>
<a href=#241 id=241 data-nosnippet>241</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">0</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">0</span>]));
<a href=#242 id=242 data-nosnippet>242</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">1</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">1</span>]));
<a href=#243 id=243 data-nosnippet>243</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">2</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">2</span>]));
<a href=#244 id=244 data-nosnippet>244</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">3</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">3</span>]));
<a href=#245 id=245 data-nosnippet>245</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">4</span>), <span class="prelude-val">None</span>);
<a href=#246 id=246 data-nosnippet>246</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">5</span>), <span class="prelude-val">None</span>);
<a href=#247 id=247 data-nosnippet>247</a>    }
<a href=#248 id=248 data-nosnippet>248</a>
<a href=#249 id=249 data-nosnippet>249</a>    <span class="attr">#[test]
<a href=#250 id=250 data-nosnippet>250</a>    </span><span class="kw">fn </span>test_get_with_dynamic_keys() {
<a href=#251 id=251 data-nosnippet>251</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>], keys[<span class="number">1</span>], keys[<span class="number">2</span>]];
<a href=#254 id=254 data-nosnippet>254</a>        <span class="kw">let </span>dynamic_keys = LoadedAddresses {
<a href=#255 id=255 data-nosnippet>255</a>            writable: <span class="macro">vec!</span>[keys[<span class="number">3</span>], keys[<span class="number">4</span>]],
<a href=#256 id=256 data-nosnippet>256</a>            readonly: <span class="macro">vec!</span>[keys[<span class="number">5</span>]],
<a href=#257 id=257 data-nosnippet>257</a>        };
<a href=#258 id=258 data-nosnippet>258</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#259 id=259 data-nosnippet>259</a>
<a href=#260 id=260 data-nosnippet>260</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">0</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">0</span>]));
<a href=#261 id=261 data-nosnippet>261</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">1</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">1</span>]));
<a href=#262 id=262 data-nosnippet>262</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">2</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">2</span>]));
<a href=#263 id=263 data-nosnippet>263</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">3</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">3</span>]));
<a href=#264 id=264 data-nosnippet>264</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">4</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">4</span>]));
<a href=#265 id=265 data-nosnippet>265</a>        <span class="macro">assert_eq!</span>(account_keys.get(<span class="number">5</span>), <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>keys[<span class="number">5</span>]));
<a href=#266 id=266 data-nosnippet>266</a>    }
<a href=#267 id=267 data-nosnippet>267</a>
<a href=#268 id=268 data-nosnippet>268</a>    <span class="attr">#[test]
<a href=#269 id=269 data-nosnippet>269</a>    </span><span class="kw">fn </span>test_try_compile_instructions() {
<a href=#270 id=270 data-nosnippet>270</a>        <span class="kw">let </span>keys = test_account_keys();
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[keys[<span class="number">0</span>]];
<a href=#273 id=273 data-nosnippet>273</a>        <span class="kw">let </span>dynamic_keys = LoadedAddresses {
<a href=#274 id=274 data-nosnippet>274</a>            writable: <span class="macro">vec!</span>[keys[<span class="number">1</span>]],
<a href=#275 id=275 data-nosnippet>275</a>            readonly: <span class="macro">vec!</span>[keys[<span class="number">2</span>]],
<a href=#276 id=276 data-nosnippet>276</a>        };
<a href=#277 id=277 data-nosnippet>277</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#278 id=278 data-nosnippet>278</a>
<a href=#279 id=279 data-nosnippet>279</a>        <span class="kw">let </span>instruction = Instruction {
<a href=#280 id=280 data-nosnippet>280</a>            program_id: keys[<span class="number">0</span>],
<a href=#281 id=281 data-nosnippet>281</a>            accounts: <span class="macro">vec!</span>[
<a href=#282 id=282 data-nosnippet>282</a>                AccountMeta::new(keys[<span class="number">1</span>], <span class="bool-val">true</span>),
<a href=#283 id=283 data-nosnippet>283</a>                AccountMeta::new(keys[<span class="number">2</span>], <span class="bool-val">true</span>),
<a href=#284 id=284 data-nosnippet>284</a>            ],
<a href=#285 id=285 data-nosnippet>285</a>            data: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#286 id=286 data-nosnippet>286</a>        };
<a href=#287 id=287 data-nosnippet>287</a>
<a href=#288 id=288 data-nosnippet>288</a>        <span class="macro">assert_eq!</span>(
<a href=#289 id=289 data-nosnippet>289</a>            account_keys.try_compile_instructions(<span class="kw-2">&amp;</span>[instruction]),
<a href=#290 id=290 data-nosnippet>290</a>            <span class="prelude-val">Ok</span>(<span class="macro">vec!</span>[CompiledInstruction {
<a href=#291 id=291 data-nosnippet>291</a>                program_id_index: <span class="number">0</span>,
<a href=#292 id=292 data-nosnippet>292</a>                accounts: <span class="macro">vec!</span>[<span class="number">1</span>, <span class="number">2</span>],
<a href=#293 id=293 data-nosnippet>293</a>                data: <span class="macro">vec!</span>[<span class="number">0</span>],
<a href=#294 id=294 data-nosnippet>294</a>            }]),
<a href=#295 id=295 data-nosnippet>295</a>        );
<a href=#296 id=296 data-nosnippet>296</a>    }
<a href=#297 id=297 data-nosnippet>297</a>
<a href=#298 id=298 data-nosnippet>298</a>    <span class="attr">#[test]
<a href=#299 id=299 data-nosnippet>299</a>    </span><span class="kw">fn </span>test_try_compile_instructions_with_unknown_key() {
<a href=#300 id=300 data-nosnippet>300</a>        <span class="kw">let </span>static_keys = test_account_keys();
<a href=#301 id=301 data-nosnippet>301</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">None</span>);
<a href=#302 id=302 data-nosnippet>302</a>
<a href=#303 id=303 data-nosnippet>303</a>        <span class="kw">let </span>unknown_key = Pubkey::new_unique();
<a href=#304 id=304 data-nosnippet>304</a>        <span class="kw">let </span>test_instructions = [
<a href=#305 id=305 data-nosnippet>305</a>            Instruction {
<a href=#306 id=306 data-nosnippet>306</a>                program_id: unknown_key,
<a href=#307 id=307 data-nosnippet>307</a>                accounts: <span class="macro">vec!</span>[],
<a href=#308 id=308 data-nosnippet>308</a>                data: <span class="macro">vec!</span>[],
<a href=#309 id=309 data-nosnippet>309</a>            },
<a href=#310 id=310 data-nosnippet>310</a>            Instruction {
<a href=#311 id=311 data-nosnippet>311</a>                program_id: static_keys[<span class="number">0</span>],
<a href=#312 id=312 data-nosnippet>312</a>                accounts: <span class="macro">vec!</span>[
<a href=#313 id=313 data-nosnippet>313</a>                    AccountMeta::new(static_keys[<span class="number">1</span>], <span class="bool-val">false</span>),
<a href=#314 id=314 data-nosnippet>314</a>                    AccountMeta::new(unknown_key, <span class="bool-val">false</span>),
<a href=#315 id=315 data-nosnippet>315</a>                ],
<a href=#316 id=316 data-nosnippet>316</a>                data: <span class="macro">vec!</span>[],
<a href=#317 id=317 data-nosnippet>317</a>            },
<a href=#318 id=318 data-nosnippet>318</a>        ];
<a href=#319 id=319 data-nosnippet>319</a>
<a href=#320 id=320 data-nosnippet>320</a>        <span class="kw">for </span>ix <span class="kw">in </span>test_instructions {
<a href=#321 id=321 data-nosnippet>321</a>            <span class="macro">assert_eq!</span>(
<a href=#322 id=322 data-nosnippet>322</a>                account_keys.try_compile_instructions(<span class="kw-2">&amp;</span>[ix]),
<a href=#323 id=323 data-nosnippet>323</a>                <span class="prelude-val">Err</span>(CompileError::UnknownInstructionKey(unknown_key))
<a href=#324 id=324 data-nosnippet>324</a>            );
<a href=#325 id=325 data-nosnippet>325</a>        }
<a href=#326 id=326 data-nosnippet>326</a>    }
<a href=#327 id=327 data-nosnippet>327</a>
<a href=#328 id=328 data-nosnippet>328</a>    <span class="attr">#[test]
<a href=#329 id=329 data-nosnippet>329</a>    </span><span class="kw">fn </span>test_try_compile_instructions_with_too_many_account_keys() {
<a href=#330 id=330 data-nosnippet>330</a>        <span class="kw">const </span>MAX_LENGTH_WITHOUT_OVERFLOW: usize = u8::MAX <span class="kw">as </span>usize + <span class="number">1</span>;
<a href=#331 id=331 data-nosnippet>331</a>        <span class="kw">let </span>static_keys = <span class="macro">vec!</span>[Pubkey::default(); MAX_LENGTH_WITHOUT_OVERFLOW];
<a href=#332 id=332 data-nosnippet>332</a>        <span class="kw">let </span>dynamic_keys = LoadedAddresses {
<a href=#333 id=333 data-nosnippet>333</a>            writable: <span class="macro">vec!</span>[Pubkey::default()],
<a href=#334 id=334 data-nosnippet>334</a>            readonly: <span class="macro">vec!</span>[],
<a href=#335 id=335 data-nosnippet>335</a>        };
<a href=#336 id=336 data-nosnippet>336</a>        <span class="kw">let </span>account_keys = AccountKeys::new(<span class="kw-2">&amp;</span>static_keys, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>dynamic_keys));
<a href=#337 id=337 data-nosnippet>337</a>        <span class="macro">assert_eq!</span>(
<a href=#338 id=338 data-nosnippet>338</a>            account_keys.try_compile_instructions(<span class="kw-2">&amp;</span>[]),
<a href=#339 id=339 data-nosnippet>339</a>            <span class="prelude-val">Err</span>(CompileError::AccountIndexOverflow)
<a href=#340 id=340 data-nosnippet>340</a>        );
<a href=#341 id=341 data-nosnippet>341</a>    }
<a href=#342 id=342 data-nosnippet>342</a>}</code></pre></div></section></main></body></html>