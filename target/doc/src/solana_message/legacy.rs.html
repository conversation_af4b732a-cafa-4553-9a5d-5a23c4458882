<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/solana-message-2.2.1/src/legacy.rs`."><title>legacy.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">solana_message/</div>legacy.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-4"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="doccomment">//! The original and current Solana message format.
<a href=#2 id=2 data-nosnippet>2</a>//!
<a href=#3 id=3 data-nosnippet>3</a>//! This crate defines two versions of `Message` in their own modules:
<a href=#4 id=4 data-nosnippet>4</a>//! [`legacy`] and [`v0`]. `legacy` is the current version as of Solana 1.10.0.
<a href=#5 id=5 data-nosnippet>5</a>//! `v0` is a [future message format] that encodes more account keys into a
<a href=#6 id=6 data-nosnippet>6</a>//! transaction than the legacy format.
<a href=#7 id=7 data-nosnippet>7</a>//!
<a href=#8 id=8 data-nosnippet>8</a>//! [`legacy`]: crate::legacy
<a href=#9 id=9 data-nosnippet>9</a>//! [`v0`]: crate::v0
<a href=#10 id=10 data-nosnippet>10</a>//! [future message format]: https://docs.solanalabs.com/proposals/versioned-transactions
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a></span><span class="attr">#![allow(clippy::arithmetic_side_effects)]
<a href=#13 id=13 data-nosnippet>13</a>
<a href=#14 id=14 data-nosnippet>14</a>#[allow(deprecated)]
<a href=#15 id=15 data-nosnippet>15</a></span><span class="kw">pub use </span>builtins::{BUILTIN_PROGRAMS_KEYS, MAYBE_BUILTIN_KEY_OR_SYSVAR};
<a href=#16 id=16 data-nosnippet>16</a><span class="attr">#[cfg(feature = <span class="string">"serde"</span>)]
<a href=#17 id=17 data-nosnippet>17</a></span><span class="kw">use </span>serde_derive::{Deserialize, Serialize};
<a href=#18 id=18 data-nosnippet>18</a><span class="attr">#[cfg(feature = <span class="string">"frozen-abi"</span>)]
<a href=#19 id=19 data-nosnippet>19</a></span><span class="kw">use </span>solana_frozen_abi_macro::{frozen_abi, AbiExample};
<a href=#20 id=20 data-nosnippet>20</a><span class="attr">#[cfg(target_arch = <span class="string">"wasm32"</span>)]
<a href=#21 id=21 data-nosnippet>21</a></span><span class="kw">use </span>wasm_bindgen::prelude::wasm_bindgen;
<a href=#22 id=22 data-nosnippet>22</a><span class="kw">use </span>{
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">crate</span>::{
<a href=#24 id=24 data-nosnippet>24</a>        compiled_instruction::CompiledInstruction, compiled_keys::CompiledKeys, MessageHeader,
<a href=#25 id=25 data-nosnippet>25</a>    },
<a href=#26 id=26 data-nosnippet>26</a>    solana_hash::Hash,
<a href=#27 id=27 data-nosnippet>27</a>    solana_instruction::Instruction,
<a href=#28 id=28 data-nosnippet>28</a>    solana_pubkey::Pubkey,
<a href=#29 id=29 data-nosnippet>29</a>    solana_sanitize::{Sanitize, SanitizeError},
<a href=#30 id=30 data-nosnippet>30</a>    solana_sdk_ids::{
<a href=#31 id=31 data-nosnippet>31</a>        bpf_loader, bpf_loader_deprecated, bpf_loader_upgradeable, system_program, sysvar,
<a href=#32 id=32 data-nosnippet>32</a>    },
<a href=#33 id=33 data-nosnippet>33</a>    std::{collections::HashSet, convert::TryFrom, str::FromStr},
<a href=#34 id=34 data-nosnippet>34</a>};
<a href=#35 id=35 data-nosnippet>35</a>
<a href=#36 id=36 data-nosnippet>36</a><span class="comment">// copied from deprecated code in solana_program::sysvar to avoid a dependency.
<a href=#37 id=37 data-nosnippet>37</a>// This should be removed when the items that depend on it are removed.
<a href=#38 id=38 data-nosnippet>38</a></span><span class="macro">lazy_static::lazy_static!</span> {
<a href=#39 id=39 data-nosnippet>39</a>    <span class="comment">// This will be deprecated and so this list shouldn't be modified
<a href=#40 id=40 data-nosnippet>40</a>    </span><span class="kw">static </span><span class="kw-2">ref </span>ALL_IDS: Vec&lt;Pubkey&gt; = <span class="macro">vec!</span>[
<a href=#41 id=41 data-nosnippet>41</a>        sysvar::clock::id(),
<a href=#42 id=42 data-nosnippet>42</a>        sysvar::epoch_schedule::id(),
<a href=#43 id=43 data-nosnippet>43</a>        sysvar::fees::id(),
<a href=#44 id=44 data-nosnippet>44</a>        sysvar::recent_blockhashes::id(),
<a href=#45 id=45 data-nosnippet>45</a>        sysvar::rent::id(),
<a href=#46 id=46 data-nosnippet>46</a>        sysvar::rewards::id(),
<a href=#47 id=47 data-nosnippet>47</a>        sysvar::slot_hashes::id(),
<a href=#48 id=48 data-nosnippet>48</a>        sysvar::slot_history::id(),
<a href=#49 id=49 data-nosnippet>49</a>        sysvar::stake_history::id(),
<a href=#50 id=50 data-nosnippet>50</a>        sysvar::instructions::id(),
<a href=#51 id=51 data-nosnippet>51</a>    ];
<a href=#52 id=52 data-nosnippet>52</a>}
<a href=#53 id=53 data-nosnippet>53</a>
<a href=#54 id=54 data-nosnippet>54</a><span class="comment">// copied from deprecated code in solana_program::sysvar to avoid a dependency.
<a href=#55 id=55 data-nosnippet>55</a>// This should be removed when the items that depend on it are removed.
<a href=#56 id=56 data-nosnippet>56</a></span><span class="kw">fn </span>is_sysvar_id(id: <span class="kw-2">&amp;</span>Pubkey) -&gt; bool {
<a href=#57 id=57 data-nosnippet>57</a>    ALL_IDS.iter().any(|key| key == id)
<a href=#58 id=58 data-nosnippet>58</a>}
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a><span class="attr">#[deprecated(
<a href=#61 id=61 data-nosnippet>61</a>    since = <span class="string">"2.0.0"</span>,
<a href=#62 id=62 data-nosnippet>62</a>    note = <span class="string">"please use `solana_sdk::reserved_account_keys::ReservedAccountKeys` instead"
<a href=#63 id=63 data-nosnippet>63</a></span>)]
<a href=#64 id=64 data-nosnippet>64</a>#[allow(deprecated)]
<a href=#65 id=65 data-nosnippet>65</a></span><span class="kw">mod </span>builtins {
<a href=#66 id=66 data-nosnippet>66</a>    <span class="kw">use </span>{<span class="kw">super</span>::<span class="kw-2">*</span>, lazy_static::lazy_static};
<a href=#67 id=67 data-nosnippet>67</a>
<a href=#68 id=68 data-nosnippet>68</a>    <span class="macro">lazy_static!</span> {
<a href=#69 id=69 data-nosnippet>69</a>        <span class="kw">pub static </span><span class="kw-2">ref </span>BUILTIN_PROGRAMS_KEYS: [Pubkey; <span class="number">10</span>] = {
<a href=#70 id=70 data-nosnippet>70</a>            <span class="kw">let </span>parse = |s| Pubkey::from_str(s).unwrap();
<a href=#71 id=71 data-nosnippet>71</a>            [
<a href=#72 id=72 data-nosnippet>72</a>                parse(<span class="string">"Config1111111111111111111111111111111111111"</span>),
<a href=#73 id=73 data-nosnippet>73</a>                parse(<span class="string">"Feature111111111111111111111111111111111111"</span>),
<a href=#74 id=74 data-nosnippet>74</a>                parse(<span class="string">"NativeLoader1111111111111111111111111111111"</span>),
<a href=#75 id=75 data-nosnippet>75</a>                parse(<span class="string">"Stake11111111111111111111111111111111111111"</span>),
<a href=#76 id=76 data-nosnippet>76</a>                parse(<span class="string">"StakeConfig11111111111111111111111111111111"</span>),
<a href=#77 id=77 data-nosnippet>77</a>                parse(<span class="string">"Vote111111111111111111111111111111111111111"</span>),
<a href=#78 id=78 data-nosnippet>78</a>                system_program::id(),
<a href=#79 id=79 data-nosnippet>79</a>                bpf_loader::id(),
<a href=#80 id=80 data-nosnippet>80</a>                bpf_loader_deprecated::id(),
<a href=#81 id=81 data-nosnippet>81</a>                bpf_loader_upgradeable::id(),
<a href=#82 id=82 data-nosnippet>82</a>            ]
<a href=#83 id=83 data-nosnippet>83</a>        };
<a href=#84 id=84 data-nosnippet>84</a>    }
<a href=#85 id=85 data-nosnippet>85</a>
<a href=#86 id=86 data-nosnippet>86</a>    <span class="macro">lazy_static!</span> {
<a href=#87 id=87 data-nosnippet>87</a>        <span class="comment">// Each element of a key is a u8. We use key[0] as an index into this table of 256 boolean
<a href=#88 id=88 data-nosnippet>88</a>        // elements, to store whether or not the first element of any key is present in the static
<a href=#89 id=89 data-nosnippet>89</a>        // lists of built-in-program keys or system ids. By using this lookup table, we can very
<a href=#90 id=90 data-nosnippet>90</a>        // quickly determine that a key under consideration cannot be in either of these lists (if
<a href=#91 id=91 data-nosnippet>91</a>        // the value is "false"), or might be in one of these lists (if the value is "true")
<a href=#92 id=92 data-nosnippet>92</a>        </span><span class="kw">pub static </span><span class="kw-2">ref </span>MAYBE_BUILTIN_KEY_OR_SYSVAR: [bool; <span class="number">256</span>] = {
<a href=#93 id=93 data-nosnippet>93</a>            <span class="kw">let </span><span class="kw-2">mut </span>temp_table: [bool; <span class="number">256</span>] = [<span class="bool-val">false</span>; <span class="number">256</span>];
<a href=#94 id=94 data-nosnippet>94</a>            BUILTIN_PROGRAMS_KEYS.iter().for_each(|key| temp_table[key.as_ref()[<span class="number">0</span>] <span class="kw">as </span>usize] = <span class="bool-val">true</span>);
<a href=#95 id=95 data-nosnippet>95</a>            ALL_IDS.iter().for_each(|key| temp_table[key.as_ref()[<span class="number">0</span>] <span class="kw">as </span>usize] = <span class="bool-val">true</span>);
<a href=#96 id=96 data-nosnippet>96</a>            temp_table
<a href=#97 id=97 data-nosnippet>97</a>        };
<a href=#98 id=98 data-nosnippet>98</a>    }
<a href=#99 id=99 data-nosnippet>99</a>}
<a href=#100 id=100 data-nosnippet>100</a>
<a href=#101 id=101 data-nosnippet>101</a><span class="attr">#[deprecated(
<a href=#102 id=102 data-nosnippet>102</a>    since = <span class="string">"2.0.0"</span>,
<a href=#103 id=103 data-nosnippet>103</a>    note = <span class="string">"please use `solana_sdk::reserved_account_keys::ReservedAccountKeys::is_reserved` instead"
<a href=#104 id=104 data-nosnippet>104</a></span>)]
<a href=#105 id=105 data-nosnippet>105</a>#[allow(deprecated)]
<a href=#106 id=106 data-nosnippet>106</a></span><span class="kw">pub fn </span>is_builtin_key_or_sysvar(key: <span class="kw-2">&amp;</span>Pubkey) -&gt; bool {
<a href=#107 id=107 data-nosnippet>107</a>    <span class="kw">if </span>MAYBE_BUILTIN_KEY_OR_SYSVAR[key.as_ref()[<span class="number">0</span>] <span class="kw">as </span>usize] {
<a href=#108 id=108 data-nosnippet>108</a>        <span class="kw">return </span>is_sysvar_id(key) || BUILTIN_PROGRAMS_KEYS.contains(key);
<a href=#109 id=109 data-nosnippet>109</a>    }
<a href=#110 id=110 data-nosnippet>110</a>    <span class="bool-val">false
<a href=#111 id=111 data-nosnippet>111</a></span>}
<a href=#112 id=112 data-nosnippet>112</a>
<a href=#113 id=113 data-nosnippet>113</a><span class="kw">fn </span>position(keys: <span class="kw-2">&amp;</span>[Pubkey], key: <span class="kw-2">&amp;</span>Pubkey) -&gt; u8 {
<a href=#114 id=114 data-nosnippet>114</a>    keys.iter().position(|k| k == key).unwrap() <span class="kw">as </span>u8
<a href=#115 id=115 data-nosnippet>115</a>}
<a href=#116 id=116 data-nosnippet>116</a>
<a href=#117 id=117 data-nosnippet>117</a><span class="kw">fn </span>compile_instruction(ix: <span class="kw-2">&amp;</span>Instruction, keys: <span class="kw-2">&amp;</span>[Pubkey]) -&gt; CompiledInstruction {
<a href=#118 id=118 data-nosnippet>118</a>    <span class="kw">let </span>accounts: Vec&lt;<span class="kw">_</span>&gt; = ix
<a href=#119 id=119 data-nosnippet>119</a>        .accounts
<a href=#120 id=120 data-nosnippet>120</a>        .iter()
<a href=#121 id=121 data-nosnippet>121</a>        .map(|account_meta| position(keys, <span class="kw-2">&amp;</span>account_meta.pubkey))
<a href=#122 id=122 data-nosnippet>122</a>        .collect();
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>    CompiledInstruction {
<a href=#125 id=125 data-nosnippet>125</a>        program_id_index: position(keys, <span class="kw-2">&amp;</span>ix.program_id),
<a href=#126 id=126 data-nosnippet>126</a>        data: ix.data.clone(),
<a href=#127 id=127 data-nosnippet>127</a>        accounts,
<a href=#128 id=128 data-nosnippet>128</a>    }
<a href=#129 id=129 data-nosnippet>129</a>}
<a href=#130 id=130 data-nosnippet>130</a>
<a href=#131 id=131 data-nosnippet>131</a><span class="kw">fn </span>compile_instructions(ixs: <span class="kw-2">&amp;</span>[Instruction], keys: <span class="kw-2">&amp;</span>[Pubkey]) -&gt; Vec&lt;CompiledInstruction&gt; {
<a href=#132 id=132 data-nosnippet>132</a>    ixs.iter().map(|ix| compile_instruction(ix, keys)).collect()
<a href=#133 id=133 data-nosnippet>133</a>}
<a href=#134 id=134 data-nosnippet>134</a>
<a href=#135 id=135 data-nosnippet>135</a><span class="doccomment">/// A Solana transaction message (legacy).
<a href=#136 id=136 data-nosnippet>136</a>///
<a href=#137 id=137 data-nosnippet>137</a>/// See the crate documentation for further description.
<a href=#138 id=138 data-nosnippet>138</a>///
<a href=#139 id=139 data-nosnippet>139</a>/// Some constructors accept an optional `payer`, the account responsible for
<a href=#140 id=140 data-nosnippet>140</a>/// paying the cost of executing a transaction. In most cases, callers should
<a href=#141 id=141 data-nosnippet>141</a>/// specify the payer explicitly in these constructors. In some cases though,
<a href=#142 id=142 data-nosnippet>142</a>/// the caller is not _required_ to specify the payer, but is still allowed to:
<a href=#143 id=143 data-nosnippet>143</a>/// in the `Message` structure, the first account is always the fee-payer, so if
<a href=#144 id=144 data-nosnippet>144</a>/// the caller has knowledge that the first account of the constructed
<a href=#145 id=145 data-nosnippet>145</a>/// transaction's `Message` is both a signer and the expected fee-payer, then
<a href=#146 id=146 data-nosnippet>146</a>/// redundantly specifying the fee-payer is not strictly required.
<a href=#147 id=147 data-nosnippet>147</a></span><span class="comment">// NOTE: Serialization-related changes must be paired with the custom serialization
<a href=#148 id=148 data-nosnippet>148</a>// for versioned messages in the `RemainingLegacyMessage` struct.
<a href=#149 id=149 data-nosnippet>149</a></span><span class="attr">#[cfg(not(target_arch = <span class="string">"wasm32"</span>))]
<a href=#150 id=150 data-nosnippet>150</a>#[cfg_attr(
<a href=#151 id=151 data-nosnippet>151</a>    feature = <span class="string">"frozen-abi"</span>,
<a href=#152 id=152 data-nosnippet>152</a>    frozen_abi(digest = <span class="string">"2THeaWnXSGDTsiadKytJTcbjrk4KjfMww9arRLZcwGnw"</span>),
<a href=#153 id=153 data-nosnippet>153</a>    derive(AbiExample)
<a href=#154 id=154 data-nosnippet>154</a>)]
<a href=#155 id=155 data-nosnippet>155</a>#[cfg_attr(
<a href=#156 id=156 data-nosnippet>156</a>    feature = <span class="string">"serde"</span>,
<a href=#157 id=157 data-nosnippet>157</a>    derive(Deserialize, Serialize),
<a href=#158 id=158 data-nosnippet>158</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#159 id=159 data-nosnippet>159</a>)]
<a href=#160 id=160 data-nosnippet>160</a>#[derive(Default, Debug, PartialEq, Eq, Clone)]
<a href=#161 id=161 data-nosnippet>161</a></span><span class="kw">pub struct </span>Message {
<a href=#162 id=162 data-nosnippet>162</a>    <span class="doccomment">/// The message header, identifying signed and read-only `account_keys`.
<a href=#163 id=163 data-nosnippet>163</a>    </span><span class="comment">// NOTE: Serialization-related changes must be paired with the direct read at sigverify.
<a href=#164 id=164 data-nosnippet>164</a>    </span><span class="kw">pub </span>header: MessageHeader,
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>    <span class="doccomment">/// All the account keys used by this transaction.
<a href=#167 id=167 data-nosnippet>167</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#168 id=168 data-nosnippet>168</a>    </span><span class="kw">pub </span>account_keys: Vec&lt;Pubkey&gt;,
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>    <span class="doccomment">/// The id of a recent ledger entry.
<a href=#171 id=171 data-nosnippet>171</a>    </span><span class="kw">pub </span>recent_blockhash: Hash,
<a href=#172 id=172 data-nosnippet>172</a>
<a href=#173 id=173 data-nosnippet>173</a>    <span class="doccomment">/// Programs that will be executed in sequence and committed in one atomic transaction if all
<a href=#174 id=174 data-nosnippet>174</a>    /// succeed.
<a href=#175 id=175 data-nosnippet>175</a>    </span><span class="attr">#[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#176 id=176 data-nosnippet>176</a>    </span><span class="kw">pub </span>instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#177 id=177 data-nosnippet>177</a>}
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a><span class="doccomment">/// wasm-bindgen version of the Message struct.
<a href=#180 id=180 data-nosnippet>180</a>/// This duplication is required until https://github.com/rustwasm/wasm-bindgen/issues/3671
<a href=#181 id=181 data-nosnippet>181</a>/// is fixed. This must not diverge from the regular non-wasm Message struct.
<a href=#182 id=182 data-nosnippet>182</a></span><span class="attr">#[cfg(target_arch = <span class="string">"wasm32"</span>)]
<a href=#183 id=183 data-nosnippet>183</a>#[wasm_bindgen]
<a href=#184 id=184 data-nosnippet>184</a>#[cfg_attr(
<a href=#185 id=185 data-nosnippet>185</a>    feature = <span class="string">"frozen-abi"</span>,
<a href=#186 id=186 data-nosnippet>186</a>    frozen_abi(digest = <span class="string">"2THeaWnXSGDTsiadKytJTcbjrk4KjfMww9arRLZcwGnw"</span>),
<a href=#187 id=187 data-nosnippet>187</a>    derive(AbiExample)
<a href=#188 id=188 data-nosnippet>188</a>)]
<a href=#189 id=189 data-nosnippet>189</a>#[cfg_attr(
<a href=#190 id=190 data-nosnippet>190</a>    feature = <span class="string">"serde"</span>,
<a href=#191 id=191 data-nosnippet>191</a>    derive(Deserialize, Serialize),
<a href=#192 id=192 data-nosnippet>192</a>    serde(rename_all = <span class="string">"camelCase"</span>)
<a href=#193 id=193 data-nosnippet>193</a>)]
<a href=#194 id=194 data-nosnippet>194</a>#[derive(Default, Debug, PartialEq, Eq, Clone)]
<a href=#195 id=195 data-nosnippet>195</a></span><span class="kw">pub struct </span>Message {
<a href=#196 id=196 data-nosnippet>196</a>    <span class="attr">#[wasm_bindgen(skip)]
<a href=#197 id=197 data-nosnippet>197</a>    </span><span class="kw">pub </span>header: MessageHeader,
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>    <span class="attr">#[wasm_bindgen(skip)]
<a href=#200 id=200 data-nosnippet>200</a>    #[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#201 id=201 data-nosnippet>201</a>    </span><span class="kw">pub </span>account_keys: Vec&lt;Pubkey&gt;,
<a href=#202 id=202 data-nosnippet>202</a>
<a href=#203 id=203 data-nosnippet>203</a>    <span class="doccomment">/// The id of a recent ledger entry.
<a href=#204 id=204 data-nosnippet>204</a>    </span><span class="kw">pub </span>recent_blockhash: Hash,
<a href=#205 id=205 data-nosnippet>205</a>
<a href=#206 id=206 data-nosnippet>206</a>    <span class="attr">#[wasm_bindgen(skip)]
<a href=#207 id=207 data-nosnippet>207</a>    #[cfg_attr(feature = <span class="string">"serde"</span>, serde(with = <span class="string">"solana_short_vec"</span>))]
<a href=#208 id=208 data-nosnippet>208</a>    </span><span class="kw">pub </span>instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#209 id=209 data-nosnippet>209</a>}
<a href=#210 id=210 data-nosnippet>210</a>
<a href=#211 id=211 data-nosnippet>211</a><span class="kw">impl </span>Sanitize <span class="kw">for </span>Message {
<a href=#212 id=212 data-nosnippet>212</a>    <span class="kw">fn </span>sanitize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; std::result::Result&lt;(), SanitizeError&gt; {
<a href=#213 id=213 data-nosnippet>213</a>        <span class="comment">// signing area and read-only non-signing area should not overlap
<a href=#214 id=214 data-nosnippet>214</a>        </span><span class="kw">if </span><span class="self">self</span>.header.num_required_signatures <span class="kw">as </span>usize
<a href=#215 id=215 data-nosnippet>215</a>            + <span class="self">self</span>.header.num_readonly_unsigned_accounts <span class="kw">as </span>usize
<a href=#216 id=216 data-nosnippet>216</a>            &gt; <span class="self">self</span>.account_keys.len()
<a href=#217 id=217 data-nosnippet>217</a>        {
<a href=#218 id=218 data-nosnippet>218</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#219 id=219 data-nosnippet>219</a>        }
<a href=#220 id=220 data-nosnippet>220</a>
<a href=#221 id=221 data-nosnippet>221</a>        <span class="comment">// there should be at least 1 RW fee-payer account.
<a href=#222 id=222 data-nosnippet>222</a>        </span><span class="kw">if </span><span class="self">self</span>.header.num_readonly_signed_accounts &gt;= <span class="self">self</span>.header.num_required_signatures {
<a href=#223 id=223 data-nosnippet>223</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#224 id=224 data-nosnippet>224</a>        }
<a href=#225 id=225 data-nosnippet>225</a>
<a href=#226 id=226 data-nosnippet>226</a>        <span class="kw">for </span>ci <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.instructions {
<a href=#227 id=227 data-nosnippet>227</a>            <span class="kw">if </span>ci.program_id_index <span class="kw">as </span>usize &gt;= <span class="self">self</span>.account_keys.len() {
<a href=#228 id=228 data-nosnippet>228</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#229 id=229 data-nosnippet>229</a>            }
<a href=#230 id=230 data-nosnippet>230</a>            <span class="comment">// A program cannot be a payer.
<a href=#231 id=231 data-nosnippet>231</a>            </span><span class="kw">if </span>ci.program_id_index == <span class="number">0 </span>{
<a href=#232 id=232 data-nosnippet>232</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#233 id=233 data-nosnippet>233</a>            }
<a href=#234 id=234 data-nosnippet>234</a>            <span class="kw">for </span>ai <span class="kw">in </span><span class="kw-2">&amp;</span>ci.accounts {
<a href=#235 id=235 data-nosnippet>235</a>                <span class="kw">if </span><span class="kw-2">*</span>ai <span class="kw">as </span>usize &gt;= <span class="self">self</span>.account_keys.len() {
<a href=#236 id=236 data-nosnippet>236</a>                    <span class="kw">return </span><span class="prelude-val">Err</span>(SanitizeError::IndexOutOfBounds);
<a href=#237 id=237 data-nosnippet>237</a>                }
<a href=#238 id=238 data-nosnippet>238</a>            }
<a href=#239 id=239 data-nosnippet>239</a>        }
<a href=#240 id=240 data-nosnippet>240</a>        <span class="self">self</span>.account_keys.sanitize()<span class="question-mark">?</span>;
<a href=#241 id=241 data-nosnippet>241</a>        <span class="self">self</span>.recent_blockhash.sanitize()<span class="question-mark">?</span>;
<a href=#242 id=242 data-nosnippet>242</a>        <span class="self">self</span>.instructions.sanitize()<span class="question-mark">?</span>;
<a href=#243 id=243 data-nosnippet>243</a>        <span class="prelude-val">Ok</span>(())
<a href=#244 id=244 data-nosnippet>244</a>    }
<a href=#245 id=245 data-nosnippet>245</a>}
<a href=#246 id=246 data-nosnippet>246</a>
<a href=#247 id=247 data-nosnippet>247</a><span class="kw">impl </span>Message {
<a href=#248 id=248 data-nosnippet>248</a>    <span class="doccomment">/// Create a new `Message`.
<a href=#249 id=249 data-nosnippet>249</a>    ///
<a href=#250 id=250 data-nosnippet>250</a>    /// # Examples
<a href=#251 id=251 data-nosnippet>251</a>    ///
<a href=#252 id=252 data-nosnippet>252</a>    /// This example uses the [`solana_sdk`], [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#253 id=253 data-nosnippet>253</a>    ///
<a href=#254 id=254 data-nosnippet>254</a>    /// [`solana_sdk`]: https://docs.rs/solana-sdk
<a href=#255 id=255 data-nosnippet>255</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#256 id=256 data-nosnippet>256</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#257 id=257 data-nosnippet>257</a>    ///
<a href=#258 id=258 data-nosnippet>258</a>    /// ```
<a href=#259 id=259 data-nosnippet>259</a>    /// # use solana_program::example_mocks::solana_sdk;
<a href=#260 id=260 data-nosnippet>260</a>    /// # use solana_program::example_mocks::solana_rpc_client;
<a href=#261 id=261 data-nosnippet>261</a>    /// use anyhow::Result;
<a href=#262 id=262 data-nosnippet>262</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#263 id=263 data-nosnippet>263</a>    /// use solana_instruction::Instruction;
<a href=#264 id=264 data-nosnippet>264</a>    /// use solana_message::Message;
<a href=#265 id=265 data-nosnippet>265</a>    /// use solana_pubkey::Pubkey;
<a href=#266 id=266 data-nosnippet>266</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#267 id=267 data-nosnippet>267</a>    /// use solana_sdk::{
<a href=#268 id=268 data-nosnippet>268</a>    ///     signature::{Keypair, Signer},
<a href=#269 id=269 data-nosnippet>269</a>    ///     transaction::Transaction,
<a href=#270 id=270 data-nosnippet>270</a>    /// };
<a href=#271 id=271 data-nosnippet>271</a>    ///
<a href=#272 id=272 data-nosnippet>272</a>    /// // A custom program instruction. This would typically be defined in
<a href=#273 id=273 data-nosnippet>273</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#274 id=274 data-nosnippet>274</a>    /// // the client.
<a href=#275 id=275 data-nosnippet>275</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#276 id=276 data-nosnippet>276</a>    /// # #[borsh(crate = "borsh")]
<a href=#277 id=277 data-nosnippet>277</a>    /// enum BankInstruction {
<a href=#278 id=278 data-nosnippet>278</a>    ///     Initialize,
<a href=#279 id=279 data-nosnippet>279</a>    ///     Deposit { lamports: u64 },
<a href=#280 id=280 data-nosnippet>280</a>    ///     Withdraw { lamports: u64 },
<a href=#281 id=281 data-nosnippet>281</a>    /// }
<a href=#282 id=282 data-nosnippet>282</a>    ///
<a href=#283 id=283 data-nosnippet>283</a>    /// fn send_initialize_tx(
<a href=#284 id=284 data-nosnippet>284</a>    ///     client: &amp;RpcClient,
<a href=#285 id=285 data-nosnippet>285</a>    ///     program_id: Pubkey,
<a href=#286 id=286 data-nosnippet>286</a>    ///     payer: &amp;Keypair
<a href=#287 id=287 data-nosnippet>287</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#288 id=288 data-nosnippet>288</a>    ///
<a href=#289 id=289 data-nosnippet>289</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#290 id=290 data-nosnippet>290</a>    ///
<a href=#291 id=291 data-nosnippet>291</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#292 id=292 data-nosnippet>292</a>    ///         program_id,
<a href=#293 id=293 data-nosnippet>293</a>    ///         &amp;bank_instruction,
<a href=#294 id=294 data-nosnippet>294</a>    ///         vec![],
<a href=#295 id=295 data-nosnippet>295</a>    ///     );
<a href=#296 id=296 data-nosnippet>296</a>    ///
<a href=#297 id=297 data-nosnippet>297</a>    ///     let message = Message::new(
<a href=#298 id=298 data-nosnippet>298</a>    ///         &amp;[instruction],
<a href=#299 id=299 data-nosnippet>299</a>    ///         Some(&amp;payer.pubkey()),
<a href=#300 id=300 data-nosnippet>300</a>    ///     );
<a href=#301 id=301 data-nosnippet>301</a>    ///
<a href=#302 id=302 data-nosnippet>302</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#303 id=303 data-nosnippet>303</a>    ///     let mut tx = Transaction::new(&amp;[payer], message, blockhash);
<a href=#304 id=304 data-nosnippet>304</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#305 id=305 data-nosnippet>305</a>    ///
<a href=#306 id=306 data-nosnippet>306</a>    ///     Ok(())
<a href=#307 id=307 data-nosnippet>307</a>    /// }
<a href=#308 id=308 data-nosnippet>308</a>    /// #
<a href=#309 id=309 data-nosnippet>309</a>    /// # let client = RpcClient::new(String::new());
<a href=#310 id=310 data-nosnippet>310</a>    /// # let program_id = Pubkey::new_unique();
<a href=#311 id=311 data-nosnippet>311</a>    /// # let payer = Keypair::new();
<a href=#312 id=312 data-nosnippet>312</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#313 id=313 data-nosnippet>313</a>    /// #
<a href=#314 id=314 data-nosnippet>314</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#315 id=315 data-nosnippet>315</a>    /// ```
<a href=#316 id=316 data-nosnippet>316</a>    </span><span class="kw">pub fn </span>new(instructions: <span class="kw-2">&amp;</span>[Instruction], payer: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt;) -&gt; <span class="self">Self </span>{
<a href=#317 id=317 data-nosnippet>317</a>        <span class="self">Self</span>::new_with_blockhash(instructions, payer, <span class="kw-2">&amp;</span>Hash::default())
<a href=#318 id=318 data-nosnippet>318</a>    }
<a href=#319 id=319 data-nosnippet>319</a>
<a href=#320 id=320 data-nosnippet>320</a>    <span class="doccomment">/// Create a new message while setting the blockhash.
<a href=#321 id=321 data-nosnippet>321</a>    ///
<a href=#322 id=322 data-nosnippet>322</a>    /// # Examples
<a href=#323 id=323 data-nosnippet>323</a>    ///
<a href=#324 id=324 data-nosnippet>324</a>    /// This example uses the [`solana_sdk`], [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#325 id=325 data-nosnippet>325</a>    ///
<a href=#326 id=326 data-nosnippet>326</a>    /// [`solana_sdk`]: https://docs.rs/solana-sdk
<a href=#327 id=327 data-nosnippet>327</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-rpc-client
<a href=#328 id=328 data-nosnippet>328</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#329 id=329 data-nosnippet>329</a>    ///
<a href=#330 id=330 data-nosnippet>330</a>    /// ```
<a href=#331 id=331 data-nosnippet>331</a>    /// # use solana_program::example_mocks::solana_sdk;
<a href=#332 id=332 data-nosnippet>332</a>    /// # use solana_program::example_mocks::solana_rpc_client;
<a href=#333 id=333 data-nosnippet>333</a>    /// use anyhow::Result;
<a href=#334 id=334 data-nosnippet>334</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#335 id=335 data-nosnippet>335</a>    /// use solana_instruction::Instruction;
<a href=#336 id=336 data-nosnippet>336</a>    /// use solana_message::Message;
<a href=#337 id=337 data-nosnippet>337</a>    /// use solana_pubkey::Pubkey;
<a href=#338 id=338 data-nosnippet>338</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#339 id=339 data-nosnippet>339</a>    /// use solana_sdk::{
<a href=#340 id=340 data-nosnippet>340</a>    ///     signature::{Keypair, Signer},
<a href=#341 id=341 data-nosnippet>341</a>    ///     transaction::Transaction,
<a href=#342 id=342 data-nosnippet>342</a>    /// };
<a href=#343 id=343 data-nosnippet>343</a>    ///
<a href=#344 id=344 data-nosnippet>344</a>    /// // A custom program instruction. This would typically be defined in
<a href=#345 id=345 data-nosnippet>345</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#346 id=346 data-nosnippet>346</a>    /// // the client.
<a href=#347 id=347 data-nosnippet>347</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#348 id=348 data-nosnippet>348</a>    /// # #[borsh(crate = "borsh")]
<a href=#349 id=349 data-nosnippet>349</a>    /// enum BankInstruction {
<a href=#350 id=350 data-nosnippet>350</a>    ///     Initialize,
<a href=#351 id=351 data-nosnippet>351</a>    ///     Deposit { lamports: u64 },
<a href=#352 id=352 data-nosnippet>352</a>    ///     Withdraw { lamports: u64 },
<a href=#353 id=353 data-nosnippet>353</a>    /// }
<a href=#354 id=354 data-nosnippet>354</a>    ///
<a href=#355 id=355 data-nosnippet>355</a>    /// fn send_initialize_tx(
<a href=#356 id=356 data-nosnippet>356</a>    ///     client: &amp;RpcClient,
<a href=#357 id=357 data-nosnippet>357</a>    ///     program_id: Pubkey,
<a href=#358 id=358 data-nosnippet>358</a>    ///     payer: &amp;Keypair
<a href=#359 id=359 data-nosnippet>359</a>    /// ) -&gt; Result&lt;()&gt; {
<a href=#360 id=360 data-nosnippet>360</a>    ///
<a href=#361 id=361 data-nosnippet>361</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#362 id=362 data-nosnippet>362</a>    ///
<a href=#363 id=363 data-nosnippet>363</a>    ///     let instruction = Instruction::new_with_borsh(
<a href=#364 id=364 data-nosnippet>364</a>    ///         program_id,
<a href=#365 id=365 data-nosnippet>365</a>    ///         &amp;bank_instruction,
<a href=#366 id=366 data-nosnippet>366</a>    ///         vec![],
<a href=#367 id=367 data-nosnippet>367</a>    ///     );
<a href=#368 id=368 data-nosnippet>368</a>    ///
<a href=#369 id=369 data-nosnippet>369</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#370 id=370 data-nosnippet>370</a>    ///
<a href=#371 id=371 data-nosnippet>371</a>    ///     let message = Message::new_with_blockhash(
<a href=#372 id=372 data-nosnippet>372</a>    ///         &amp;[instruction],
<a href=#373 id=373 data-nosnippet>373</a>    ///         Some(&amp;payer.pubkey()),
<a href=#374 id=374 data-nosnippet>374</a>    ///         &amp;blockhash,
<a href=#375 id=375 data-nosnippet>375</a>    ///     );
<a href=#376 id=376 data-nosnippet>376</a>    ///
<a href=#377 id=377 data-nosnippet>377</a>    ///     let mut tx = Transaction::new_unsigned(message);
<a href=#378 id=378 data-nosnippet>378</a>    ///     tx.sign(&amp;[payer], tx.message.recent_blockhash);
<a href=#379 id=379 data-nosnippet>379</a>    ///     client.send_and_confirm_transaction(&amp;tx)?;
<a href=#380 id=380 data-nosnippet>380</a>    ///
<a href=#381 id=381 data-nosnippet>381</a>    ///     Ok(())
<a href=#382 id=382 data-nosnippet>382</a>    /// }
<a href=#383 id=383 data-nosnippet>383</a>    /// #
<a href=#384 id=384 data-nosnippet>384</a>    /// # let client = RpcClient::new(String::new());
<a href=#385 id=385 data-nosnippet>385</a>    /// # let program_id = Pubkey::new_unique();
<a href=#386 id=386 data-nosnippet>386</a>    /// # let payer = Keypair::new();
<a href=#387 id=387 data-nosnippet>387</a>    /// # send_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#388 id=388 data-nosnippet>388</a>    /// #
<a href=#389 id=389 data-nosnippet>389</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#390 id=390 data-nosnippet>390</a>    /// ```
<a href=#391 id=391 data-nosnippet>391</a>    </span><span class="kw">pub fn </span>new_with_blockhash(
<a href=#392 id=392 data-nosnippet>392</a>        instructions: <span class="kw-2">&amp;</span>[Instruction],
<a href=#393 id=393 data-nosnippet>393</a>        payer: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt;,
<a href=#394 id=394 data-nosnippet>394</a>        blockhash: <span class="kw-2">&amp;</span>Hash,
<a href=#395 id=395 data-nosnippet>395</a>    ) -&gt; <span class="self">Self </span>{
<a href=#396 id=396 data-nosnippet>396</a>        <span class="kw">let </span>compiled_keys = CompiledKeys::compile(instructions, payer.cloned());
<a href=#397 id=397 data-nosnippet>397</a>        <span class="kw">let </span>(header, account_keys) = compiled_keys
<a href=#398 id=398 data-nosnippet>398</a>            .try_into_message_components()
<a href=#399 id=399 data-nosnippet>399</a>            .expect(<span class="string">"overflow when compiling message keys"</span>);
<a href=#400 id=400 data-nosnippet>400</a>        <span class="kw">let </span>instructions = compile_instructions(instructions, <span class="kw-2">&amp;</span>account_keys);
<a href=#401 id=401 data-nosnippet>401</a>        <span class="self">Self</span>::new_with_compiled_instructions(
<a href=#402 id=402 data-nosnippet>402</a>            header.num_required_signatures,
<a href=#403 id=403 data-nosnippet>403</a>            header.num_readonly_signed_accounts,
<a href=#404 id=404 data-nosnippet>404</a>            header.num_readonly_unsigned_accounts,
<a href=#405 id=405 data-nosnippet>405</a>            account_keys,
<a href=#406 id=406 data-nosnippet>406</a>            <span class="kw-2">*</span>blockhash,
<a href=#407 id=407 data-nosnippet>407</a>            instructions,
<a href=#408 id=408 data-nosnippet>408</a>        )
<a href=#409 id=409 data-nosnippet>409</a>    }
<a href=#410 id=410 data-nosnippet>410</a>
<a href=#411 id=411 data-nosnippet>411</a>    <span class="doccomment">/// Create a new message for a [nonced transaction].
<a href=#412 id=412 data-nosnippet>412</a>    ///
<a href=#413 id=413 data-nosnippet>413</a>    /// [nonced transaction]: https://docs.solanalabs.com/implemented-proposals/durable-tx-nonces
<a href=#414 id=414 data-nosnippet>414</a>    ///
<a href=#415 id=415 data-nosnippet>415</a>    /// In this type of transaction, the blockhash is replaced with a _durable
<a href=#416 id=416 data-nosnippet>416</a>    /// transaction nonce_, allowing for extended time to pass between the
<a href=#417 id=417 data-nosnippet>417</a>    /// transaction's signing and submission to the blockchain.
<a href=#418 id=418 data-nosnippet>418</a>    ///
<a href=#419 id=419 data-nosnippet>419</a>    /// # Examples
<a href=#420 id=420 data-nosnippet>420</a>    ///
<a href=#421 id=421 data-nosnippet>421</a>    /// This example uses the [`solana_sdk`], [`solana_rpc_client`] and [`anyhow`] crates.
<a href=#422 id=422 data-nosnippet>422</a>    ///
<a href=#423 id=423 data-nosnippet>423</a>    /// [`solana_sdk`]: https://docs.rs/solana-sdk
<a href=#424 id=424 data-nosnippet>424</a>    /// [`solana_rpc_client`]: https://docs.rs/solana-client
<a href=#425 id=425 data-nosnippet>425</a>    /// [`anyhow`]: https://docs.rs/anyhow
<a href=#426 id=426 data-nosnippet>426</a>    ///
<a href=#427 id=427 data-nosnippet>427</a>    /// ```
<a href=#428 id=428 data-nosnippet>428</a>    /// # use solana_program::example_mocks::solana_sdk;
<a href=#429 id=429 data-nosnippet>429</a>    /// # use solana_program::example_mocks::solana_rpc_client;
<a href=#430 id=430 data-nosnippet>430</a>    /// use anyhow::Result;
<a href=#431 id=431 data-nosnippet>431</a>    /// use borsh::{BorshSerialize, BorshDeserialize};
<a href=#432 id=432 data-nosnippet>432</a>    /// use solana_hash::Hash;
<a href=#433 id=433 data-nosnippet>433</a>    /// use solana_instruction::Instruction;
<a href=#434 id=434 data-nosnippet>434</a>    /// use solana_message::Message;
<a href=#435 id=435 data-nosnippet>435</a>    /// use solana_pubkey::Pubkey;
<a href=#436 id=436 data-nosnippet>436</a>    /// use solana_rpc_client::rpc_client::RpcClient;
<a href=#437 id=437 data-nosnippet>437</a>    /// use solana_sdk::{
<a href=#438 id=438 data-nosnippet>438</a>    ///     signature::{Keypair, Signer},
<a href=#439 id=439 data-nosnippet>439</a>    ///     transaction::Transaction,
<a href=#440 id=440 data-nosnippet>440</a>    /// };
<a href=#441 id=441 data-nosnippet>441</a>    /// use solana_system_interface::instruction::create_nonce_account;
<a href=#442 id=442 data-nosnippet>442</a>    ///
<a href=#443 id=443 data-nosnippet>443</a>    /// // A custom program instruction. This would typically be defined in
<a href=#444 id=444 data-nosnippet>444</a>    /// // another crate so it can be shared between the on-chain program and
<a href=#445 id=445 data-nosnippet>445</a>    /// // the client.
<a href=#446 id=446 data-nosnippet>446</a>    /// #[derive(BorshSerialize, BorshDeserialize)]
<a href=#447 id=447 data-nosnippet>447</a>    /// # #[borsh(crate = "borsh")]
<a href=#448 id=448 data-nosnippet>448</a>    /// enum BankInstruction {
<a href=#449 id=449 data-nosnippet>449</a>    ///     Initialize,
<a href=#450 id=450 data-nosnippet>450</a>    ///     Deposit { lamports: u64 },
<a href=#451 id=451 data-nosnippet>451</a>    ///     Withdraw { lamports: u64 },
<a href=#452 id=452 data-nosnippet>452</a>    /// }
<a href=#453 id=453 data-nosnippet>453</a>    ///
<a href=#454 id=454 data-nosnippet>454</a>    /// // Create a nonced transaction for later signing and submission,
<a href=#455 id=455 data-nosnippet>455</a>    /// // returning it and the nonce account's pubkey.
<a href=#456 id=456 data-nosnippet>456</a>    /// fn create_offline_initialize_tx(
<a href=#457 id=457 data-nosnippet>457</a>    ///     client: &amp;RpcClient,
<a href=#458 id=458 data-nosnippet>458</a>    ///     program_id: Pubkey,
<a href=#459 id=459 data-nosnippet>459</a>    ///     payer: &amp;Keypair
<a href=#460 id=460 data-nosnippet>460</a>    /// ) -&gt; Result&lt;(Transaction, Pubkey)&gt; {
<a href=#461 id=461 data-nosnippet>461</a>    ///
<a href=#462 id=462 data-nosnippet>462</a>    ///     let bank_instruction = BankInstruction::Initialize;
<a href=#463 id=463 data-nosnippet>463</a>    ///     let bank_instruction = Instruction::new_with_borsh(
<a href=#464 id=464 data-nosnippet>464</a>    ///         program_id,
<a href=#465 id=465 data-nosnippet>465</a>    ///         &amp;bank_instruction,
<a href=#466 id=466 data-nosnippet>466</a>    ///         vec![],
<a href=#467 id=467 data-nosnippet>467</a>    ///     );
<a href=#468 id=468 data-nosnippet>468</a>    ///
<a href=#469 id=469 data-nosnippet>469</a>    ///     // This will create a nonce account and assign authority to the
<a href=#470 id=470 data-nosnippet>470</a>    ///     // payer so they can sign to advance the nonce and withdraw its rent.
<a href=#471 id=471 data-nosnippet>471</a>    ///     let nonce_account = make_nonce_account(client, payer)?;
<a href=#472 id=472 data-nosnippet>472</a>    ///
<a href=#473 id=473 data-nosnippet>473</a>    ///     let mut message = Message::new_with_nonce(
<a href=#474 id=474 data-nosnippet>474</a>    ///         vec![bank_instruction],
<a href=#475 id=475 data-nosnippet>475</a>    ///         Some(&amp;payer.pubkey()),
<a href=#476 id=476 data-nosnippet>476</a>    ///         &amp;nonce_account,
<a href=#477 id=477 data-nosnippet>477</a>    ///         &amp;payer.pubkey()
<a href=#478 id=478 data-nosnippet>478</a>    ///     );
<a href=#479 id=479 data-nosnippet>479</a>    ///
<a href=#480 id=480 data-nosnippet>480</a>    ///     // This transaction will need to be signed later, using the blockhash
<a href=#481 id=481 data-nosnippet>481</a>    ///     // stored in the nonce account.
<a href=#482 id=482 data-nosnippet>482</a>    ///     let tx = Transaction::new_unsigned(message);
<a href=#483 id=483 data-nosnippet>483</a>    ///
<a href=#484 id=484 data-nosnippet>484</a>    ///     Ok((tx, nonce_account))
<a href=#485 id=485 data-nosnippet>485</a>    /// }
<a href=#486 id=486 data-nosnippet>486</a>    ///
<a href=#487 id=487 data-nosnippet>487</a>    /// fn make_nonce_account(client: &amp;RpcClient, payer: &amp;Keypair)
<a href=#488 id=488 data-nosnippet>488</a>    ///     -&gt; Result&lt;Pubkey&gt;
<a href=#489 id=489 data-nosnippet>489</a>    /// {
<a href=#490 id=490 data-nosnippet>490</a>    ///     let nonce_account_address = Keypair::new();
<a href=#491 id=491 data-nosnippet>491</a>    ///     let nonce_account_size = solana_nonce::state::State::size();
<a href=#492 id=492 data-nosnippet>492</a>    ///     let nonce_rent = client.get_minimum_balance_for_rent_exemption(nonce_account_size)?;
<a href=#493 id=493 data-nosnippet>493</a>    ///
<a href=#494 id=494 data-nosnippet>494</a>    ///     // Assigning the nonce authority to the payer so they can sign for the withdrawal,
<a href=#495 id=495 data-nosnippet>495</a>    ///     // and we can throw away the nonce address secret key.
<a href=#496 id=496 data-nosnippet>496</a>    ///     let create_nonce_instr = create_nonce_account(
<a href=#497 id=497 data-nosnippet>497</a>    ///         &amp;payer.pubkey(),
<a href=#498 id=498 data-nosnippet>498</a>    ///         &amp;nonce_account_address.pubkey(),
<a href=#499 id=499 data-nosnippet>499</a>    ///         &amp;payer.pubkey(),
<a href=#500 id=500 data-nosnippet>500</a>    ///         nonce_rent,
<a href=#501 id=501 data-nosnippet>501</a>    ///     );
<a href=#502 id=502 data-nosnippet>502</a>    ///
<a href=#503 id=503 data-nosnippet>503</a>    ///     let mut nonce_tx = Transaction::new_with_payer(&amp;create_nonce_instr, Some(&amp;payer.pubkey()));
<a href=#504 id=504 data-nosnippet>504</a>    ///     let blockhash = client.get_latest_blockhash()?;
<a href=#505 id=505 data-nosnippet>505</a>    ///     nonce_tx.sign(&amp;[&amp;payer, &amp;nonce_account_address], blockhash);
<a href=#506 id=506 data-nosnippet>506</a>    ///     client.send_and_confirm_transaction(&amp;nonce_tx)?;
<a href=#507 id=507 data-nosnippet>507</a>    ///
<a href=#508 id=508 data-nosnippet>508</a>    ///     Ok(nonce_account_address.pubkey())
<a href=#509 id=509 data-nosnippet>509</a>    /// }
<a href=#510 id=510 data-nosnippet>510</a>    /// #
<a href=#511 id=511 data-nosnippet>511</a>    /// # let client = RpcClient::new(String::new());
<a href=#512 id=512 data-nosnippet>512</a>    /// # let program_id = Pubkey::new_unique();
<a href=#513 id=513 data-nosnippet>513</a>    /// # let payer = Keypair::new();
<a href=#514 id=514 data-nosnippet>514</a>    /// # create_offline_initialize_tx(&amp;client, program_id, &amp;payer)?;
<a href=#515 id=515 data-nosnippet>515</a>    /// # Ok::&lt;(), anyhow::Error&gt;(())
<a href=#516 id=516 data-nosnippet>516</a>    /// ```
<a href=#517 id=517 data-nosnippet>517</a>    </span><span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#518 id=518 data-nosnippet>518</a>    </span><span class="kw">pub fn </span>new_with_nonce(
<a href=#519 id=519 data-nosnippet>519</a>        <span class="kw-2">mut </span>instructions: Vec&lt;Instruction&gt;,
<a href=#520 id=520 data-nosnippet>520</a>        payer: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt;,
<a href=#521 id=521 data-nosnippet>521</a>        nonce_account_pubkey: <span class="kw-2">&amp;</span>Pubkey,
<a href=#522 id=522 data-nosnippet>522</a>        nonce_authority_pubkey: <span class="kw-2">&amp;</span>Pubkey,
<a href=#523 id=523 data-nosnippet>523</a>    ) -&gt; <span class="self">Self </span>{
<a href=#524 id=524 data-nosnippet>524</a>        <span class="kw">let </span>nonce_ix = solana_system_interface::instruction::advance_nonce_account(
<a href=#525 id=525 data-nosnippet>525</a>            nonce_account_pubkey,
<a href=#526 id=526 data-nosnippet>526</a>            nonce_authority_pubkey,
<a href=#527 id=527 data-nosnippet>527</a>        );
<a href=#528 id=528 data-nosnippet>528</a>        instructions.insert(<span class="number">0</span>, nonce_ix);
<a href=#529 id=529 data-nosnippet>529</a>        <span class="self">Self</span>::new(<span class="kw-2">&amp;</span>instructions, payer)
<a href=#530 id=530 data-nosnippet>530</a>    }
<a href=#531 id=531 data-nosnippet>531</a>
<a href=#532 id=532 data-nosnippet>532</a>    <span class="kw">pub fn </span>new_with_compiled_instructions(
<a href=#533 id=533 data-nosnippet>533</a>        num_required_signatures: u8,
<a href=#534 id=534 data-nosnippet>534</a>        num_readonly_signed_accounts: u8,
<a href=#535 id=535 data-nosnippet>535</a>        num_readonly_unsigned_accounts: u8,
<a href=#536 id=536 data-nosnippet>536</a>        account_keys: Vec&lt;Pubkey&gt;,
<a href=#537 id=537 data-nosnippet>537</a>        recent_blockhash: Hash,
<a href=#538 id=538 data-nosnippet>538</a>        instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#539 id=539 data-nosnippet>539</a>    ) -&gt; <span class="self">Self </span>{
<a href=#540 id=540 data-nosnippet>540</a>        <span class="self">Self </span>{
<a href=#541 id=541 data-nosnippet>541</a>            header: MessageHeader {
<a href=#542 id=542 data-nosnippet>542</a>                num_required_signatures,
<a href=#543 id=543 data-nosnippet>543</a>                num_readonly_signed_accounts,
<a href=#544 id=544 data-nosnippet>544</a>                num_readonly_unsigned_accounts,
<a href=#545 id=545 data-nosnippet>545</a>            },
<a href=#546 id=546 data-nosnippet>546</a>            account_keys,
<a href=#547 id=547 data-nosnippet>547</a>            recent_blockhash,
<a href=#548 id=548 data-nosnippet>548</a>            instructions,
<a href=#549 id=549 data-nosnippet>549</a>        }
<a href=#550 id=550 data-nosnippet>550</a>    }
<a href=#551 id=551 data-nosnippet>551</a>
<a href=#552 id=552 data-nosnippet>552</a>    <span class="doccomment">/// Compute the blake3 hash of this transaction's message.
<a href=#553 id=553 data-nosnippet>553</a>    </span><span class="attr">#[cfg(all(not(target_os = <span class="string">"solana"</span>), feature = <span class="string">"bincode"</span>, feature = <span class="string">"blake3"</span>))]
<a href=#554 id=554 data-nosnippet>554</a>    </span><span class="kw">pub fn </span>hash(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Hash {
<a href=#555 id=555 data-nosnippet>555</a>        <span class="kw">let </span>message_bytes = <span class="self">self</span>.serialize();
<a href=#556 id=556 data-nosnippet>556</a>        <span class="self">Self</span>::hash_raw_message(<span class="kw-2">&amp;</span>message_bytes)
<a href=#557 id=557 data-nosnippet>557</a>    }
<a href=#558 id=558 data-nosnippet>558</a>
<a href=#559 id=559 data-nosnippet>559</a>    <span class="doccomment">/// Compute the blake3 hash of a raw transaction message.
<a href=#560 id=560 data-nosnippet>560</a>    </span><span class="attr">#[cfg(all(not(target_os = <span class="string">"solana"</span>), feature = <span class="string">"blake3"</span>))]
<a href=#561 id=561 data-nosnippet>561</a>    </span><span class="kw">pub fn </span>hash_raw_message(message_bytes: <span class="kw-2">&amp;</span>[u8]) -&gt; Hash {
<a href=#562 id=562 data-nosnippet>562</a>        <span class="kw">use </span>{blake3::traits::digest::Digest, solana_hash::HASH_BYTES};
<a href=#563 id=563 data-nosnippet>563</a>        <span class="kw">let </span><span class="kw-2">mut </span>hasher = blake3::Hasher::new();
<a href=#564 id=564 data-nosnippet>564</a>        hasher.update(<span class="string">b"solana-tx-message-v1"</span>);
<a href=#565 id=565 data-nosnippet>565</a>        hasher.update(message_bytes);
<a href=#566 id=566 data-nosnippet>566</a>        <span class="kw">let </span>hash_bytes: [u8; HASH_BYTES] = hasher.finalize().into();
<a href=#567 id=567 data-nosnippet>567</a>        hash_bytes.into()
<a href=#568 id=568 data-nosnippet>568</a>    }
<a href=#569 id=569 data-nosnippet>569</a>
<a href=#570 id=570 data-nosnippet>570</a>    <span class="kw">pub fn </span>compile_instruction(<span class="kw-2">&amp;</span><span class="self">self</span>, ix: <span class="kw-2">&amp;</span>Instruction) -&gt; CompiledInstruction {
<a href=#571 id=571 data-nosnippet>571</a>        compile_instruction(ix, <span class="kw-2">&amp;</span><span class="self">self</span>.account_keys)
<a href=#572 id=572 data-nosnippet>572</a>    }
<a href=#573 id=573 data-nosnippet>573</a>
<a href=#574 id=574 data-nosnippet>574</a>    <span class="attr">#[cfg(feature = <span class="string">"bincode"</span>)]
<a href=#575 id=575 data-nosnippet>575</a>    </span><span class="kw">pub fn </span>serialize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;u8&gt; {
<a href=#576 id=576 data-nosnippet>576</a>        bincode::serialize(<span class="self">self</span>).unwrap()
<a href=#577 id=577 data-nosnippet>577</a>    }
<a href=#578 id=578 data-nosnippet>578</a>
<a href=#579 id=579 data-nosnippet>579</a>    <span class="kw">pub fn </span>program_id(<span class="kw-2">&amp;</span><span class="self">self</span>, instruction_index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#580 id=580 data-nosnippet>580</a>        <span class="prelude-val">Some</span>(
<a href=#581 id=581 data-nosnippet>581</a>            <span class="kw-2">&amp;</span><span class="self">self</span>.account_keys[<span class="self">self</span>.instructions.get(instruction_index)<span class="question-mark">?</span>.program_id_index <span class="kw">as </span>usize],
<a href=#582 id=582 data-nosnippet>582</a>        )
<a href=#583 id=583 data-nosnippet>583</a>    }
<a href=#584 id=584 data-nosnippet>584</a>
<a href=#585 id=585 data-nosnippet>585</a>    <span class="kw">pub fn </span>program_index(<span class="kw-2">&amp;</span><span class="self">self</span>, instruction_index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;usize&gt; {
<a href=#586 id=586 data-nosnippet>586</a>        <span class="prelude-val">Some</span>(<span class="self">self</span>.instructions.get(instruction_index)<span class="question-mark">?</span>.program_id_index <span class="kw">as </span>usize)
<a href=#587 id=587 data-nosnippet>587</a>    }
<a href=#588 id=588 data-nosnippet>588</a>
<a href=#589 id=589 data-nosnippet>589</a>    <span class="kw">pub fn </span>program_ids(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#590 id=590 data-nosnippet>590</a>        <span class="self">self</span>.instructions
<a href=#591 id=591 data-nosnippet>591</a>            .iter()
<a href=#592 id=592 data-nosnippet>592</a>            .map(|ix| <span class="kw-2">&amp;</span><span class="self">self</span>.account_keys[ix.program_id_index <span class="kw">as </span>usize])
<a href=#593 id=593 data-nosnippet>593</a>            .collect()
<a href=#594 id=594 data-nosnippet>594</a>    }
<a href=#595 id=595 data-nosnippet>595</a>
<a href=#596 id=596 data-nosnippet>596</a>    <span class="attr">#[deprecated(since = <span class="string">"2.0.0"</span>, note = <span class="string">"Please use `is_instruction_account` instead"</span>)]
<a href=#597 id=597 data-nosnippet>597</a>    </span><span class="kw">pub fn </span>is_key_passed_to_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#598 id=598 data-nosnippet>598</a>        <span class="self">self</span>.is_instruction_account(key_index)
<a href=#599 id=599 data-nosnippet>599</a>    }
<a href=#600 id=600 data-nosnippet>600</a>
<a href=#601 id=601 data-nosnippet>601</a>    <span class="doccomment">/// Returns true if the account at the specified index is an account input
<a href=#602 id=602 data-nosnippet>602</a>    /// to some program instruction in this message.
<a href=#603 id=603 data-nosnippet>603</a>    </span><span class="kw">pub fn </span>is_instruction_account(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#604 id=604 data-nosnippet>604</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(key_index) = u8::try_from(key_index) {
<a href=#605 id=605 data-nosnippet>605</a>            <span class="self">self</span>.instructions
<a href=#606 id=606 data-nosnippet>606</a>                .iter()
<a href=#607 id=607 data-nosnippet>607</a>                .any(|ix| ix.accounts.contains(<span class="kw-2">&amp;</span>key_index))
<a href=#608 id=608 data-nosnippet>608</a>        } <span class="kw">else </span>{
<a href=#609 id=609 data-nosnippet>609</a>            <span class="bool-val">false
<a href=#610 id=610 data-nosnippet>610</a>        </span>}
<a href=#611 id=611 data-nosnippet>611</a>    }
<a href=#612 id=612 data-nosnippet>612</a>
<a href=#613 id=613 data-nosnippet>613</a>    <span class="kw">pub fn </span>is_key_called_as_program(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#614 id=614 data-nosnippet>614</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(key_index) = u8::try_from(key_index) {
<a href=#615 id=615 data-nosnippet>615</a>            <span class="self">self</span>.instructions
<a href=#616 id=616 data-nosnippet>616</a>                .iter()
<a href=#617 id=617 data-nosnippet>617</a>                .any(|ix| ix.program_id_index == key_index)
<a href=#618 id=618 data-nosnippet>618</a>        } <span class="kw">else </span>{
<a href=#619 id=619 data-nosnippet>619</a>            <span class="bool-val">false
<a href=#620 id=620 data-nosnippet>620</a>        </span>}
<a href=#621 id=621 data-nosnippet>621</a>    }
<a href=#622 id=622 data-nosnippet>622</a>
<a href=#623 id=623 data-nosnippet>623</a>    <span class="attr">#[deprecated(
<a href=#624 id=624 data-nosnippet>624</a>        since = <span class="string">"2.0.0"</span>,
<a href=#625 id=625 data-nosnippet>625</a>        note = <span class="string">"Please use `is_key_called_as_program` and `is_instruction_account` directly"
<a href=#626 id=626 data-nosnippet>626</a>    </span>)]
<a href=#627 id=627 data-nosnippet>627</a>    </span><span class="kw">pub fn </span>is_non_loader_key(<span class="kw-2">&amp;</span><span class="self">self</span>, key_index: usize) -&gt; bool {
<a href=#628 id=628 data-nosnippet>628</a>        !<span class="self">self</span>.is_key_called_as_program(key_index) || <span class="self">self</span>.is_instruction_account(key_index)
<a href=#629 id=629 data-nosnippet>629</a>    }
<a href=#630 id=630 data-nosnippet>630</a>
<a href=#631 id=631 data-nosnippet>631</a>    <span class="kw">pub fn </span>program_position(<span class="kw-2">&amp;</span><span class="self">self</span>, index: usize) -&gt; <span class="prelude-ty">Option</span>&lt;usize&gt; {
<a href=#632 id=632 data-nosnippet>632</a>        <span class="kw">let </span>program_ids = <span class="self">self</span>.program_ids();
<a href=#633 id=633 data-nosnippet>633</a>        program_ids
<a href=#634 id=634 data-nosnippet>634</a>            .iter()
<a href=#635 id=635 data-nosnippet>635</a>            .position(|&amp;&amp;pubkey| pubkey == <span class="self">self</span>.account_keys[index])
<a href=#636 id=636 data-nosnippet>636</a>    }
<a href=#637 id=637 data-nosnippet>637</a>
<a href=#638 id=638 data-nosnippet>638</a>    <span class="kw">pub fn </span>maybe_executable(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#639 id=639 data-nosnippet>639</a>        <span class="self">self</span>.program_position(i).is_some()
<a href=#640 id=640 data-nosnippet>640</a>    }
<a href=#641 id=641 data-nosnippet>641</a>
<a href=#642 id=642 data-nosnippet>642</a>    <span class="kw">pub fn </span>demote_program_id(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#643 id=643 data-nosnippet>643</a>        <span class="self">self</span>.is_key_called_as_program(i) &amp;&amp; !<span class="self">self</span>.is_upgradeable_loader_present()
<a href=#644 id=644 data-nosnippet>644</a>    }
<a href=#645 id=645 data-nosnippet>645</a>
<a href=#646 id=646 data-nosnippet>646</a>    <span class="doccomment">/// Returns true if the account at the specified index was requested to be
<a href=#647 id=647 data-nosnippet>647</a>    /// writable. This method should not be used directly.
<a href=#648 id=648 data-nosnippet>648</a>    </span><span class="kw">pub</span>(<span class="kw">super</span>) <span class="kw">fn </span>is_writable_index(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#649 id=649 data-nosnippet>649</a>        i &lt; (<span class="self">self</span>.header.num_required_signatures - <span class="self">self</span>.header.num_readonly_signed_accounts)
<a href=#650 id=650 data-nosnippet>650</a>            <span class="kw">as </span>usize
<a href=#651 id=651 data-nosnippet>651</a>            || (i &gt;= <span class="self">self</span>.header.num_required_signatures <span class="kw">as </span>usize
<a href=#652 id=652 data-nosnippet>652</a>                &amp;&amp; i &lt; <span class="self">self</span>.account_keys.len()
<a href=#653 id=653 data-nosnippet>653</a>                    - <span class="self">self</span>.header.num_readonly_unsigned_accounts <span class="kw">as </span>usize)
<a href=#654 id=654 data-nosnippet>654</a>    }
<a href=#655 id=655 data-nosnippet>655</a>
<a href=#656 id=656 data-nosnippet>656</a>    <span class="doccomment">/// Returns true if the account at the specified index is writable by the
<a href=#657 id=657 data-nosnippet>657</a>    /// instructions in this message. Since the dynamic set of reserved accounts
<a href=#658 id=658 data-nosnippet>658</a>    /// isn't used here to demote write locks, this shouldn't be used in the
<a href=#659 id=659 data-nosnippet>659</a>    /// runtime.
<a href=#660 id=660 data-nosnippet>660</a>    </span><span class="attr">#[deprecated(since = <span class="string">"2.0.0"</span>, note = <span class="string">"Please use `is_maybe_writable` instead"</span>)]
<a href=#661 id=661 data-nosnippet>661</a>    #[allow(deprecated)]
<a href=#662 id=662 data-nosnippet>662</a>    </span><span class="kw">pub fn </span>is_writable(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#663 id=663 data-nosnippet>663</a>        (<span class="self">self</span>.is_writable_index(i))
<a href=#664 id=664 data-nosnippet>664</a>            &amp;&amp; !is_builtin_key_or_sysvar(<span class="kw-2">&amp;</span><span class="self">self</span>.account_keys[i])
<a href=#665 id=665 data-nosnippet>665</a>            &amp;&amp; !<span class="self">self</span>.demote_program_id(i)
<a href=#666 id=666 data-nosnippet>666</a>    }
<a href=#667 id=667 data-nosnippet>667</a>
<a href=#668 id=668 data-nosnippet>668</a>    <span class="doccomment">/// Returns true if the account at the specified index is writable by the
<a href=#669 id=669 data-nosnippet>669</a>    /// instructions in this message. The `reserved_account_keys` param has been
<a href=#670 id=670 data-nosnippet>670</a>    /// optional to allow clients to approximate writability without requiring
<a href=#671 id=671 data-nosnippet>671</a>    /// fetching the latest set of reserved account keys. If this method is
<a href=#672 id=672 data-nosnippet>672</a>    /// called by the runtime, the latest set of reserved account keys must be
<a href=#673 id=673 data-nosnippet>673</a>    /// passed.
<a href=#674 id=674 data-nosnippet>674</a>    </span><span class="kw">pub fn </span>is_maybe_writable(
<a href=#675 id=675 data-nosnippet>675</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#676 id=676 data-nosnippet>676</a>        i: usize,
<a href=#677 id=677 data-nosnippet>677</a>        reserved_account_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;&gt;,
<a href=#678 id=678 data-nosnippet>678</a>    ) -&gt; bool {
<a href=#679 id=679 data-nosnippet>679</a>        (<span class="self">self</span>.is_writable_index(i))
<a href=#680 id=680 data-nosnippet>680</a>            &amp;&amp; !<span class="self">self</span>.is_account_maybe_reserved(i, reserved_account_keys)
<a href=#681 id=681 data-nosnippet>681</a>            &amp;&amp; !<span class="self">self</span>.demote_program_id(i)
<a href=#682 id=682 data-nosnippet>682</a>    }
<a href=#683 id=683 data-nosnippet>683</a>
<a href=#684 id=684 data-nosnippet>684</a>    <span class="doccomment">/// Returns true if the account at the specified index is in the optional
<a href=#685 id=685 data-nosnippet>685</a>    /// reserved account keys set.
<a href=#686 id=686 data-nosnippet>686</a>    </span><span class="kw">fn </span>is_account_maybe_reserved(
<a href=#687 id=687 data-nosnippet>687</a>        <span class="kw-2">&amp;</span><span class="self">self</span>,
<a href=#688 id=688 data-nosnippet>688</a>        key_index: usize,
<a href=#689 id=689 data-nosnippet>689</a>        reserved_account_keys: <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>HashSet&lt;Pubkey&gt;&gt;,
<a href=#690 id=690 data-nosnippet>690</a>    ) -&gt; bool {
<a href=#691 id=691 data-nosnippet>691</a>        <span class="kw">let </span><span class="kw-2">mut </span>is_maybe_reserved = <span class="bool-val">false</span>;
<a href=#692 id=692 data-nosnippet>692</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(reserved_account_keys) = reserved_account_keys {
<a href=#693 id=693 data-nosnippet>693</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(key) = <span class="self">self</span>.account_keys.get(key_index) {
<a href=#694 id=694 data-nosnippet>694</a>                is_maybe_reserved = reserved_account_keys.contains(key);
<a href=#695 id=695 data-nosnippet>695</a>            }
<a href=#696 id=696 data-nosnippet>696</a>        }
<a href=#697 id=697 data-nosnippet>697</a>        is_maybe_reserved
<a href=#698 id=698 data-nosnippet>698</a>    }
<a href=#699 id=699 data-nosnippet>699</a>
<a href=#700 id=700 data-nosnippet>700</a>    <span class="kw">pub fn </span>is_signer(<span class="kw-2">&amp;</span><span class="self">self</span>, i: usize) -&gt; bool {
<a href=#701 id=701 data-nosnippet>701</a>        i &lt; <span class="self">self</span>.header.num_required_signatures <span class="kw">as </span>usize
<a href=#702 id=702 data-nosnippet>702</a>    }
<a href=#703 id=703 data-nosnippet>703</a>
<a href=#704 id=704 data-nosnippet>704</a>    <span class="kw">pub fn </span>signer_keys(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;<span class="kw-2">&amp;</span>Pubkey&gt; {
<a href=#705 id=705 data-nosnippet>705</a>        <span class="comment">// Clamp in case we're working on un-`sanitize()`ed input
<a href=#706 id=706 data-nosnippet>706</a>        </span><span class="kw">let </span>last_key = <span class="self">self
<a href=#707 id=707 data-nosnippet>707</a>            </span>.account_keys
<a href=#708 id=708 data-nosnippet>708</a>            .len()
<a href=#709 id=709 data-nosnippet>709</a>            .min(<span class="self">self</span>.header.num_required_signatures <span class="kw">as </span>usize);
<a href=#710 id=710 data-nosnippet>710</a>        <span class="self">self</span>.account_keys[..last_key].iter().collect()
<a href=#711 id=711 data-nosnippet>711</a>    }
<a href=#712 id=712 data-nosnippet>712</a>
<a href=#713 id=713 data-nosnippet>713</a>    <span class="doccomment">/// Returns `true` if `account_keys` has any duplicate keys.
<a href=#714 id=714 data-nosnippet>714</a>    </span><span class="kw">pub fn </span>has_duplicates(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#715 id=715 data-nosnippet>715</a>        <span class="comment">// Note: This is an O(n^2) algorithm, but requires no heap allocations. The benchmark
<a href=#716 id=716 data-nosnippet>716</a>        // `bench_has_duplicates` in benches/message_processor.rs shows that this implementation is
<a href=#717 id=717 data-nosnippet>717</a>        // ~50 times faster than using HashSet for very short slices.
<a href=#718 id=718 data-nosnippet>718</a>        </span><span class="kw">for </span>i <span class="kw">in </span><span class="number">1</span>..<span class="self">self</span>.account_keys.len() {
<a href=#719 id=719 data-nosnippet>719</a>            <span class="attr">#[allow(clippy::arithmetic_side_effects)]
<a href=#720 id=720 data-nosnippet>720</a>            </span><span class="kw">if </span><span class="self">self</span>.account_keys[i..].contains(<span class="kw-2">&amp;</span><span class="self">self</span>.account_keys[i - <span class="number">1</span>]) {
<a href=#721 id=721 data-nosnippet>721</a>                <span class="kw">return </span><span class="bool-val">true</span>;
<a href=#722 id=722 data-nosnippet>722</a>            }
<a href=#723 id=723 data-nosnippet>723</a>        }
<a href=#724 id=724 data-nosnippet>724</a>        <span class="bool-val">false
<a href=#725 id=725 data-nosnippet>725</a>    </span>}
<a href=#726 id=726 data-nosnippet>726</a>
<a href=#727 id=727 data-nosnippet>727</a>    <span class="doccomment">/// Returns `true` if any account is the BPF upgradeable loader.
<a href=#728 id=728 data-nosnippet>728</a>    </span><span class="kw">pub fn </span>is_upgradeable_loader_present(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#729 id=729 data-nosnippet>729</a>        <span class="self">self</span>.account_keys
<a href=#730 id=730 data-nosnippet>730</a>            .iter()
<a href=#731 id=731 data-nosnippet>731</a>            .any(|<span class="kw-2">&amp;</span>key| key == bpf_loader_upgradeable::id())
<a href=#732 id=732 data-nosnippet>732</a>    }
<a href=#733 id=733 data-nosnippet>733</a>}
<a href=#734 id=734 data-nosnippet>734</a>
<a href=#735 id=735 data-nosnippet>735</a><span class="attr">#[cfg(test)]
<a href=#736 id=736 data-nosnippet>736</a></span><span class="kw">mod </span>tests {
<a href=#737 id=737 data-nosnippet>737</a>    <span class="attr">#![allow(deprecated)]
<a href=#738 id=738 data-nosnippet>738</a>    </span><span class="kw">use </span>{
<a href=#739 id=739 data-nosnippet>739</a>        <span class="kw">super</span>::<span class="kw-2">*</span>, <span class="kw">crate</span>::MESSAGE_HEADER_LENGTH, solana_instruction::AccountMeta,
<a href=#740 id=740 data-nosnippet>740</a>        solana_sha256_hasher::hash, std::collections::HashSet,
<a href=#741 id=741 data-nosnippet>741</a>    };
<a href=#742 id=742 data-nosnippet>742</a>
<a href=#743 id=743 data-nosnippet>743</a>    <span class="attr">#[test]
<a href=#744 id=744 data-nosnippet>744</a>    </span><span class="kw">fn </span>test_builtin_program_keys() {
<a href=#745 id=745 data-nosnippet>745</a>        <span class="kw">let </span>keys: HashSet&lt;Pubkey&gt; = BUILTIN_PROGRAMS_KEYS.iter().copied().collect();
<a href=#746 id=746 data-nosnippet>746</a>        <span class="macro">assert_eq!</span>(keys.len(), <span class="number">10</span>);
<a href=#747 id=747 data-nosnippet>747</a>        <span class="kw">for </span>k <span class="kw">in </span>keys {
<a href=#748 id=748 data-nosnippet>748</a>            <span class="kw">let </span>k = <span class="macro">format!</span>(<span class="string">"{k}"</span>);
<a href=#749 id=749 data-nosnippet>749</a>            <span class="macro">assert!</span>(k.ends_with(<span class="string">"11111111111111111111111"</span>));
<a href=#750 id=750 data-nosnippet>750</a>        }
<a href=#751 id=751 data-nosnippet>751</a>    }
<a href=#752 id=752 data-nosnippet>752</a>
<a href=#753 id=753 data-nosnippet>753</a>    <span class="attr">#[test]
<a href=#754 id=754 data-nosnippet>754</a>    </span><span class="kw">fn </span>test_builtin_program_keys_abi_freeze() {
<a href=#755 id=755 data-nosnippet>755</a>        <span class="comment">// Once the feature is flipped on, we can't further modify
<a href=#756 id=756 data-nosnippet>756</a>        // BUILTIN_PROGRAMS_KEYS without the risk of breaking consensus.
<a href=#757 id=757 data-nosnippet>757</a>        </span><span class="kw">let </span>builtins = <span class="macro">format!</span>(<span class="string">"{:?}"</span>, <span class="kw-2">*</span>BUILTIN_PROGRAMS_KEYS);
<a href=#758 id=758 data-nosnippet>758</a>        <span class="macro">assert_eq!</span>(
<a href=#759 id=759 data-nosnippet>759</a>            <span class="macro">format!</span>(<span class="string">"{}"</span>, hash(builtins.as_bytes())),
<a href=#760 id=760 data-nosnippet>760</a>            <span class="string">"ACqmMkYbo9eqK6QrRSrB3HLyR6uHhLf31SCfGUAJjiWj"
<a href=#761 id=761 data-nosnippet>761</a>        </span>);
<a href=#762 id=762 data-nosnippet>762</a>    }
<a href=#763 id=763 data-nosnippet>763</a>
<a href=#764 id=764 data-nosnippet>764</a>    <span class="attr">#[test]
<a href=#765 id=765 data-nosnippet>765</a>    </span><span class="comment">// Ensure there's a way to calculate the number of required signatures.
<a href=#766 id=766 data-nosnippet>766</a>    </span><span class="kw">fn </span>test_message_signed_keys_len() {
<a href=#767 id=767 data-nosnippet>767</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#768 id=768 data-nosnippet>768</a>        <span class="kw">let </span>id0 = Pubkey::default();
<a href=#769 id=769 data-nosnippet>769</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>)]);
<a href=#770 id=770 data-nosnippet>770</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">None</span>);
<a href=#771 id=771 data-nosnippet>771</a>        <span class="macro">assert_eq!</span>(message.header.num_required_signatures, <span class="number">0</span>);
<a href=#772 id=772 data-nosnippet>772</a>
<a href=#773 id=773 data-nosnippet>773</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">true</span>)]);
<a href=#774 id=774 data-nosnippet>774</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id0));
<a href=#775 id=775 data-nosnippet>775</a>        <span class="macro">assert_eq!</span>(message.header.num_required_signatures, <span class="number">1</span>);
<a href=#776 id=776 data-nosnippet>776</a>    }
<a href=#777 id=777 data-nosnippet>777</a>
<a href=#778 id=778 data-nosnippet>778</a>    <span class="attr">#[test]
<a href=#779 id=779 data-nosnippet>779</a>    </span><span class="kw">fn </span>test_message_kitchen_sink() {
<a href=#780 id=780 data-nosnippet>780</a>        <span class="kw">let </span>program_id0 = Pubkey::new_unique();
<a href=#781 id=781 data-nosnippet>781</a>        <span class="kw">let </span>program_id1 = Pubkey::new_unique();
<a href=#782 id=782 data-nosnippet>782</a>        <span class="kw">let </span>id0 = Pubkey::default();
<a href=#783 id=783 data-nosnippet>783</a>        <span class="kw">let </span>id1 = Pubkey::new_unique();
<a href=#784 id=784 data-nosnippet>784</a>        <span class="kw">let </span>message = Message::new(
<a href=#785 id=785 data-nosnippet>785</a>            <span class="kw-2">&amp;</span>[
<a href=#786 id=786 data-nosnippet>786</a>                Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>)]),
<a href=#787 id=787 data-nosnippet>787</a>                Instruction::new_with_bincode(program_id1, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id1, <span class="bool-val">true</span>)]),
<a href=#788 id=788 data-nosnippet>788</a>                Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id1, <span class="bool-val">false</span>)]),
<a href=#789 id=789 data-nosnippet>789</a>            ],
<a href=#790 id=790 data-nosnippet>790</a>            <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id1),
<a href=#791 id=791 data-nosnippet>791</a>        );
<a href=#792 id=792 data-nosnippet>792</a>        <span class="macro">assert_eq!</span>(
<a href=#793 id=793 data-nosnippet>793</a>            message.instructions[<span class="number">0</span>],
<a href=#794 id=794 data-nosnippet>794</a>            CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[<span class="number">1</span>])
<a href=#795 id=795 data-nosnippet>795</a>        );
<a href=#796 id=796 data-nosnippet>796</a>        <span class="macro">assert_eq!</span>(
<a href=#797 id=797 data-nosnippet>797</a>            message.instructions[<span class="number">1</span>],
<a href=#798 id=798 data-nosnippet>798</a>            CompiledInstruction::new(<span class="number">3</span>, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[<span class="number">0</span>])
<a href=#799 id=799 data-nosnippet>799</a>        );
<a href=#800 id=800 data-nosnippet>800</a>        <span class="macro">assert_eq!</span>(
<a href=#801 id=801 data-nosnippet>801</a>            message.instructions[<span class="number">2</span>],
<a href=#802 id=802 data-nosnippet>802</a>            CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[<span class="number">0</span>])
<a href=#803 id=803 data-nosnippet>803</a>        );
<a href=#804 id=804 data-nosnippet>804</a>    }
<a href=#805 id=805 data-nosnippet>805</a>
<a href=#806 id=806 data-nosnippet>806</a>    <span class="attr">#[test]
<a href=#807 id=807 data-nosnippet>807</a>    </span><span class="kw">fn </span>test_message_payer_first() {
<a href=#808 id=808 data-nosnippet>808</a>        <span class="kw">let </span>program_id = Pubkey::default();
<a href=#809 id=809 data-nosnippet>809</a>        <span class="kw">let </span>payer = Pubkey::new_unique();
<a href=#810 id=810 data-nosnippet>810</a>        <span class="kw">let </span>id0 = Pubkey::default();
<a href=#811 id=811 data-nosnippet>811</a>
<a href=#812 id=812 data-nosnippet>812</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>)]);
<a href=#813 id=813 data-nosnippet>813</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer));
<a href=#814 id=814 data-nosnippet>814</a>        <span class="macro">assert_eq!</span>(message.header.num_required_signatures, <span class="number">1</span>);
<a href=#815 id=815 data-nosnippet>815</a>
<a href=#816 id=816 data-nosnippet>816</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(program_id, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">true</span>)]);
<a href=#817 id=817 data-nosnippet>817</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer));
<a href=#818 id=818 data-nosnippet>818</a>        <span class="macro">assert_eq!</span>(message.header.num_required_signatures, <span class="number">2</span>);
<a href=#819 id=819 data-nosnippet>819</a>
<a href=#820 id=820 data-nosnippet>820</a>        <span class="kw">let </span>ix = Instruction::new_with_bincode(
<a href=#821 id=821 data-nosnippet>821</a>            program_id,
<a href=#822 id=822 data-nosnippet>822</a>            <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#823 id=823 data-nosnippet>823</a>            <span class="macro">vec!</span>[AccountMeta::new(payer, <span class="bool-val">true</span>), AccountMeta::new(id0, <span class="bool-val">true</span>)],
<a href=#824 id=824 data-nosnippet>824</a>        );
<a href=#825 id=825 data-nosnippet>825</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>[ix], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer));
<a href=#826 id=826 data-nosnippet>826</a>        <span class="macro">assert_eq!</span>(message.header.num_required_signatures, <span class="number">2</span>);
<a href=#827 id=827 data-nosnippet>827</a>    }
<a href=#828 id=828 data-nosnippet>828</a>
<a href=#829 id=829 data-nosnippet>829</a>    <span class="attr">#[test]
<a href=#830 id=830 data-nosnippet>830</a>    </span><span class="kw">fn </span>test_program_position() {
<a href=#831 id=831 data-nosnippet>831</a>        <span class="kw">let </span>program_id0 = Pubkey::default();
<a href=#832 id=832 data-nosnippet>832</a>        <span class="kw">let </span>program_id1 = Pubkey::new_unique();
<a href=#833 id=833 data-nosnippet>833</a>        <span class="kw">let </span>id = Pubkey::new_unique();
<a href=#834 id=834 data-nosnippet>834</a>        <span class="kw">let </span>message = Message::new(
<a href=#835 id=835 data-nosnippet>835</a>            <span class="kw-2">&amp;</span>[
<a href=#836 id=836 data-nosnippet>836</a>                Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id, <span class="bool-val">false</span>)]),
<a href=#837 id=837 data-nosnippet>837</a>                Instruction::new_with_bincode(program_id1, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id, <span class="bool-val">true</span>)]),
<a href=#838 id=838 data-nosnippet>838</a>            ],
<a href=#839 id=839 data-nosnippet>839</a>            <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id),
<a href=#840 id=840 data-nosnippet>840</a>        );
<a href=#841 id=841 data-nosnippet>841</a>        <span class="macro">assert_eq!</span>(message.program_position(<span class="number">0</span>), <span class="prelude-val">None</span>);
<a href=#842 id=842 data-nosnippet>842</a>        <span class="macro">assert_eq!</span>(message.program_position(<span class="number">1</span>), <span class="prelude-val">Some</span>(<span class="number">0</span>));
<a href=#843 id=843 data-nosnippet>843</a>        <span class="macro">assert_eq!</span>(message.program_position(<span class="number">2</span>), <span class="prelude-val">Some</span>(<span class="number">1</span>));
<a href=#844 id=844 data-nosnippet>844</a>    }
<a href=#845 id=845 data-nosnippet>845</a>
<a href=#846 id=846 data-nosnippet>846</a>    <span class="attr">#[test]
<a href=#847 id=847 data-nosnippet>847</a>    </span><span class="kw">fn </span>test_is_writable() {
<a href=#848 id=848 data-nosnippet>848</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#849 id=849 data-nosnippet>849</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#850 id=850 data-nosnippet>850</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#851 id=851 data-nosnippet>851</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#852 id=852 data-nosnippet>852</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#853 id=853 data-nosnippet>853</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#854 id=854 data-nosnippet>854</a>
<a href=#855 id=855 data-nosnippet>855</a>        <span class="kw">let </span>message = Message {
<a href=#856 id=856 data-nosnippet>856</a>            header: MessageHeader {
<a href=#857 id=857 data-nosnippet>857</a>                num_required_signatures: <span class="number">3</span>,
<a href=#858 id=858 data-nosnippet>858</a>                num_readonly_signed_accounts: <span class="number">2</span>,
<a href=#859 id=859 data-nosnippet>859</a>                num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#860 id=860 data-nosnippet>860</a>            },
<a href=#861 id=861 data-nosnippet>861</a>            account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3, key4, key5],
<a href=#862 id=862 data-nosnippet>862</a>            recent_blockhash: Hash::default(),
<a href=#863 id=863 data-nosnippet>863</a>            instructions: <span class="macro">vec!</span>[],
<a href=#864 id=864 data-nosnippet>864</a>        };
<a href=#865 id=865 data-nosnippet>865</a>        <span class="macro">assert!</span>(message.is_writable(<span class="number">0</span>));
<a href=#866 id=866 data-nosnippet>866</a>        <span class="macro">assert!</span>(!message.is_writable(<span class="number">1</span>));
<a href=#867 id=867 data-nosnippet>867</a>        <span class="macro">assert!</span>(!message.is_writable(<span class="number">2</span>));
<a href=#868 id=868 data-nosnippet>868</a>        <span class="macro">assert!</span>(message.is_writable(<span class="number">3</span>));
<a href=#869 id=869 data-nosnippet>869</a>        <span class="macro">assert!</span>(message.is_writable(<span class="number">4</span>));
<a href=#870 id=870 data-nosnippet>870</a>        <span class="macro">assert!</span>(!message.is_writable(<span class="number">5</span>));
<a href=#871 id=871 data-nosnippet>871</a>    }
<a href=#872 id=872 data-nosnippet>872</a>
<a href=#873 id=873 data-nosnippet>873</a>    <span class="attr">#[test]
<a href=#874 id=874 data-nosnippet>874</a>    </span><span class="kw">fn </span>test_is_maybe_writable() {
<a href=#875 id=875 data-nosnippet>875</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#876 id=876 data-nosnippet>876</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#877 id=877 data-nosnippet>877</a>        <span class="kw">let </span>key2 = Pubkey::new_unique();
<a href=#878 id=878 data-nosnippet>878</a>        <span class="kw">let </span>key3 = Pubkey::new_unique();
<a href=#879 id=879 data-nosnippet>879</a>        <span class="kw">let </span>key4 = Pubkey::new_unique();
<a href=#880 id=880 data-nosnippet>880</a>        <span class="kw">let </span>key5 = Pubkey::new_unique();
<a href=#881 id=881 data-nosnippet>881</a>
<a href=#882 id=882 data-nosnippet>882</a>        <span class="kw">let </span>message = Message {
<a href=#883 id=883 data-nosnippet>883</a>            header: MessageHeader {
<a href=#884 id=884 data-nosnippet>884</a>                num_required_signatures: <span class="number">3</span>,
<a href=#885 id=885 data-nosnippet>885</a>                num_readonly_signed_accounts: <span class="number">2</span>,
<a href=#886 id=886 data-nosnippet>886</a>                num_readonly_unsigned_accounts: <span class="number">1</span>,
<a href=#887 id=887 data-nosnippet>887</a>            },
<a href=#888 id=888 data-nosnippet>888</a>            account_keys: <span class="macro">vec!</span>[key0, key1, key2, key3, key4, key5],
<a href=#889 id=889 data-nosnippet>889</a>            recent_blockhash: Hash::default(),
<a href=#890 id=890 data-nosnippet>890</a>            instructions: <span class="macro">vec!</span>[],
<a href=#891 id=891 data-nosnippet>891</a>        };
<a href=#892 id=892 data-nosnippet>892</a>
<a href=#893 id=893 data-nosnippet>893</a>        <span class="kw">let </span>reserved_account_keys = HashSet::from([key3]);
<a href=#894 id=894 data-nosnippet>894</a>
<a href=#895 id=895 data-nosnippet>895</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">0</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#896 id=896 data-nosnippet>896</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">1</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#897 id=897 data-nosnippet>897</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">2</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#898 id=898 data-nosnippet>898</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">3</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#899 id=899 data-nosnippet>899</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">3</span>, <span class="prelude-val">None</span>));
<a href=#900 id=900 data-nosnippet>900</a>        <span class="macro">assert!</span>(message.is_maybe_writable(<span class="number">4</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#901 id=901 data-nosnippet>901</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">5</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#902 id=902 data-nosnippet>902</a>        <span class="macro">assert!</span>(!message.is_maybe_writable(<span class="number">6</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#903 id=903 data-nosnippet>903</a>    }
<a href=#904 id=904 data-nosnippet>904</a>
<a href=#905 id=905 data-nosnippet>905</a>    <span class="attr">#[test]
<a href=#906 id=906 data-nosnippet>906</a>    </span><span class="kw">fn </span>test_is_account_maybe_reserved() {
<a href=#907 id=907 data-nosnippet>907</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#908 id=908 data-nosnippet>908</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#909 id=909 data-nosnippet>909</a>
<a href=#910 id=910 data-nosnippet>910</a>        <span class="kw">let </span>message = Message {
<a href=#911 id=911 data-nosnippet>911</a>            account_keys: <span class="macro">vec!</span>[key0, key1],
<a href=#912 id=912 data-nosnippet>912</a>            ..Message::default()
<a href=#913 id=913 data-nosnippet>913</a>        };
<a href=#914 id=914 data-nosnippet>914</a>
<a href=#915 id=915 data-nosnippet>915</a>        <span class="kw">let </span>reserved_account_keys = HashSet::from([key1]);
<a href=#916 id=916 data-nosnippet>916</a>
<a href=#917 id=917 data-nosnippet>917</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">0</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#918 id=918 data-nosnippet>918</a>        <span class="macro">assert!</span>(message.is_account_maybe_reserved(<span class="number">1</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#919 id=919 data-nosnippet>919</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">2</span>, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>reserved_account_keys)));
<a href=#920 id=920 data-nosnippet>920</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">0</span>, <span class="prelude-val">None</span>));
<a href=#921 id=921 data-nosnippet>921</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">1</span>, <span class="prelude-val">None</span>));
<a href=#922 id=922 data-nosnippet>922</a>        <span class="macro">assert!</span>(!message.is_account_maybe_reserved(<span class="number">2</span>, <span class="prelude-val">None</span>));
<a href=#923 id=923 data-nosnippet>923</a>    }
<a href=#924 id=924 data-nosnippet>924</a>
<a href=#925 id=925 data-nosnippet>925</a>    <span class="attr">#[test]
<a href=#926 id=926 data-nosnippet>926</a>    </span><span class="kw">fn </span>test_program_ids() {
<a href=#927 id=927 data-nosnippet>927</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#928 id=928 data-nosnippet>928</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#929 id=929 data-nosnippet>929</a>        <span class="kw">let </span>loader2 = Pubkey::new_unique();
<a href=#930 id=930 data-nosnippet>930</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>])];
<a href=#931 id=931 data-nosnippet>931</a>        <span class="kw">let </span>message = Message::new_with_compiled_instructions(
<a href=#932 id=932 data-nosnippet>932</a>            <span class="number">1</span>,
<a href=#933 id=933 data-nosnippet>933</a>            <span class="number">0</span>,
<a href=#934 id=934 data-nosnippet>934</a>            <span class="number">2</span>,
<a href=#935 id=935 data-nosnippet>935</a>            <span class="macro">vec!</span>[key0, key1, loader2],
<a href=#936 id=936 data-nosnippet>936</a>            Hash::default(),
<a href=#937 id=937 data-nosnippet>937</a>            instructions,
<a href=#938 id=938 data-nosnippet>938</a>        );
<a href=#939 id=939 data-nosnippet>939</a>        <span class="macro">assert_eq!</span>(message.program_ids(), <span class="macro">vec!</span>[<span class="kw-2">&amp;</span>loader2]);
<a href=#940 id=940 data-nosnippet>940</a>    }
<a href=#941 id=941 data-nosnippet>941</a>
<a href=#942 id=942 data-nosnippet>942</a>    <span class="attr">#[test]
<a href=#943 id=943 data-nosnippet>943</a>    </span><span class="kw">fn </span>test_is_instruction_account() {
<a href=#944 id=944 data-nosnippet>944</a>        <span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#945 id=945 data-nosnippet>945</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#946 id=946 data-nosnippet>946</a>        <span class="kw">let </span>loader2 = Pubkey::new_unique();
<a href=#947 id=947 data-nosnippet>947</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>])];
<a href=#948 id=948 data-nosnippet>948</a>        <span class="kw">let </span>message = Message::new_with_compiled_instructions(
<a href=#949 id=949 data-nosnippet>949</a>            <span class="number">1</span>,
<a href=#950 id=950 data-nosnippet>950</a>            <span class="number">0</span>,
<a href=#951 id=951 data-nosnippet>951</a>            <span class="number">2</span>,
<a href=#952 id=952 data-nosnippet>952</a>            <span class="macro">vec!</span>[key0, key1, loader2],
<a href=#953 id=953 data-nosnippet>953</a>            Hash::default(),
<a href=#954 id=954 data-nosnippet>954</a>            instructions,
<a href=#955 id=955 data-nosnippet>955</a>        );
<a href=#956 id=956 data-nosnippet>956</a>
<a href=#957 id=957 data-nosnippet>957</a>        <span class="macro">assert!</span>(message.is_instruction_account(<span class="number">0</span>));
<a href=#958 id=958 data-nosnippet>958</a>        <span class="macro">assert!</span>(message.is_instruction_account(<span class="number">1</span>));
<a href=#959 id=959 data-nosnippet>959</a>        <span class="macro">assert!</span>(!message.is_instruction_account(<span class="number">2</span>));
<a href=#960 id=960 data-nosnippet>960</a>    }
<a href=#961 id=961 data-nosnippet>961</a>
<a href=#962 id=962 data-nosnippet>962</a>    <span class="attr">#[test]
<a href=#963 id=963 data-nosnippet>963</a>    </span><span class="kw">fn </span>test_is_non_loader_key() {
<a href=#964 id=964 data-nosnippet>964</a>        <span class="attr">#![allow(deprecated)]
<a href=#965 id=965 data-nosnippet>965</a>        </span><span class="kw">let </span>key0 = Pubkey::new_unique();
<a href=#966 id=966 data-nosnippet>966</a>        <span class="kw">let </span>key1 = Pubkey::new_unique();
<a href=#967 id=967 data-nosnippet>967</a>        <span class="kw">let </span>loader2 = Pubkey::new_unique();
<a href=#968 id=968 data-nosnippet>968</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[CompiledInstruction::new(<span class="number">2</span>, <span class="kw-2">&amp;</span>(), <span class="macro">vec!</span>[<span class="number">0</span>, <span class="number">1</span>])];
<a href=#969 id=969 data-nosnippet>969</a>        <span class="kw">let </span>message = Message::new_with_compiled_instructions(
<a href=#970 id=970 data-nosnippet>970</a>            <span class="number">1</span>,
<a href=#971 id=971 data-nosnippet>971</a>            <span class="number">0</span>,
<a href=#972 id=972 data-nosnippet>972</a>            <span class="number">2</span>,
<a href=#973 id=973 data-nosnippet>973</a>            <span class="macro">vec!</span>[key0, key1, loader2],
<a href=#974 id=974 data-nosnippet>974</a>            Hash::default(),
<a href=#975 id=975 data-nosnippet>975</a>            instructions,
<a href=#976 id=976 data-nosnippet>976</a>        );
<a href=#977 id=977 data-nosnippet>977</a>        <span class="macro">assert!</span>(message.is_non_loader_key(<span class="number">0</span>));
<a href=#978 id=978 data-nosnippet>978</a>        <span class="macro">assert!</span>(message.is_non_loader_key(<span class="number">1</span>));
<a href=#979 id=979 data-nosnippet>979</a>        <span class="macro">assert!</span>(!message.is_non_loader_key(<span class="number">2</span>));
<a href=#980 id=980 data-nosnippet>980</a>    }
<a href=#981 id=981 data-nosnippet>981</a>
<a href=#982 id=982 data-nosnippet>982</a>    <span class="attr">#[test]
<a href=#983 id=983 data-nosnippet>983</a>    </span><span class="kw">fn </span>test_message_header_len_constant() {
<a href=#984 id=984 data-nosnippet>984</a>        <span class="macro">assert_eq!</span>(
<a href=#985 id=985 data-nosnippet>985</a>            bincode::serialized_size(<span class="kw-2">&amp;</span>MessageHeader::default()).unwrap() <span class="kw">as </span>usize,
<a href=#986 id=986 data-nosnippet>986</a>            MESSAGE_HEADER_LENGTH
<a href=#987 id=987 data-nosnippet>987</a>        );
<a href=#988 id=988 data-nosnippet>988</a>    }
<a href=#989 id=989 data-nosnippet>989</a>
<a href=#990 id=990 data-nosnippet>990</a>    <span class="attr">#[test]
<a href=#991 id=991 data-nosnippet>991</a>    </span><span class="kw">fn </span>test_message_hash() {
<a href=#992 id=992 data-nosnippet>992</a>        <span class="comment">// when this test fails, it's most likely due to a new serialized format of a message.
<a href=#993 id=993 data-nosnippet>993</a>        // in this case, the domain prefix `solana-tx-message-v1` should be updated.
<a href=#994 id=994 data-nosnippet>994</a>        </span><span class="kw">let </span>program_id0 = Pubkey::from_str(<span class="string">"4uQeVj5tqViQh7yWWGStvkEG1Zmhx6uasJtWCJziofM"</span>).unwrap();
<a href=#995 id=995 data-nosnippet>995</a>        <span class="kw">let </span>program_id1 = Pubkey::from_str(<span class="string">"8opHzTAnfzRpPEx21XtnrVTX28YQuCpAjcn1PczScKh"</span>).unwrap();
<a href=#996 id=996 data-nosnippet>996</a>        <span class="kw">let </span>id0 = Pubkey::from_str(<span class="string">"CiDwVBFgWV9E5MvXWoLgnEgn2hK7rJikbvfWavzAQz3"</span>).unwrap();
<a href=#997 id=997 data-nosnippet>997</a>        <span class="kw">let </span>id1 = Pubkey::from_str(<span class="string">"GcdayuLaLyrdmUu324nahyv33G5poQdLUEZ1nEytDeP"</span>).unwrap();
<a href=#998 id=998 data-nosnippet>998</a>        <span class="kw">let </span>id2 = Pubkey::from_str(<span class="string">"LX3EUdRUBUa3TbsYXLEUdj9J3prXkWXvLYSWyYyc2Jj"</span>).unwrap();
<a href=#999 id=999 data-nosnippet>999</a>        <span class="kw">let </span>id3 = Pubkey::from_str(<span class="string">"QRSsyMWN1yHT9ir42bgNZUNZ4PdEhcSWCrL2AryKpy5"</span>).unwrap();
<a href=#1000 id=1000 data-nosnippet>1000</a>        <span class="kw">let </span>instructions = <span class="macro">vec!</span>[
<a href=#1001 id=1001 data-nosnippet>1001</a>            Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id0, <span class="bool-val">false</span>)]),
<a href=#1002 id=1002 data-nosnippet>1002</a>            Instruction::new_with_bincode(program_id0, <span class="kw-2">&amp;</span><span class="number">0</span>, <span class="macro">vec!</span>[AccountMeta::new(id1, <span class="bool-val">true</span>)]),
<a href=#1003 id=1003 data-nosnippet>1003</a>            Instruction::new_with_bincode(
<a href=#1004 id=1004 data-nosnippet>1004</a>                program_id1,
<a href=#1005 id=1005 data-nosnippet>1005</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1006 id=1006 data-nosnippet>1006</a>                <span class="macro">vec!</span>[AccountMeta::new_readonly(id2, <span class="bool-val">false</span>)],
<a href=#1007 id=1007 data-nosnippet>1007</a>            ),
<a href=#1008 id=1008 data-nosnippet>1008</a>            Instruction::new_with_bincode(
<a href=#1009 id=1009 data-nosnippet>1009</a>                program_id1,
<a href=#1010 id=1010 data-nosnippet>1010</a>                <span class="kw-2">&amp;</span><span class="number">0</span>,
<a href=#1011 id=1011 data-nosnippet>1011</a>                <span class="macro">vec!</span>[AccountMeta::new_readonly(id3, <span class="bool-val">true</span>)],
<a href=#1012 id=1012 data-nosnippet>1012</a>            ),
<a href=#1013 id=1013 data-nosnippet>1013</a>        ];
<a href=#1014 id=1014 data-nosnippet>1014</a>
<a href=#1015 id=1015 data-nosnippet>1015</a>        <span class="kw">let </span>message = Message::new(<span class="kw-2">&amp;</span>instructions, <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>id1));
<a href=#1016 id=1016 data-nosnippet>1016</a>        <span class="macro">assert_eq!</span>(
<a href=#1017 id=1017 data-nosnippet>1017</a>            message.hash(),
<a href=#1018 id=1018 data-nosnippet>1018</a>            Hash::from_str(<span class="string">"7VWCF4quo2CcWQFNUayZiorxpiR5ix8YzLebrXKf3fMF"</span>).unwrap()
<a href=#1019 id=1019 data-nosnippet>1019</a>        )
<a href=#1020 id=1020 data-nosnippet>1020</a>    }
<a href=#1021 id=1021 data-nosnippet>1021</a>
<a href=#1022 id=1022 data-nosnippet>1022</a>    <span class="attr">#[test]
<a href=#1023 id=1023 data-nosnippet>1023</a>    </span><span class="kw">fn </span>test_inline_all_ids() {
<a href=#1024 id=1024 data-nosnippet>1024</a>        <span class="macro">assert_eq!</span>(solana_sysvar::ALL_IDS.to_vec(), ALL_IDS.to_vec());
<a href=#1025 id=1025 data-nosnippet>1025</a>    }
<a href=#1026 id=1026 data-nosnippet>1026</a>}</code></pre></div></section></main></body></html>