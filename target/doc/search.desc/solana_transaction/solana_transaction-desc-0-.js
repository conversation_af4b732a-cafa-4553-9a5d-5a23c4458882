searchState.loadedDescShard("solana_transaction", 0, "Atomically-committed sequences of instructions.\nAn atomically-committed sequence of instructions.\nGet the data for an instruction at the given index.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns a signature that is not valid for signing this …\nGet the positions of the pubkeys in <code>account_keys</code> …\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nGet the <code>Pubkey</code> of an account required by one of the …\nReturn the message containing all data that should be …\nThe message to sign.\nReturn the serialized message data to sign.\nCreate a fully-signed transaction from a <code>Message</code>.\nCreate a fully-signed transaction from a list of …\nCreate an unsigned transaction from a <code>Message</code>.\nCreate a fully-signed transaction from pre-compiled …\nCreate an unsigned transaction from a list of <code>Instruction</code>s.\nSign the transaction with a subset of required keys.\nSign the transaction with a subset of required keys.\nSign the transaction.\nA set of signatures of a serialized <code>Message</code>, signed by the …\nGet the <code>Pubkey</code> of a signing account required by one of the …\nSign the transaction with a subset of required keys, …\nSign the transaction with a subset of required keys, …\nSign the transaction, returning any errors.\nReturns true if transaction begins with an advance nonce …\nDefines a transaction which supports multiple versions of …\nMaximum number of accounts that a transaction may lock. …\nType that represents whether the transaction message has …\nSanitized transaction and the hash of its message\nSet of accounts that must be locked for safe transaction …\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nValidate and return the account keys locked by this …\nReturn the list of accounts that must be locked during …\nIf the transaction uses a durable nonce, return the pubkey …\nReturn the list of addresses loaded from on-chain address …\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nReturns true if this transaction is a simple vote\nReturn the signed message\nReturn the hash of the signed message\nList of readonly account key locks\nReturn the first signature for this transaction.\nReturn the list of signatures for this transaction\nConvert this sanitized transaction into a versioned …\nCreate a sanitized transaction from a sanitized versioned …\nCreate a sanitized transaction from fields. Performs only …\nValidate a transaction message against locked accounts\nList of writable account key locks\nSimple vote transaction meets these conditions:\nSimple vote transaction meets these conditions:\nType that serializes to the string “legacy”\nAn atomic transaction\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nReturns a legacy transaction if the transaction message is …\nMessage to sign.\nList of signatures\nSigns a versioned message and if successful, returns a …\nReturns true if transaction begins with an advance nonce …\nReturns the version of the transaction\nWraps a sanitized <code>VersionedTransaction</code> to provide a safe …\nConsumes the SanitizedVersionedTransaction, returning the …\nReturns the argument unchanged.\nCalls <code>U::from(self)</code>.")