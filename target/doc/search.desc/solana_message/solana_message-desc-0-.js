searchState.loadedDescShard("solana_message", 0, "Sequences of <code>Instruction</code>s executed within a single …\nCollection of static and dynamically loaded keys used to …\nThe definition of address lookup table accounts.\nAddress loading from lookup tables is disabled\nAttempted to lookup addresses from an invalid account\nAttempted to lookup addresses from an account owned by the …\nAddress lookup contains an invalid index\nSanitized legacy message\nAttempted to lookup addresses from a table that does not …\nThe length of a message header in bytes.\nBit mask that indicates whether a serialized message is …\nDescribes the organization of a <code>Message</code>’s account keys.\nSanitized message of a transaction.\nWraps a sanitized <code>VersionedMessage</code> to provide a safe API\nFailed to load slot hashes sysvar\nTransaction signature details including the number of …\nSanitized version #0 message with dynamically loaded …\nEither a legacy message or a v0 message.\nReturns the full list of account keys.\nReturns the list of account keys that are loaded for this …\nCompile instructions using the order of account keys to …\nDecompile message instructions without cloning account keys\nReturns the fee payer for the transaction\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the address of the account at the specified index …\nIf the message uses a durable nonce, return the pubkey of …\nGet a list of signers for the instruction at the given …\nreturn detailed signature counts\nReturn true if this message contains duplicate account keys\nCompute the blake3 hash of this transaction’s message\nCompute the blake3 hash of a raw transaction message\nMessage header which identifies the number of signer and …\nProgram instructions that will be executed in sequence and …\nProgram instructions that will be executed in sequence and …\nProgram instructions that will be executed in sequence and …\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nReturns true if this collection of account keys is empty\nReturns true if the account at the specified index is an …\nReturns true if the account at the specified index is …\nReturns true if the account at the specified index is an …\nReturns true if the account at the specified index is …\nReturns true if the account at the specified index is not …\nReturns true if the account at the specified index is not …\nReturns true if the account at the specified index signed …\nReturns true if the account at the specified index signed …\nInspect all message keys for the bpf upgradeable loader\nInspect all message keys for the bpf upgradeable loader\nReturns true if the account at the specified index is …\nList of boolean with same length as account_keys(), each …\nIterator for the addresses of the loaded accounts for a …\nThe original and current Solana message format.\nReturns a legacy message if this sanitized message wraps …\nReturns the total length of loaded accounts for a message\nLegacy message\nReturns the list of account keys used for account lookup …\nreturn the number of ed25519 instruction signatures\nReturn the number of readonly accounts loaded by this …\nThe last <code>num_readonly_signed_accounts</code> of the signed keys …\nThe last <code>num_readonly_unsigned_accounts</code> of the unsigned …\nThe number of signatures required for this message to be …\nreturn the number of secp256k1 instruction signatures\nreturn the number of secp256r1 instruction signatures\nReturns the total number of signatures in the message. …\nreturn the number of transaction signatures\nReturns the number of requested write-locks in this …\nProgram instructions iterator which includes each …\nProgram instructions iterator which includes each …\nThe hash of a recent block, used for timing out a …\nReturn the list of statically included account keys.\nreturn total number of signature, treating pre-processor …\nCompile instructions using the order of account keys to …\nCreate a sanitized legacy message\nCreate a sanitized message from a sanitized versioned …\nA future Solana message format.\nA compact encoding of an instruction.\nOrdered indices into the transaction keys array indicating …\nThe program input data.\nReturns the argument unchanged.\nCalls <code>U::from(self)</code>.\nIndex into the transaction keys array indicating the …\nAn ordered list of compiled instructions that were invoked …\nA list of compiled instructions that were invoked during …\nReturns the argument unchanged.\nCalls <code>U::from(self)</code>.\nInvocation stack height of this instruction. Instruction …\nA Solana transaction message (legacy).\nAll the account keys used by this transaction.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns <code>true</code> if <code>account_keys</code> has any duplicate keys.\nCompute the blake3 hash of this transaction’s message.\nCompute the blake3 hash of a raw transaction message.\nThe message header, identifying signed and read-only …\nPrograms that will be executed in sequence and committed …\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nReturns true if the account at the specified index is an …\nReturns true if the account at the specified index is …\nReturns <code>true</code> if any account is the BPF upgradeable loader.\nReturns true if the account at the specified index is …\nCreate a new <code>Message</code>.\nCreate a new message while setting the blockhash.\nCreate a new message for a nonced transaction.\nThe id of a recent ledger entry.\nCollection of addresses loaded from on-chain lookup …\nCombination of a version #0 message and its loaded …\nA Solana transaction message (v0).\nAddress table lookups describe an on-chain address lookup …\nAddress lookup table account key\nReturns the full list of static and dynamic account keys …\nList of accounts loaded by this transaction.\nList of address table lookups used to load additional …\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns the argument unchanged.\nReturns true if any account keys are duplicates\nThe message header, identifying signed and read-only …\nInstructions that invoke a designated program, are …\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nCalls <code>U::from(self)</code>.\nChecks if there are no writable or readonly addresses\nReturns true if the account at the specified index is …\nReturns true if the account at the specified index is …\nReturns true if the account at the specified index was …\nReturns true if any account is the bpf upgradeable loader\nList of boolean with same length as account_keys(), each …\nCombined length of loaded writable and readonly addresses\nAddresses loaded with on-chain address lookup tables\nMessage which loaded a collection of lookup table addresses\nList of addresses for read-only loaded accounts\nList of indexes used to load readonly account addresses\nThe blockhash of a recent block.\nSanitize message fields and compiled instruction indexes\nSerialize this message with a version #0 prefix using …\nReturns the list of static account keys that are loaded …\nCreate a signable transaction message from a <code>payer</code> public …\nList of addresses for writable loaded accounts\nList of indexes used to load writable account addresses")