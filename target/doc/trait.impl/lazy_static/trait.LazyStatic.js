(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://docs.rs/lazy_static/1.5.0/lazy_static/trait.LazyStatic.html\" title=\"trait lazy_static::LazyStatic\">LazyStatic</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.BUILTIN_PROGRAMS_KEYS.html\" title=\"struct solana_message::legacy::BUILTIN_PROGRAMS_KEYS\">BUILTIN_PROGRAMS_KEYS</a>"],["impl <a class=\"trait\" href=\"https://docs.rs/lazy_static/1.5.0/lazy_static/trait.LazyStatic.html\" title=\"trait lazy_static::LazyStatic\">LazyStatic</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.MAYBE_BUILTIN_KEY_OR_SYSVAR.html\" title=\"struct solana_message::legacy::MAYBE_BUILTIN_KEY_OR_SYSVAR\">MAYBE_BUILTIN_KEY_OR_SYSVAR</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[714]}