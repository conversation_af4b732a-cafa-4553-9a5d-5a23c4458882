(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/iter/traits/collect/trait.FromIterator.html\" title=\"trait core::iter::traits::collect::FromIterator\">FromIterator</a>&lt;<a class=\"struct\" href=\"solana_message/v0/struct.LoadedAddresses.html\" title=\"struct solana_message::v0::LoadedAddresses\">LoadedAddresses</a>&gt; for <a class=\"struct\" href=\"solana_message/v0/struct.LoadedAddresses.html\" title=\"struct solana_message::v0::LoadedAddresses\">LoadedAddresses</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[522]}