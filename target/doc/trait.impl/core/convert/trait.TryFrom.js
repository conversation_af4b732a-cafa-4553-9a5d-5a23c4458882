(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html\" title=\"trait core::convert::TryFrom\">TryFrom</a>&lt;<a class=\"enum\" href=\"solana_message/enum.VersionedMessage.html\" title=\"enum solana_message::VersionedMessage\">VersionedMessage</a>&gt; for <a class=\"struct\" href=\"solana_message/struct.SanitizedVersionedMessage.html\" title=\"struct solana_message::SanitizedVersionedMessage\">SanitizedVersionedMessage</a>"]]],["solana_transaction",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html\" title=\"trait core::convert::<PERSON><PERSON>rom\">TryFrom</a>&lt;<a class=\"struct\" href=\"solana_transaction/versioned/struct.VersionedTransaction.html\" title=\"struct solana_transaction::versioned::VersionedTransaction\">VersionedTransaction</a>&gt; for <a class=\"struct\" href=\"solana_transaction/versioned/sanitized/struct.SanitizedVersionedTransaction.html\" title=\"struct solana_transaction::versioned::sanitized::SanitizedVersionedTransaction\">SanitizedVersionedTransaction</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[494,608]}