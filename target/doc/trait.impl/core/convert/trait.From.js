(function() {
    var implementors = Object.fromEntries([["solana_transaction",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"solana_transaction/struct.Transaction.html\" title=\"struct solana_transaction::Transaction\">Transaction</a>&gt; for <a class=\"struct\" href=\"solana_transaction/versioned/struct.VersionedTransaction.html\" title=\"struct solana_transaction::versioned::VersionedTransaction\">VersionedTransaction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;Hash&gt; for <a class=\"enum\" href=\"solana_transaction/sanitized/enum.MessageHash.html\" title=\"enum solana_transaction::sanitized::MessageHash\">MessageHash</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[815]}