(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\">Deref</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.BUILTIN_PROGRAMS_KEYS.html\" title=\"struct solana_message::legacy::BUILTIN_PROGRAMS_KEYS\">BUILTIN_PROGRAMS_KEYS</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\">Deref</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.MAYBE_BUILTIN_KEY_OR_SYSVAR.html\" title=\"struct solana_message::legacy::MAYBE_BUILTIN_KEY_OR_SYSVAR\">MAYBE_BUILTIN_KEY_OR_SYSVAR</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[700]}