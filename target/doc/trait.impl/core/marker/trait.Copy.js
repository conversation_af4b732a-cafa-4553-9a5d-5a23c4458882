(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a> for <a class=\"struct\" href=\"solana_message/struct.MessageHeader.html\" title=\"struct solana_message::MessageHeader\">MessageHeader</a>"]]],["solana_transaction",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a> for <a class=\"enum\" href=\"solana_transaction/enum.TransactionVerificationMode.html\" title=\"enum solana_transaction::TransactionVerificationMode\">TransactionVerificationMode</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[302,351]}