(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_message/enum.CompileError.html\" title=\"enum solana_message::CompileError\">CompileError</a>",1,["solana_message::compiled_keys::CompileError"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_message/enum.SanitizedMessage.html\" title=\"enum solana_message::SanitizedMessage\">SanitizedMessage</a>",1,["solana_message::non_bpf_modules::sanitized::SanitizedMessage"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_message/enum.SimpleAddressLoader.html\" title=\"enum solana_message::SimpleAddressLoader\">SimpleAddressLoader</a>",1,["solana_message::non_bpf_modules::address_loader::SimpleAddressLoader"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_message/enum.VersionedMessage.html\" title=\"enum solana_message::VersionedMessage\">VersionedMessage</a>",1,["solana_message::non_bpf_modules::versions::VersionedMessage"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/compiled_instruction/struct.CompiledInstruction.html\" title=\"struct solana_message::compiled_instruction::CompiledInstruction\">CompiledInstruction</a>",1,["solana_message::compiled_instruction::CompiledInstruction"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/inner_instruction/struct.InnerInstruction.html\" title=\"struct solana_message::inner_instruction::InnerInstruction\">InnerInstruction</a>",1,["solana_message::inner_instruction::InnerInstruction"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.BUILTIN_PROGRAMS_KEYS.html\" title=\"struct solana_message::legacy::BUILTIN_PROGRAMS_KEYS\">BUILTIN_PROGRAMS_KEYS</a>",1,["solana_message::legacy::builtins::BUILTIN_PROGRAMS_KEYS"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.MAYBE_BUILTIN_KEY_OR_SYSVAR.html\" title=\"struct solana_message::legacy::MAYBE_BUILTIN_KEY_OR_SYSVAR\">MAYBE_BUILTIN_KEY_OR_SYSVAR</a>",1,["solana_message::legacy::builtins::MAYBE_BUILTIN_KEY_OR_SYSVAR"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.Message.html\" title=\"struct solana_message::legacy::Message\">Message</a>",1,["solana_message::legacy::Message"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/struct.AddressLookupTableAccount.html\" title=\"struct solana_message::AddressLookupTableAccount\">AddressLookupTableAccount</a>",1,["solana_message::AddressLookupTableAccount"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/struct.MessageHeader.html\" title=\"struct solana_message::MessageHeader\">MessageHeader</a>",1,["solana_message::MessageHeader"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/struct.SanitizedVersionedMessage.html\" title=\"struct solana_message::SanitizedVersionedMessage\">SanitizedVersionedMessage</a>",1,["solana_message::non_bpf_modules::versions::sanitized::SanitizedVersionedMessage"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/struct.TransactionSignatureDetails.html\" title=\"struct solana_message::TransactionSignatureDetails\">TransactionSignatureDetails</a>",1,["solana_message::non_bpf_modules::sanitized::TransactionSignatureDetails"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/v0/struct.LoadedAddresses.html\" title=\"struct solana_message::v0::LoadedAddresses\">LoadedAddresses</a>",1,["solana_message::non_bpf_modules::versions::v0::loaded::LoadedAddresses"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/v0/struct.Message.html\" title=\"struct solana_message::v0::Message\">Message</a>",1,["solana_message::non_bpf_modules::versions::v0::Message"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/v0/struct.MessageAddressTableLookup.html\" title=\"struct solana_message::v0::MessageAddressTableLookup\">MessageAddressTableLookup</a>",1,["solana_message::non_bpf_modules::versions::v0::MessageAddressTableLookup"]],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/struct.AccountKeys.html\" title=\"struct solana_message::AccountKeys\">AccountKeys</a>&lt;'a&gt;",1,["solana_message::non_bpf_modules::account_keys::AccountKeys"]],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/struct.LegacyMessage.html\" title=\"struct solana_message::LegacyMessage\">LegacyMessage</a>&lt;'a&gt;",1,["solana_message::non_bpf_modules::sanitized::LegacyMessage"]],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_message/v0/struct.LoadedMessage.html\" title=\"struct solana_message::v0::LoadedMessage\">LoadedMessage</a>&lt;'a&gt;",1,["solana_message::non_bpf_modules::versions::v0::loaded::LoadedMessage"]]]],["solana_transaction",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_transaction/enum.TransactionVerificationMode.html\" title=\"enum solana_transaction::TransactionVerificationMode\">TransactionVerificationMode</a>",1,["solana_transaction::TransactionVerificationMode"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_transaction/sanitized/enum.MessageHash.html\" title=\"enum solana_transaction::sanitized::MessageHash\">MessageHash</a>",1,["solana_transaction::sanitized::MessageHash"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_transaction/versioned/enum.Legacy.html\" title=\"enum solana_transaction::versioned::Legacy\">Legacy</a>",1,["solana_transaction::versioned::Legacy"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"enum\" href=\"solana_transaction/versioned/enum.TransactionVersion.html\" title=\"enum solana_transaction::versioned::TransactionVersion\">TransactionVersion</a>",1,["solana_transaction::versioned::TransactionVersion"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_transaction/sanitized/struct.SanitizedTransaction.html\" title=\"struct solana_transaction::sanitized::SanitizedTransaction\">SanitizedTransaction</a>",1,["solana_transaction::sanitized::SanitizedTransaction"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_transaction/struct.Transaction.html\" title=\"struct solana_transaction::Transaction\">Transaction</a>",1,["solana_transaction::Transaction"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_transaction/versioned/sanitized/struct.SanitizedVersionedTransaction.html\" title=\"struct solana_transaction::versioned::sanitized::SanitizedVersionedTransaction\">SanitizedVersionedTransaction</a>",1,["solana_transaction::versioned::sanitized::SanitizedVersionedTransaction"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_transaction/versioned/struct.VersionedTransaction.html\" title=\"struct solana_transaction::versioned::VersionedTransaction\">VersionedTransaction</a>",1,["solana_transaction::versioned::VersionedTransaction"]],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html\" title=\"trait core::panic::unwind_safe::RefUnwindSafe\">RefUnwindSafe</a> for <a class=\"struct\" href=\"solana_transaction/sanitized/struct.TransactionAccountLocks.html\" title=\"struct solana_transaction::sanitized::TransactionAccountLocks\">TransactionAccountLocks</a>&lt;'a&gt;",1,["solana_transaction::sanitized::TransactionAccountLocks"]]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[7974,3889]}