(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"solana_message/enum.CompileError.html\" title=\"enum solana_message::CompileError\">CompileError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"solana_message/enum.SanitizedMessage.html\" title=\"enum solana_message::SanitizedMessage\">SanitizedMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"solana_message/enum.VersionedMessage.html\" title=\"enum solana_message::VersionedMessage\">VersionedMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/compiled_instruction/struct.CompiledInstruction.html\" title=\"struct solana_message::compiled_instruction::CompiledInstruction\">CompiledInstruction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/inner_instruction/struct.InnerInstruction.html\" title=\"struct solana_message::inner_instruction::InnerInstruction\">InnerInstruction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.Message.html\" title=\"struct solana_message::legacy::Message\">Message</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/struct.AddressLookupTableAccount.html\" title=\"struct solana_message::AddressLookupTableAccount\">AddressLookupTableAccount</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/struct.MessageHeader.html\" title=\"struct solana_message::MessageHeader\">MessageHeader</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/struct.SanitizedVersionedMessage.html\" title=\"struct solana_message::SanitizedVersionedMessage\">SanitizedVersionedMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/struct.TransactionSignatureDetails.html\" title=\"struct solana_message::TransactionSignatureDetails\">TransactionSignatureDetails</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/v0/struct.LoadedAddresses.html\" title=\"struct solana_message::v0::LoadedAddresses\">LoadedAddresses</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/v0/struct.Message.html\" title=\"struct solana_message::v0::Message\">Message</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/v0/struct.MessageAddressTableLookup.html\" title=\"struct solana_message::v0::MessageAddressTableLookup\">MessageAddressTableLookup</a>"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/struct.AccountKeys.html\" title=\"struct solana_message::AccountKeys\">AccountKeys</a>&lt;'a&gt;"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/struct.LegacyMessage.html\" title=\"struct solana_message::LegacyMessage\">LegacyMessage</a>&lt;'a&gt;"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_message/v0/struct.LoadedMessage.html\" title=\"struct solana_message::v0::LoadedMessage\">LoadedMessage</a>&lt;'a&gt;"]]],["solana_transaction",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"solana_transaction/enum.TransactionVerificationMode.html\" title=\"enum solana_transaction::TransactionVerificationMode\">TransactionVerificationMode</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"solana_transaction/versioned/enum.Legacy.html\" title=\"enum solana_transaction::versioned::Legacy\">Legacy</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"solana_transaction/versioned/enum.TransactionVersion.html\" title=\"enum solana_transaction::versioned::TransactionVersion\">TransactionVersion</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_transaction/sanitized/struct.SanitizedTransaction.html\" title=\"struct solana_transaction::sanitized::SanitizedTransaction\">SanitizedTransaction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_transaction/struct.Transaction.html\" title=\"struct solana_transaction::Transaction\">Transaction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_transaction/versioned/sanitized/struct.SanitizedVersionedTransaction.html\" title=\"struct solana_transaction::versioned::sanitized::SanitizedVersionedTransaction\">SanitizedVersionedTransaction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_transaction/versioned/struct.VersionedTransaction.html\" title=\"struct solana_transaction::versioned::VersionedTransaction\">VersionedTransaction</a>"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"struct\" href=\"solana_transaction/sanitized/struct.TransactionAccountLocks.html\" title=\"struct solana_transaction::sanitized::TransactionAccountLocks\">TransactionAccountLocks</a>&lt;'a&gt;"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[4805,2620]}