(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"enum\" href=\"solana_message/enum.VersionedMessage.html\" title=\"enum solana_message::VersionedMessage\">VersionedMessage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/legacy/struct.Message.html\" title=\"struct solana_message::legacy::Message\">Message</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/struct.MessageHeader.html\" title=\"struct solana_message::MessageHeader\">MessageHeader</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/struct.TransactionSignatureDetails.html\" title=\"struct solana_message::TransactionSignatureDetails\">TransactionSignatureDetails</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/v0/struct.LoadedAddresses.html\" title=\"struct solana_message::v0::LoadedAddresses\">LoadedAddresses</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/v0/struct.Message.html\" title=\"struct solana_message::v0::Message\">Message</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/v0/struct.MessageAddressTableLookup.html\" title=\"struct solana_message::v0::MessageAddressTableLookup\">MessageAddressTableLookup</a>"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_message/struct.AccountKeys.html\" title=\"struct solana_message::AccountKeys\">AccountKeys</a>&lt;'a&gt;"]]],["solana_transaction",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_transaction/struct.Transaction.html\" title=\"struct solana_transaction::Transaction\">Transaction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_transaction/versioned/struct.VersionedTransaction.html\" title=\"struct solana_transaction::versioned::VersionedTransaction\">VersionedTransaction</a>"],["impl&lt;'a&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"solana_transaction/sanitized/struct.TransactionAccountLocks.html\" title=\"struct solana_transaction::sanitized::TransactionAccountLocks\">TransactionAccountLocks</a>&lt;'a&gt;"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[2465,1035]}