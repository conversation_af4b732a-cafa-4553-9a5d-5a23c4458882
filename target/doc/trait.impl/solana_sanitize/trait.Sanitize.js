(function() {
    var implementors = Object.fromEntries([["solana_message",[["impl Sanitize for <a class=\"struct\" href=\"solana_message/compiled_instruction/struct.CompiledInstruction.html\" title=\"struct solana_message::compiled_instruction::CompiledInstruction\">CompiledInstruction</a>"],["impl Sanitize for <a class=\"struct\" href=\"solana_message/legacy/struct.Message.html\" title=\"struct solana_message::legacy::Message\">Message</a>"]]],["solana_transaction",[["impl Sanitize for <a class=\"struct\" href=\"solana_transaction/struct.Transaction.html\" title=\"struct solana_transaction::Transaction\">Transaction</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[392,184]}