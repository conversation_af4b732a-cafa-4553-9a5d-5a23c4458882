<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="An atomically-committed sequence of instructions."><title>Transaction in solana_transaction - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc struct"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_transaction/index.html">solana_<wbr>transaction</a><span class="version">2.2.2</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Transaction</a></h2><h3><a href="#fields">Fields</a></h3><ul class="block structfield"><li><a href="#structfield.message" title="message">message</a></li><li><a href="#structfield.signatures" title="signatures">signatures</a></li></ul><h3><a href="#implementations">Methods</a></h3><ul class="block method"><li><a href="#method.data" title="data">data</a></li><li><a href="#method.get_invalid_signature" title="get_invalid_signature">get_invalid_signature</a></li><li><a href="#method.get_signing_keypair_positions" title="get_signing_keypair_positions">get_signing_keypair_positions</a></li><li><a href="#method.is_signed" title="is_signed">is_signed</a></li><li><a href="#method.key" title="key">key</a></li><li><a href="#method.message" title="message">message</a></li><li><a href="#method.message_data" title="message_data">message_data</a></li><li><a href="#method.new" title="new">new</a></li><li><a href="#method.new_signed_with_payer" title="new_signed_with_payer">new_signed_with_payer</a></li><li><a href="#method.new_unsigned" title="new_unsigned">new_unsigned</a></li><li><a href="#method.new_with_compiled_instructions" title="new_with_compiled_instructions">new_with_compiled_instructions</a></li><li><a href="#method.new_with_payer" title="new_with_payer">new_with_payer</a></li><li><a href="#method.partial_sign" title="partial_sign">partial_sign</a></li><li><a href="#method.partial_sign_unchecked" title="partial_sign_unchecked">partial_sign_unchecked</a></li><li><a href="#method.sign" title="sign">sign</a></li><li><a href="#method.signer_key" title="signer_key">signer_key</a></li><li><a href="#method.try_partial_sign" title="try_partial_sign">try_partial_sign</a></li><li><a href="#method.try_partial_sign_unchecked" title="try_partial_sign_unchecked">try_partial_sign_unchecked</a></li><li><a href="#method.try_sign" title="try_sign">try_sign</a></li></ul><h3><a href="#trait-implementations">Trait Implementations</a></h3><ul class="block trait-implementation"><li><a href="#impl-Clone-for-Transaction" title="Clone">Clone</a></li><li><a href="#impl-Debug-for-Transaction" title="Debug">Debug</a></li><li><a href="#impl-Default-for-Transaction" title="Default">Default</a></li><li><a href="#impl-Deserialize%3C'de%3E-for-Transaction" title="Deserialize&#60;&#39;de&#62;">Deserialize&#60;&#39;de&#62;</a></li><li><a href="#impl-Eq-for-Transaction" title="Eq">Eq</a></li><li><a href="#impl-From%3CTransaction%3E-for-VersionedTransaction" title="From&#60;Transaction&#62;">From&#60;Transaction&#62;</a></li><li><a href="#impl-PartialEq-for-Transaction" title="PartialEq">PartialEq</a></li><li><a href="#impl-Sanitize-for-Transaction" title="Sanitize">Sanitize</a></li><li><a href="#impl-Serialize-for-Transaction" title="Serialize">Serialize</a></li><li><a href="#impl-StructuralPartialEq-for-Transaction" title="StructuralPartialEq">StructuralPartialEq</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-Freeze-for-Transaction" title="Freeze">Freeze</a></li><li><a href="#impl-RefUnwindSafe-for-Transaction" title="RefUnwindSafe">RefUnwindSafe</a></li><li><a href="#impl-Send-for-Transaction" title="Send">Send</a></li><li><a href="#impl-Sync-for-Transaction" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-Transaction" title="Unpin">Unpin</a></li><li><a href="#impl-UnwindSafe-for-Transaction" title="UnwindSafe">UnwindSafe</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-CloneToUninit-for-T" title="CloneToUninit">CloneToUninit</a></li><li><a href="#impl-DeserializeOwned-for-T" title="DeserializeOwned">DeserializeOwned</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-Same-for-T" title="Same">Same</a></li><li><a href="#impl-ToOwned-for-T" title="ToOwned">ToOwned</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li><li><a href="#impl-VZip%3CV%3E-for-T" title="VZip&#60;V&#62;">VZip&#60;V&#62;</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="index.html">In crate solana_<wbr>transaction</a></h2></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="index.html">solana_transaction</a></div><h1>Struct <span class="struct">Transaction</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/solana_transaction/lib.rs.html#193-208">Source</a> </span></div><pre class="rust item-decl"><code>pub struct Transaction {
    pub signatures: <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;Signature&gt;,
    pub message: <a class="struct" href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a>,
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>An atomically-committed sequence of instructions.</p>
<p>While [<code>Instruction</code>]s are the basic unit of computation in Solana,
they are submitted by clients in <a href="struct.Transaction.html" title="struct solana_transaction::Transaction"><code>Transaction</code></a>s containing one or
more instructions, and signed by one or more <a href="https://docs.rs/solana-signer/latest/solana_signer/trait.Signer.html"><code>Signer</code></a>s.</p>
<p>See the <a href="index.html" title="mod solana_transaction">module documentation</a> for more details about transactions.</p>
<p>Some constructors accept an optional <code>payer</code>, the account responsible for
paying the cost of executing a transaction. In most cases, callers should
specify the payer explicitly in these constructors. In some cases though,
the caller is not <em>required</em> to specify the payer, but is still allowed to:
in the <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a> structure, the first account is always the fee-payer, so
if the caller has knowledge that the first account of the constructed
transaction’s <code>Message</code> is both a signer and the expected fee-payer, then
redundantly specifying the fee-payer is not strictly required.</p>
</div></details><h2 id="fields" class="fields section-header">Fields<a href="#fields" class="anchor">§</a></h2><span id="structfield.signatures" class="structfield section-header"><a href="#structfield.signatures" class="anchor field">§</a><code>signatures: <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;Signature&gt;</code></span><div class="docblock"><p>A set of signatures of a serialized <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a>, signed by the first
keys of the <code>Message</code>’s <a href="https://docs.rs/solana-message/latest/solana_message/legacy/struct.Message.html#structfield.account_keys"><code>account_keys</code></a>, where the number of signatures
is equal to <a href="https://docs.rs/solana-message/latest/solana_message/struct.MessageHeader.html#structfield.num_required_signatures"><code>num_required_signatures</code></a> of the <code>Message</code>’s
<a href="https://docs.rs/solana-message/latest/solana_message/struct.MessageHeader.html"><code>MessageHeader</code></a>.</p>
</div><span id="structfield.message" class="structfield section-header"><a href="#structfield.message" class="anchor field">§</a><code>message: <a class="struct" href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a></code></span><div class="docblock"><p>The message to sign.</p>
</div><h2 id="implementations" class="section-header">Implementations<a href="#implementations" class="anchor">§</a></h2><div id="implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#243-1132">Source</a><a href="#impl-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.new_unsigned" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#309-314">Source</a><h4 class="code-header">pub fn <a href="#method.new_unsigned" class="fn">new_unsigned</a>(message: <a class="struct" href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a>) -&gt; Self</h4></section></summary><div class="docblock"><p>Create an unsigned transaction from a <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a>.</p>
<h5 id="examples"><a class="doc-anchor" href="#examples">§</a>Examples</h5>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span>message = Message::new(
        <span class="kw-2">&amp;</span>[instruction],
        <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()),
    );

    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_unsigned(message);
    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    tx.sign(<span class="kw-2">&amp;</span>[payer], blockhash);
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><details class="toggle method-toggle" open><summary><section id="method.new" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#387-395">Source</a><h4 class="code-header">pub fn <a href="#method.new" class="fn">new</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    from_keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    message: <a class="struct" href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a>,
    recent_blockhash: Hash,
) -&gt; <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h4></section></summary><div class="docblock"><p>Create a fully-signed transaction from a <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a>.</p>
<h5 id="panics"><a class="doc-anchor" href="#panics">§</a>Panics</h5>
<p>Panics when signing fails. See <a href="struct.Transaction.html#method.try_sign" title="method solana_transaction::Transaction::try_sign"><code>Transaction::try_sign</code></a> and
<a href="struct.Transaction.html#method.try_partial_sign" title="method solana_transaction::Transaction::try_partial_sign"><code>Transaction::try_partial_sign</code></a> for a full description of failure
scenarios.</p>
<h5 id="examples-1"><a class="doc-anchor" href="#examples-1">§</a>Examples</h5>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span>message = Message::new(
        <span class="kw-2">&amp;</span>[instruction],
        <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()),
    );

    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new(<span class="kw-2">&amp;</span>[payer], message, blockhash);
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><details class="toggle method-toggle" open><summary><section id="method.new_with_payer" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#461-464">Source</a><h4 class="code-header">pub fn <a href="#method.new_with_payer" class="fn">new_with_payer</a>(
    instructions: &amp;[Instruction],
    payer: <a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;&amp;Pubkey&gt;,
) -&gt; Self</h4></section></summary><div class="docblock"><p>Create an unsigned transaction from a list of [<code>Instruction</code>]s.</p>
<p><code>payer</code> is the account responsible for paying the cost of executing the
transaction. It is typically provided, but is optional in some cases.
See the <a href="struct.Transaction.html" title="struct solana_transaction::Transaction"><code>Transaction</code></a> docs for more.</p>
<h5 id="examples-2"><a class="doc-anchor" href="#examples-2">§</a>Examples</h5>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_with_payer(<span class="kw-2">&amp;</span>[instruction], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()));
    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    tx.sign(<span class="kw-2">&amp;</span>[payer], blockhash);
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><details class="toggle method-toggle" open><summary><section id="method.new_signed_with_payer" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#541-549">Source</a><h4 class="code-header">pub fn <a href="#method.new_signed_with_payer" class="fn">new_signed_with_payer</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    instructions: &amp;[Instruction],
    payer: <a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;&amp;Pubkey&gt;,
    signing_keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    recent_blockhash: Hash,
) -&gt; Self</h4></section></summary><div class="docblock"><p>Create a fully-signed transaction from a list of [<code>Instruction</code>]s.</p>
<p><code>payer</code> is the account responsible for paying the cost of executing the
transaction. It is typically provided, but is optional in some cases.
See the <a href="struct.Transaction.html" title="struct solana_transaction::Transaction"><code>Transaction</code></a> docs for more.</p>
<h5 id="panics-1"><a class="doc-anchor" href="#panics-1">§</a>Panics</h5>
<p>Panics when signing fails. See <a href="struct.Transaction.html#method.try_sign" title="method solana_transaction::Transaction::try_sign"><code>Transaction::try_sign</code></a> and
<a href="struct.Transaction.html#method.try_partial_sign" title="method solana_transaction::Transaction::try_partial_sign"><code>Transaction::try_partial_sign</code></a> for a full description of failure
scenarios.</p>
<h5 id="examples-3"><a class="doc-anchor" href="#examples-3">§</a>Examples</h5>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_signed_with_payer(
        <span class="kw-2">&amp;</span>[instruction],
        <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()),
        <span class="kw-2">&amp;</span>[payer],
        blockhash,
    );
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><details class="toggle method-toggle" open><summary><section id="method.new_with_compiled_instructions" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#567-587">Source</a><h4 class="code-header">pub fn <a href="#method.new_with_compiled_instructions" class="fn">new_with_compiled_instructions</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    from_keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    keys: &amp;[Pubkey],
    recent_blockhash: Hash,
    program_ids: <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;Pubkey&gt;,
    instructions: <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="../solana_message/compiled_instruction/struct.CompiledInstruction.html" title="struct solana_message::compiled_instruction::CompiledInstruction">CompiledInstruction</a>&gt;,
) -&gt; Self</h4></section></summary><div class="docblock"><p>Create a fully-signed transaction from pre-compiled instructions.</p>
<h5 id="arguments"><a class="doc-anchor" href="#arguments">§</a>Arguments</h5>
<ul>
<li><code>from_keypairs</code> - The keys used to sign the transaction.</li>
<li><code>keys</code> - The keys for the transaction.  These are the program state
instances or lamport recipient keys.</li>
<li><code>recent_blockhash</code> - The PoH hash.</li>
<li><code>program_ids</code> - The keys that identify programs used in the <code>instruction</code> vector.</li>
<li><code>instructions</code> - Instructions that will be executed atomically.</li>
</ul>
<h5 id="panics-2"><a class="doc-anchor" href="#panics-2">§</a>Panics</h5>
<p>Panics when signing fails. See <a href="struct.Transaction.html#method.try_sign" title="method solana_transaction::Transaction::try_sign"><code>Transaction::try_sign</code></a> and for a full
description of failure conditions.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.data" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#600-602">Source</a><h4 class="code-header">pub fn <a href="#method.data" class="fn">data</a>(&amp;self, instruction_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; &amp;[<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u8.html">u8</a>] <a href="#" class="tooltip" data-notable-ty="&amp;[u8]">ⓘ</a></h4></section></summary><div class="docblock"><p>Get the data for an instruction at the given index.</p>
<p>The <code>instruction_index</code> corresponds to the <a href="../solana_message/legacy/struct.Message.html#structfield.instructions" title="field solana_message::legacy::Message::instructions"><code>instructions</code></a> vector of
the <code>Transaction</code>’s <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a> value.</p>
<h5 id="panics-3"><a class="doc-anchor" href="#panics-3">§</a>Panics</h5>
<p>Panics if <code>instruction_index</code> is greater than or equal to the number of
instructions in the transaction.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.key" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#626-629">Source</a><h4 class="code-header">pub fn <a href="#method.key" class="fn">key</a>(
    &amp;self,
    instruction_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>,
    accounts_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;&amp;Pubkey&gt;</h4></section></summary><div class="docblock"><p>Get the <code>Pubkey</code> of an account required by one of the instructions in
the transaction.</p>
<p>The <code>instruction_index</code> corresponds to the <a href="../solana_message/legacy/struct.Message.html#structfield.instructions" title="field solana_message::legacy::Message::instructions"><code>instructions</code></a> vector of
the <code>Transaction</code>’s <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a> value; and the <code>account_index</code> to the
<a href="../solana_message/compiled_instruction/struct.CompiledInstruction.html#structfield.accounts" title="field solana_message::compiled_instruction::CompiledInstruction::accounts"><code>accounts</code></a> vector of the message’s <a href="../solana_message/compiled_instruction/struct.CompiledInstruction.html" title="struct solana_message::compiled_instruction::CompiledInstruction"><code>CompiledInstruction</code></a>s.</p>
<p>Returns <code>None</code> if <code>instruction_index</code> is greater than or equal to the
number of instructions in the transaction; or if <code>accounts_index</code> is
greater than or equal to the number of accounts in the instruction.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.signer_key" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#647-657">Source</a><h4 class="code-header">pub fn <a href="#method.signer_key" class="fn">signer_key</a>(
    &amp;self,
    instruction_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>,
    accounts_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;&amp;Pubkey&gt;</h4></section></summary><div class="docblock"><p>Get the <code>Pubkey</code> of a signing account required by one of the
instructions in the transaction.</p>
<p>The transaction does not need to be signed for this function to return a
signing account’s pubkey.</p>
<p>Returns <code>None</code> if the indexed account is not required to sign the
transaction. Returns <code>None</code> if the <a href="struct.Transaction.html#structfield.signatures" title="field solana_transaction::Transaction::signatures"><code>signatures</code></a> field does not contain
enough elements to hold a signature for the indexed account (this should
only be possible if <code>Transaction</code> has been manually constructed).</p>
<p>Returns <code>None</code> if <code>instruction_index</code> is greater than or equal to the
number of instructions in the transaction; or if <code>accounts_index</code> is
greater than or equal to the number of accounts in the instruction.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.message" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#660-662">Source</a><h4 class="code-header">pub fn <a href="#method.message" class="fn">message</a>(&amp;self) -&gt; &amp;<a class="struct" href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a></h4></section></summary><div class="docblock"><p>Return the message containing all data that should be signed.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.message_data" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#666-668">Source</a><h4 class="code-header">pub fn <a href="#method.message_data" class="fn">message_data</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u8.html">u8</a>&gt; <a href="#" class="tooltip" data-notable-ty="Vec&lt;u8&gt;">ⓘ</a></h4></section></summary><div class="docblock"><p>Return the serialized message data to sign.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.sign" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#747-751">Source</a><h4 class="code-header">pub fn <a href="#method.sign" class="fn">sign</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    &amp;mut self,
    keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    recent_blockhash: Hash,
)</h4></section></summary><div class="docblock"><p>Sign the transaction.</p>
<p>This method fully signs a transaction with all required signers, which
must be present in the <code>keypairs</code> slice. To sign with only some of the
required signers, use <a href="struct.Transaction.html#method.partial_sign" title="method solana_transaction::Transaction::partial_sign"><code>Transaction::partial_sign</code></a>.</p>
<p>If <code>recent_blockhash</code> is different than recorded in the transaction message’s
<a href="../solana_message/legacy/struct.Message.html#structfield.recent_blockhash" title="field solana_message::legacy::Message::recent_blockhash"><code>recent_blockhash</code></a> field, then the message’s <code>recent_blockhash</code> will be updated
to the provided <code>recent_blockhash</code>, and any prior signatures will be cleared.</p>
<h5 id="panics-4"><a class="doc-anchor" href="#panics-4">§</a>Panics</h5>
<p>Panics when signing fails. Use <a href="struct.Transaction.html#method.try_sign" title="method solana_transaction::Transaction::try_sign"><code>Transaction::try_sign</code></a> to handle the
error. See the documentation for <a href="struct.Transaction.html#method.try_sign" title="method solana_transaction::Transaction::try_sign"><code>Transaction::try_sign</code></a> for a full description of
failure conditions.</p>
<h5 id="examples-4"><a class="doc-anchor" href="#examples-4">§</a>Examples</h5>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_with_payer(<span class="kw-2">&amp;</span>[instruction], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()));
    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    tx.sign(<span class="kw-2">&amp;</span>[payer], blockhash);
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><details class="toggle method-toggle" open><summary><section id="method.partial_sign" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#774-778">Source</a><h4 class="code-header">pub fn <a href="#method.partial_sign" class="fn">partial_sign</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    &amp;mut self,
    keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    recent_blockhash: Hash,
)</h4></section></summary><div class="docblock"><p>Sign the transaction with a subset of required keys.</p>
<p>Unlike <a href="struct.Transaction.html#method.sign" title="method solana_transaction::Transaction::sign"><code>Transaction::sign</code></a>, this method does not require all keypairs
to be provided, allowing a transaction to be signed in multiple steps.</p>
<p>It is permitted to sign a transaction with the same keypair multiple
times.</p>
<p>If <code>recent_blockhash</code> is different than recorded in the transaction message’s
<a href="../solana_message/legacy/struct.Message.html#structfield.recent_blockhash" title="field solana_message::legacy::Message::recent_blockhash"><code>recent_blockhash</code></a> field, then the message’s <code>recent_blockhash</code> will be updated
to the provided <code>recent_blockhash</code>, and any prior signatures will be cleared.</p>
<h5 id="panics-5"><a class="doc-anchor" href="#panics-5">§</a>Panics</h5>
<p>Panics when signing fails. Use <a href="struct.Transaction.html#method.try_partial_sign" title="method solana_transaction::Transaction::try_partial_sign"><code>Transaction::try_partial_sign</code></a> to
handle the error. See the documentation for
<a href="struct.Transaction.html#method.try_partial_sign" title="method solana_transaction::Transaction::try_partial_sign"><code>Transaction::try_partial_sign</code></a> for a full description of failure
conditions.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.partial_sign_unchecked" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#794-803">Source</a><h4 class="code-header">pub fn <a href="#method.partial_sign_unchecked" class="fn">partial_sign_unchecked</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    &amp;mut self,
    keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    positions: <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>&gt;,
    recent_blockhash: Hash,
)</h4></section></summary><div class="docblock"><p>Sign the transaction with a subset of required keys.</p>
<p>This places each of the signatures created from <code>keypairs</code> in the
corresponding position, as specified in the <code>positions</code> vector, in the
transactions <a href="struct.Transaction.html#structfield.signatures" title="field solana_transaction::Transaction::signatures"><code>signatures</code></a> field. It does not verify that the signature
positions are correct.</p>
<h5 id="panics-6"><a class="doc-anchor" href="#panics-6">§</a>Panics</h5>
<p>Panics if signing fails. Use <a href="struct.Transaction.html#method.try_partial_sign_unchecked" title="method solana_transaction::Transaction::try_partial_sign_unchecked"><code>Transaction::try_partial_sign_unchecked</code></a>
to handle the error.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.try_sign" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#887-899">Source</a><h4 class="code-header">pub fn <a href="#method.try_sign" class="fn">try_sign</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    &amp;mut self,
    keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    recent_blockhash: Hash,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.unit.html">()</a>, SignerError&gt;</h4></section></summary><div class="docblock"><p>Sign the transaction, returning any errors.</p>
<p>This method fully signs a transaction with all required signers, which
must be present in the <code>keypairs</code> slice. To sign with only some of the
required signers, use <a href="struct.Transaction.html#method.try_partial_sign" title="method solana_transaction::Transaction::try_partial_sign"><code>Transaction::try_partial_sign</code></a>.</p>
<p>If <code>recent_blockhash</code> is different than recorded in the transaction message’s
<a href="../solana_message/legacy/struct.Message.html#structfield.recent_blockhash" title="field solana_message::legacy::Message::recent_blockhash"><code>recent_blockhash</code></a> field, then the message’s <code>recent_blockhash</code> will be updated
to the provided <code>recent_blockhash</code>, and any prior signatures will be cleared.</p>
<h5 id="errors"><a class="doc-anchor" href="#errors">§</a>Errors</h5>
<p>Signing will fail if some required signers are not provided in
<code>keypairs</code>; or, if the transaction has previously been partially signed,
some of the remaining required signers are not provided in <code>keypairs</code>.
In other words, the transaction must be fully signed as a result of
calling this function. The error is [<code>SignerError::NotEnoughSigners</code>].</p>
<p>Signing will fail for any of the reasons described in the documentation
for <a href="struct.Transaction.html#method.try_partial_sign" title="method solana_transaction::Transaction::try_partial_sign"><code>Transaction::try_partial_sign</code></a>.</p>
<h5 id="examples-5"><a class="doc-anchor" href="#examples-5">§</a>Examples</h5>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_with_payer(<span class="kw-2">&amp;</span>[instruction], <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()));
    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    tx.try_sign(<span class="kw-2">&amp;</span>[payer], blockhash)<span class="question-mark">?</span>;
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><details class="toggle method-toggle" open><summary><section id="method.try_partial_sign" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#951-962">Source</a><h4 class="code-header">pub fn <a href="#method.try_partial_sign" class="fn">try_partial_sign</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    &amp;mut self,
    keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    recent_blockhash: Hash,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.unit.html">()</a>, SignerError&gt;</h4></section></summary><div class="docblock"><p>Sign the transaction with a subset of required keys, returning any errors.</p>
<p>Unlike <a href="struct.Transaction.html#method.try_sign" title="method solana_transaction::Transaction::try_sign"><code>Transaction::try_sign</code></a>, this method does not require all
keypairs to be provided, allowing a transaction to be signed in multiple
steps.</p>
<p>It is permitted to sign a transaction with the same keypair multiple
times.</p>
<p>If <code>recent_blockhash</code> is different than recorded in the transaction message’s
<a href="../solana_message/legacy/struct.Message.html#structfield.recent_blockhash" title="field solana_message::legacy::Message::recent_blockhash"><code>recent_blockhash</code></a> field, then the message’s <code>recent_blockhash</code> will be updated
to the provided <code>recent_blockhash</code>, and any prior signatures will be cleared.</p>
<h5 id="errors-1"><a class="doc-anchor" href="#errors-1">§</a>Errors</h5>
<p>Signing will fail if</p>
<ul>
<li>The transaction’s <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a> is malformed such that the number of
required signatures recorded in its header
(<a href="https://docs.rs/solana-message/latest/solana_message/struct.MessageHeader.html#structfield.num_required_signatures"><code>num_required_signatures</code></a>) is greater than the length of its
account keys (<a href="https://docs.rs/solana-message/latest/solana_message/legacy/struct.Message.html#structfield.account_keys"><code>account_keys</code></a>). The error is
[<code>SignerError::TransactionError</code>] where the interior
[<code>TransactionError</code>] is [<code>TransactionError::InvalidAccountIndex</code>].</li>
<li>Any of the provided signers in <code>keypairs</code> is not a required signer of
the message. The error is [<code>SignerError::KeypairPubkeyMismatch</code>].</li>
<li>Any of the signers is a <a href="https://docs.rs/solana-presigner/latest/solana_presigner/struct.Presigner.html"><code>Presigner</code></a>, and its provided signature is
incorrect. The error is [<code>SignerError::PresignerError</code>] where the
interior <a href="https://docs.rs/solana-signer/latest/solana_signer/enum.PresignerError.html"><code>PresignerError</code></a> is
<a href="https://docs.rs/solana-signer/latest/solana_signer/enum.PresignerError.html#variant.WrongSize"><code>PresignerError::VerificationFailure</code></a>.</li>
<li>The signer is a <a href="https://docs.rs/solana-remote-wallet/latest/solana_remote_wallet/remote_keypair/struct.RemoteKeypair.html"><code>RemoteKeypair</code></a> and
<ul>
<li>It does not understand the input provided ([<code>SignerError::InvalidInput</code>]).</li>
<li>The device cannot be found ([<code>SignerError::NoDeviceFound</code>]).</li>
<li>The user cancels the signing ([<code>SignerError::UserCancel</code>]).</li>
<li>An error was encountered connecting ([<code>SignerError::Connection</code>]).</li>
<li>Some device-specific protocol error occurs ([<code>SignerError::Protocol</code>]).</li>
<li>Some other error occurs ([<code>SignerError::Custom</code>]).</li>
</ul>
</li>
</ul>
<p>See the documentation for the <a href="https://docs.rs/solana-remote-wallet/latest/"><code>solana-remote-wallet</code></a> crate for details
on the operation of <a href="https://docs.rs/solana-remote-wallet/latest/solana_remote_wallet/remote_keypair/struct.RemoteKeypair.html"><code>RemoteKeypair</code></a> signers.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.try_partial_sign_unchecked" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#978-997">Source</a><h4 class="code-header">pub fn <a href="#method.try_partial_sign_unchecked" class="fn">try_partial_sign_unchecked</a>&lt;T: Signers + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>&gt;(
    &amp;mut self,
    keypairs: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a>,
    positions: <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>&gt;,
    recent_blockhash: Hash,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.unit.html">()</a>, SignerError&gt;</h4></section></summary><div class="docblock"><p>Sign the transaction with a subset of required keys, returning any
errors.</p>
<p>This places each of the signatures created from <code>keypairs</code> in the
corresponding position, as specified in the <code>positions</code> vector, in the
transactions <a href="struct.Transaction.html#structfield.signatures" title="field solana_transaction::Transaction::signatures"><code>signatures</code></a> field. It does not verify that the signature
positions are correct.</p>
<h5 id="errors-2"><a class="doc-anchor" href="#errors-2">§</a>Errors</h5>
<p>Returns an error if signing fails.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_invalid_signature" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#1000-1002">Source</a><h4 class="code-header">pub fn <a href="#method.get_invalid_signature" class="fn">get_invalid_signature</a>() -&gt; Signature</h4></section></summary><div class="docblock"><p>Returns a signature that is not valid for signing this transaction.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_signing_keypair_positions" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#1084-1095">Source</a><h4 class="code-header">pub fn <a href="#method.get_signing_keypair_positions" class="fn">get_signing_keypair_positions</a>(
    &amp;self,
    pubkeys: &amp;[Pubkey],
) -&gt; Result&lt;<a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>&gt;&gt;&gt;</h4></section></summary><div class="docblock"><p>Get the positions of the pubkeys in <code>account_keys</code> associated with signing keypairs.</p>
</div></details><section id="method.is_signed" class="method"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#1127-1131">Source</a><h4 class="code-header">pub fn <a href="#method.is_signed" class="fn">is_signed</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></div></details></div><h2 id="trait-implementations" class="section-header">Trait Implementations<a href="#trait-implementations" class="anchor">§</a></h2><div id="trait-implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-Clone-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#impl-Clone-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.clone" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#method.clone" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#tymethod.clone" class="fn">clone</a>(&amp;self) -&gt; <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h4></section></summary><div class='docblock'>Returns a duplicate of the value. <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#tymethod.clone">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clone_from" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/nightly/src/core/clone.rs.html#209">Source</a></span><a href="#method.clone_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#method.clone_from" class="fn">clone_from</a>(&amp;mut self, source: &amp;Self)</h4></section></summary><div class='docblock'>Performs copy-assignment from <code>source</code>. <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#method.clone_from">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Debug-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#impl-Debug-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html" title="trait core::fmt::Debug">Debug</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.fmt" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#method.fmt" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html#tymethod.fmt" class="fn">fmt</a>(&amp;self, f: &amp;mut <a class="struct" href="https://doc.rust-lang.org/nightly/core/fmt/struct.Formatter.html" title="struct core::fmt::Formatter">Formatter</a>&lt;'_&gt;) -&gt; <a class="type" href="https://doc.rust-lang.org/nightly/core/fmt/type.Result.html" title="type core::fmt::Result">Result</a></h4></section></summary><div class='docblock'>Formats the value using the given formatter. <a href="https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html#tymethod.fmt">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Default-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#impl-Default-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/default/trait.Default.html" title="trait core::default::Default">Default</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.default" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#method.default" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/default/trait.Default.html#tymethod.default" class="fn">default</a>() -&gt; <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h4></section></summary><div class='docblock'>Returns the “default value” for a type. <a href="https://doc.rust-lang.org/nightly/core/default/trait.Default.html#tymethod.default">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Deserialize%3C'de%3E-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#191">Source</a><a href="#impl-Deserialize%3C'de%3E-for-Transaction" class="anchor">§</a><h3 class="code-header">impl&lt;'de&gt; <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html" title="trait serde::de::Deserialize">Deserialize</a>&lt;'de&gt; for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.deserialize" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#191">Source</a><a href="#method.deserialize" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize" class="fn">deserialize</a>&lt;__D&gt;(__deserializer: __D) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, __D::<a class="associatedtype" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html#associatedtype.Error" title="type serde::de::Deserializer::Error">Error</a>&gt;<div class="where">where
    __D: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html" title="trait serde::de::Deserializer">Deserializer</a>&lt;'de&gt;,</div></h4></section></summary><div class='docblock'>Deserialize this value from the given Serde deserializer. <a href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-From%3CTransaction%3E-for-VersionedTransaction" class="impl"><a class="src rightside" href="../src/solana_transaction/versioned/mod.rs.html#61-68">Source</a><a href="#impl-From%3CTransaction%3E-for-VersionedTransaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;<a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a>&gt; for <a class="struct" href="versioned/struct.VersionedTransaction.html" title="struct solana_transaction::versioned::VersionedTransaction">VersionedTransaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/versioned/mod.rs.html#62-67">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(transaction: <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a>) -&gt; Self</h4></section></summary><div class='docblock'>Converts to this type from the input type.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-PartialEq-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#impl-PartialEq-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html" title="trait core::cmp::PartialEq">PartialEq</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.eq" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#method.eq" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq" class="fn">eq</a>(&amp;self, other: &amp;<a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class="toggle method-toggle" open><summary><section id="method.ne" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#262">Source</a></span><a href="#method.ne" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne" class="fn">ne</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,
and should not be overridden without very good reason.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Sanitize-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#231-241">Source</a><a href="#impl-Sanitize-for-Transaction" class="anchor">§</a><h3 class="code-header">impl Sanitize for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><section id="method.sanitize" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#232-240">Source</a><a href="#method.sanitize" class="anchor">§</a><h4 class="code-header">fn <a class="fn">sanitize</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.unit.html">()</a>, SanitizeError&gt;</h4></section></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Serialize-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#191">Source</a><a href="#impl-Serialize-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html" title="trait serde::ser::Serialize">Serialize</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.serialize" class="method trait-impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#191">Source</a><a href="#method.serialize" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize" class="fn">serialize</a>&lt;__S&gt;(&amp;self, __serializer: __S) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;__S::<a class="associatedtype" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Ok" title="type serde::ser::Serializer::Ok">Ok</a>, __S::<a class="associatedtype" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Error" title="type serde::ser::Serializer::Error">Error</a>&gt;<div class="where">where
    __S: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html" title="trait serde::ser::Serializer">Serializer</a>,</div></h4></section></summary><div class='docblock'>Serialize this value into the given Serde serializer. <a href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize">Read more</a></div></details></div></details><section id="impl-Eq-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#impl-Eq-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/cmp/trait.Eq.html" title="trait core::cmp::Eq">Eq</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section><section id="impl-StructuralPartialEq-for-Transaction" class="impl"><a class="src rightside" href="../src/solana_transaction/lib.rs.html#192">Source</a><a href="#impl-StructuralPartialEq-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.StructuralPartialEq.html" title="trait core::marker::StructuralPartialEq">StructuralPartialEq</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-Transaction" class="impl"><a href="#impl-Freeze-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section><section id="impl-RefUnwindSafe-for-Transaction" class="impl"><a href="#impl-RefUnwindSafe-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section><section id="impl-Send-for-Transaction" class="impl"><a href="#impl-Send-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section><section id="impl-Sync-for-Transaction" class="impl"><a href="#impl-Sync-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section><section id="impl-Unpin-for-Transaction" class="impl"><a href="#impl-Unpin-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section><section id="impl-UnwindSafe-for-Transaction" class="impl"><a href="#impl-UnwindSafe-for-Transaction" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/nightly/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/nightly/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-CloneToUninit-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/clone.rs.html#476">Source</a><a href="#impl-CloneToUninit-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.CloneToUninit.html" title="trait core::clone::CloneToUninit">CloneToUninit</a> for T<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.clone_to_uninit" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/clone.rs.html#478">Source</a><a href="#method.clone_to_uninit" class="anchor">§</a><h4 class="code-header">unsafe fn <a href="https://doc.rust-lang.org/nightly/core/clone/trait.CloneToUninit.html#tymethod.clone_to_uninit" class="fn">clone_to_uninit</a>(&amp;self, dest: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.pointer.html">*mut </a><a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u8.html">u8</a>)</h4></section></summary><span class="item-info"><div class="stab unstable"><span class="emoji">🔬</span><span>This is a nightly-only experimental API. (<code>clone_to_uninit</code>)</span></div></span><div class='docblock'>Performs copy-assignment from <code>self</code> to <code>dest</code>. <a href="https://doc.rust-lang.org/nightly/core/clone/trait.CloneToUninit.html#tymethod.clone_to_uninit">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#773">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from-1" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#776">Source</a><a href="#method.from-1" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#756-758">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#766">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Same-for-T" class="impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#34">Source</a><a href="#impl-Same-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html" title="trait typenum::type_operators::Same">Same</a> for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Output" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#35">Source</a><a href="#associatedtype.Output" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html#associatedtype.Output" class="associatedtype">Output</a> = T</h4></section></summary><div class='docblock'>Should always be <code>Self</code></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-ToOwned-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#82-84">Source</a><a href="#impl-ToOwned-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html" title="trait alloc::borrow::ToOwned">ToOwned</a> for T<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Owned" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#86">Source</a><a href="#associatedtype.Owned" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#associatedtype.Owned" class="associatedtype">Owned</a> = T</h4></section></summary><div class='docblock'>The resulting type after obtaining ownership.</div></details><details class="toggle method-toggle" open><summary><section id="method.to_owned" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#87">Source</a><a href="#method.to_owned" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#tymethod.to_owned" class="fn">to_owned</a>(&amp;self) -&gt; T</h4></section></summary><div class='docblock'>Creates owned data from borrowed data, usually by cloning. <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#tymethod.to_owned">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clone_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#91">Source</a><a href="#method.clone_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#method.clone_into" class="fn">clone_into</a>(&amp;self, target: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;mut T</a>)</h4></section></summary><div class='docblock'>Uses borrowed data to replace owned data, usually by cloning. <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#method.clone_into">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#812-814">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#816">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/nightly/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#819">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#797-799">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#801">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#804">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-VZip%3CV%3E-for-T" class="impl"><a href="#impl-VZip%3CV%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;V, T&gt; VZip&lt;V&gt; for T<div class="where">where
    V: MultiLane&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><section id="method.vzip" class="method trait-impl"><a href="#method.vzip" class="anchor">§</a><h4 class="code-header">fn <a class="fn">vzip</a>(self) -&gt; V</h4></section></div></details><section id="impl-DeserializeOwned-for-T" class="impl"><a class="src rightside" href="https://docs.rs/serde/1.0.219/src/serde/de/mod.rs.html#614">Source</a><a href="#impl-DeserializeOwned-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.DeserializeOwned.html" title="trait serde::de::DeserializeOwned">DeserializeOwned</a> for T<div class="where">where
    T: for&lt;'de&gt; <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html" title="trait serde::de::Deserialize">Deserialize</a>&lt;'de&gt;,</div></h3></section></div><script type="text/json" id="notable-traits-data">{"&[u8]":"<h3>Notable traits for <code>&amp;[<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>]</code></h3><pre><code><div class=\"where\">impl <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/std/io/trait.Read.html\" title=\"trait std::io::Read\">Read</a> for &amp;[<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>]</div>","Vec<u8>":"<h3>Notable traits for <code><a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;</code></h3><pre><code><div class=\"where\">impl&lt;A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/std/io/trait.Write.html\" title=\"trait std::io::Write\">Write</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></div>"}</script></section></div></main></body></html>