<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="List of all items in this crate"><title>List of all items in this crate</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod sys"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_transaction/index.html">solana_<wbr>transaction</a><span class="version">2.2.2</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h3><a href="#structs">Crate Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#constants" title="Constants">Constants</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><h1>List of all items</h1><h3 id="structs">Structs</h3><ul class="all-items"><li><a href="struct.Transaction.html">Transaction</a></li><li><a href="sanitized/struct.SanitizedTransaction.html">sanitized::SanitizedTransaction</a></li><li><a href="sanitized/struct.TransactionAccountLocks.html">sanitized::TransactionAccountLocks</a></li><li><a href="versioned/struct.VersionedTransaction.html">versioned::VersionedTransaction</a></li><li><a href="versioned/sanitized/struct.SanitizedVersionedTransaction.html">versioned::sanitized::SanitizedVersionedTransaction</a></li></ul><h3 id="enums">Enums</h3><ul class="all-items"><li><a href="enum.TransactionVerificationMode.html">TransactionVerificationMode</a></li><li><a href="sanitized/enum.MessageHash.html">sanitized::MessageHash</a></li><li><a href="versioned/enum.Legacy.html">versioned::Legacy</a></li><li><a href="versioned/enum.TransactionVersion.html">versioned::TransactionVersion</a></li></ul><h3 id="functions">Functions</h3><ul class="all-items"><li><a href="simple_vote_transaction_checker/fn.is_simple_vote_transaction.html">simple_vote_transaction_checker::is_simple_vote_transaction</a></li><li><a href="simple_vote_transaction_checker/fn.is_simple_vote_transaction_impl.html">simple_vote_transaction_checker::is_simple_vote_transaction_impl</a></li><li><a href="fn.uses_durable_nonce.html">uses_durable_nonce</a></li></ul><h3 id="constants">Constants</h3><ul class="all-items"><li><a href="sanitized/constant.MAX_TX_ACCOUNT_LOCKS.html">sanitized::MAX_TX_ACCOUNT_LOCKS</a></li></ul></section></div></main></body></html>