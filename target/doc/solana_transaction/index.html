<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Atomically-committed sequences of instructions."><title>solana_transaction - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_transaction" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="../crates.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod crate"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_transaction/index.html">solana_<wbr>transaction</a><span class="version">2.2.2</span></h2></div><div class="sidebar-elems"><ul class="block"><li><a id="all-types" href="all.html">All Items</a></li></ul><section id="rustdoc-toc"><h3><a href="#">Sections</a></h3><ul class="block top-toc"><li><a href="#examples" title="Examples">Examples</a></li></ul><h3><a href="#modules">Crate Items</a></h3><ul class="block"><li><a href="#modules" title="Modules">Modules</a></li><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1>Crate <span>solana_transaction</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/solana_transaction/lib.rs.html#1-1712">Source</a> </span></div><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>Atomically-committed sequences of instructions.</p>
<p>While [<code>Instruction</code>]s are the basic unit of computation in Solana, they are
submitted by clients in <a href="struct.Transaction.html" title="struct solana_transaction::Transaction"><code>Transaction</code></a>s containing one or more
instructions, and signed by one or more <a href="https://docs.rs/solana-signer/latest/solana_signer/trait.Signer.html"><code>Signer</code></a>s. Solana executes the
instructions in a transaction in order, and only commits any changes if all
instructions terminate without producing an error or exception.</p>
<p>Transactions do not directly contain their instructions but instead include
a <a href="../solana_message/legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a>, a precompiled representation of a sequence of instructions.
<code>Message</code>’s constructors handle the complex task of reordering the
individual lists of accounts required by each instruction into a single flat
list of deduplicated accounts required by the Solana runtime. The
<code>Transaction</code> type has constructors that build the <code>Message</code> so that clients
don’t need to interact with them directly.</p>
<p>Prior to submission to the network, transactions must be signed by one or
more keypairs, and this signing is typically performed by an abstract
<a href="https://docs.rs/solana-signer/latest/solana_signer/trait.Signer.html"><code>Signer</code></a>, which may be a <a href="https://docs.rs/solana-keypair/latest/solana_keypair/struct.Keypair.html"><code>Keypair</code></a> but may also be other types of
signers including remote wallets, such as Ledger devices, as represented by
the <a href="https://docs.rs/solana-remote-wallet/latest/solana_remote_wallet/remote_keypair/struct.RemoteKeypair.html"><code>RemoteKeypair</code></a> type in the <a href="https://docs.rs/solana-remote-wallet/latest/"><code>solana-remote-wallet</code></a> crate.</p>
<p>Every transaction must be signed by a fee-paying account, the account from
which the cost of executing the transaction is withdrawn. Other required
signatures are determined by the requirements of the programs being executed
by each instruction, and are conventionally specified by that program’s
documentation.</p>
<p>When signing a transaction, a recent blockhash must be provided (which can
be retrieved with <a href="https://docs.rs/solana-rpc-client/latest/solana_rpc_client/rpc_client/struct.RpcClient.html#method.get_latest_blockhash"><code>RpcClient::get_latest_blockhash</code></a>). This allows
validators to drop old but unexecuted transactions; and to distinguish
between accidentally duplicated transactions and intentionally duplicated
transactions — any identical transactions will not be executed more
than once, so updating the blockhash between submitting otherwise identical
transactions makes them unique. If a client must sign a transaction long
before submitting it to the network, then it can use the <em><a href="https://docs.solanalabs.com/implemented-proposals/durable-tx-nonces">durable
transaction nonce</a></em> mechanism instead of a recent blockhash to ensure unique
transactions.</p>
<h2 id="examples"><a class="doc-anchor" href="#examples">§</a>Examples</h2>
<p>This example uses the <a href="https://docs.rs/solana-rpc-client"><code>solana_rpc_client</code></a> and <a href="https://docs.rs/anyhow"><code>anyhow</code></a> crates.</p>

<div class="example-wrap"><pre class="rust rust-example-rendered"><code><span class="kw">use </span>anyhow::Result;
<span class="kw">use </span>borsh::{BorshSerialize, BorshDeserialize};
<span class="kw">use </span>solana_instruction::Instruction;
<span class="kw">use </span>solana_keypair::Keypair;
<span class="kw">use </span>solana_message::Message;
<span class="kw">use </span>solana_pubkey::Pubkey;
<span class="kw">use </span>solana_rpc_client::rpc_client::RpcClient;
<span class="kw">use </span>solana_signer::Signer;
<span class="kw">use </span>solana_transaction::Transaction;

<span class="comment">// A custom program instruction. This would typically be defined in
// another crate so it can be shared between the on-chain program and
// the client.
</span><span class="attr">#[derive(BorshSerialize, BorshDeserialize)]
</span><span class="kw">enum </span>BankInstruction {
    Initialize,
    Deposit { lamports: u64 },
    Withdraw { lamports: u64 },
}

<span class="kw">fn </span>send_initialize_tx(
    client: <span class="kw-2">&amp;</span>RpcClient,
    program_id: Pubkey,
    payer: <span class="kw-2">&amp;</span>Keypair
) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {

    <span class="kw">let </span>bank_instruction = BankInstruction::Initialize;

    <span class="kw">let </span>instruction = Instruction::new_with_borsh(
        program_id,
        <span class="kw-2">&amp;</span>bank_instruction,
        <span class="macro">vec!</span>[],
    );

    <span class="kw">let </span>blockhash = client.get_latest_blockhash()<span class="question-mark">?</span>;
    <span class="kw">let </span><span class="kw-2">mut </span>tx = Transaction::new_signed_with_payer(
        <span class="kw-2">&amp;</span>[instruction],
        <span class="prelude-val">Some</span>(<span class="kw-2">&amp;</span>payer.pubkey()),
        <span class="kw-2">&amp;</span>[payer],
        blockhash,
    );
    client.send_and_confirm_transaction(<span class="kw-2">&amp;</span>tx)<span class="question-mark">?</span>;

    <span class="prelude-val">Ok</span>(())
}</code></pre></div>
</div></details><h2 id="modules" class="section-header">Modules<a href="#modules" class="anchor">§</a></h2><dl class="item-table"><dt><a class="mod" href="sanitized/index.html" title="mod solana_transaction::sanitized">sanitized</a></dt><dt><a class="mod" href="simple_vote_transaction_checker/index.html" title="mod solana_transaction::simple_vote_transaction_checker">simple_<wbr>vote_<wbr>transaction_<wbr>checker</a></dt><dt><a class="mod" href="versioned/index.html" title="mod solana_transaction::versioned">versioned</a></dt><dd>Defines a transaction which supports multiple versions of messages.</dd></dl><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.Transaction.html" title="struct solana_transaction::Transaction">Transaction</a></dt><dd>An atomically-committed sequence of instructions.</dd></dl><h2 id="enums" class="section-header">Enums<a href="#enums" class="anchor">§</a></h2><dl class="item-table"><dt><a class="enum" href="enum.TransactionVerificationMode.html" title="enum solana_transaction::TransactionVerificationMode">Transaction<wbr>Verification<wbr>Mode</a></dt></dl><h2 id="functions" class="section-header">Functions<a href="#functions" class="anchor">§</a></h2><dl class="item-table"><dt><a class="fn" href="fn.uses_durable_nonce.html" title="fn solana_transaction::uses_durable_nonce">uses_<wbr>durable_<wbr>nonce</a></dt><dd>Returns true if transaction begins with an advance nonce instruction.</dd></dl></section></div></main></body></html>