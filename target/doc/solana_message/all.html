<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="List of all items in this crate"><title>List of all items in this crate</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod sys"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_message/index.html">solana_<wbr>message</a><span class="version">2.2.1</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h3><a href="#structs">Crate Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#constants" title="Constants">Constants</a></li><li><a href="#traits" title="Traits">Traits</a></li><li><a href="#functions" title="Functions">Functions</a></li><li><a href="#types" title="Type Aliases">Type Aliases</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><h1>List of all items</h1><h3 id="structs">Structs</h3><ul class="all-items"><li><a href="struct.AccountKeys.html">AccountKeys</a></li><li><a href="struct.AddressLookupTableAccount.html">AddressLookupTableAccount</a></li><li><a href="struct.LegacyMessage.html">LegacyMessage</a></li><li><a href="struct.MessageHeader.html">MessageHeader</a></li><li><a href="struct.SanitizedVersionedMessage.html">SanitizedVersionedMessage</a></li><li><a href="struct.TransactionSignatureDetails.html">TransactionSignatureDetails</a></li><li><a href="compiled_instruction/struct.CompiledInstruction.html">compiled_instruction::CompiledInstruction</a></li><li><a href="inner_instruction/struct.InnerInstruction.html">inner_instruction::InnerInstruction</a></li><li><a href="legacy/struct.BUILTIN_PROGRAMS_KEYS.html">legacy::BUILTIN_PROGRAMS_KEYS</a></li><li><a href="legacy/struct.MAYBE_BUILTIN_KEY_OR_SYSVAR.html">legacy::MAYBE_BUILTIN_KEY_OR_SYSVAR</a></li><li><a href="legacy/struct.Message.html">legacy::Message</a></li><li><a href="v0/struct.LoadedAddresses.html">v0::LoadedAddresses</a></li><li><a href="v0/struct.LoadedMessage.html">v0::LoadedMessage</a></li><li><a href="v0/struct.Message.html">v0::Message</a></li><li><a href="v0/struct.MessageAddressTableLookup.html">v0::MessageAddressTableLookup</a></li></ul><h3 id="enums">Enums</h3><ul class="all-items"><li><a href="enum.AddressLoaderError.html">AddressLoaderError</a></li><li><a href="enum.CompileError.html">CompileError</a></li><li><a href="enum.SanitizeMessageError.html">SanitizeMessageError</a></li><li><a href="enum.SanitizedMessage.html">SanitizedMessage</a></li><li><a href="enum.SimpleAddressLoader.html">SimpleAddressLoader</a></li><li><a href="enum.VersionedMessage.html">VersionedMessage</a></li></ul><h3 id="traits">Traits</h3><ul class="all-items"><li><a href="trait.AddressLoader.html">AddressLoader</a></li></ul><h3 id="functions">Functions</h3><ul class="all-items"><li><a href="legacy/fn.is_builtin_key_or_sysvar.html">legacy::is_builtin_key_or_sysvar</a></li></ul><h3 id="types">Type Aliases</h3><ul class="all-items"><li><a href="inner_instruction/type.InnerInstructions.html">inner_instruction::InnerInstructions</a></li><li><a href="inner_instruction/type.InnerInstructionsList.html">inner_instruction::InnerInstructionsList</a></li></ul><h3 id="constants">Constants</h3><ul class="all-items"><li><a href="constant.MESSAGE_HEADER_LENGTH.html">MESSAGE_HEADER_LENGTH</a></li><li><a href="constant.MESSAGE_VERSION_PREFIX.html">MESSAGE_VERSION_PREFIX</a></li></ul></section></div></main></body></html>