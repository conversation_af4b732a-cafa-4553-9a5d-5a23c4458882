<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="The original and current Solana message format."><title>solana_message::legacy - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../solana_message/index.html">solana_<wbr>message</a><span class="version">2.2.1</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module legacy</a></h2><h3><a href="#reexports">Module Items</a></h3><ul class="block"><li><a href="#reexports" title="Re-exports">Re-exports</a></li><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="../index.html">In crate solana_<wbr>message</a></h2></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">solana_message</a></div><h1>Module <span>legacy</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/solana_message/legacy.rs.html#1-1026">Source</a> </span></div><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>The original and current Solana message format.</p>
<p>This crate defines two versions of <code>Message</code> in their own modules:
<a href="index.html" title="mod solana_message::legacy"><code>legacy</code></a> and <a href="../v0/index.html" title="mod solana_message::v0"><code>v0</code></a>. <code>legacy</code> is the current version as of Solana 1.10.0.
<code>v0</code> is a <a href="https://docs.solanalabs.com/proposals/versioned-transactions">future message format</a> that encodes more account keys into a
transaction than the legacy format.</p>
</div></details><h2 id="reexports" class="section-header">Re-exports<a href="#reexports" class="anchor">§</a></h2><dl class="item-table reexports"><dt id="reexport.BUILTIN_PROGRAMS_KEYS"><code>pub use builtins::<a class="struct" href="struct.BUILTIN_PROGRAMS_KEYS.html" title="struct solana_message::legacy::BUILTIN_PROGRAMS_KEYS">BUILTIN_PROGRAMS_KEYS</a>;</code><wbr><span class="stab deprecated" title="">Deprecated</span></dt><dt id="reexport.MAYBE_BUILTIN_KEY_OR_SYSVAR"><code>pub use builtins::<a class="struct" href="struct.MAYBE_BUILTIN_KEY_OR_SYSVAR.html" title="struct solana_message::legacy::MAYBE_BUILTIN_KEY_OR_SYSVAR">MAYBE_BUILTIN_KEY_OR_SYSVAR</a>;</code><wbr><span class="stab deprecated" title="">Deprecated</span></dt></dl><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.BUILTIN_PROGRAMS_KEYS.html" title="struct solana_message::legacy::BUILTIN_PROGRAMS_KEYS">BUILTIN_<wbr>PROGRAMS_<wbr>KEYS</a><wbr><span class="stab deprecated" title="">Deprecated</span></dt><dt><a class="struct" href="struct.MAYBE_BUILTIN_KEY_OR_SYSVAR.html" title="struct solana_message::legacy::MAYBE_BUILTIN_KEY_OR_SYSVAR">MAYBE_<wbr>BUILTIN_<wbr>KEY_<wbr>OR_<wbr>SYSVAR</a><wbr><span class="stab deprecated" title="">Deprecated</span></dt><dt><a class="struct" href="struct.Message.html" title="struct solana_message::legacy::Message">Message</a></dt><dd>A Solana transaction message (legacy).</dd></dl><h2 id="functions" class="section-header">Functions<a href="#functions" class="anchor">§</a></h2><dl class="item-table"><dt><a class="fn" href="fn.is_builtin_key_or_sysvar.html" title="fn solana_message::legacy::is_builtin_key_or_sysvar">is_<wbr>builtin_<wbr>key_<wbr>or_<wbr>sysvar</a><wbr><span class="stab deprecated" title="">Deprecated</span></dt></dl></section></div></main></body></html>