<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `AddressLoader` trait in crate `solana_message`."><title>AddressLoader in solana_message - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_message/index.html">solana_<wbr>message</a><span class="version">2.2.1</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Address<wbr>Loader</a></h2><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.load_addresses" title="load_addresses">load_addresses</a></li></ul><h3><a href="#dyn-compatibility">Dyn Compatibility</a></h3><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="index.html">In crate solana_<wbr>message</a></h2></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="index.html">solana_message</a></div><h1>Trait <span class="trait">AddressLoader</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/solana_message/address_loader.rs.html#8-13">Source</a> </span></div><pre class="rust item-decl"><code>pub trait AddressLoader: <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a> {
    // Required method
    fn <a href="#tymethod.load_addresses" class="fn">load_addresses</a>(
        self,
        lookups: &amp;[<a class="struct" href="v0/struct.MessageAddressTableLookup.html" title="struct solana_message::v0::MessageAddressTableLookup">MessageAddressTableLookup</a>],
    ) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="v0/struct.LoadedAddresses.html" title="struct solana_message::v0::LoadedAddresses">LoadedAddresses</a>, <a class="enum" href="enum.AddressLoaderError.html" title="enum solana_message::AddressLoaderError">AddressLoaderError</a>&gt;;
}</code></pre><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.load_addresses" class="method"><a class="src rightside" href="../src/solana_message/address_loader.rs.html#9-12">Source</a><h4 class="code-header">fn <a href="#tymethod.load_addresses" class="fn">load_addresses</a>(
    self,
    lookups: &amp;[<a class="struct" href="v0/struct.MessageAddressTableLookup.html" title="struct solana_message::v0::MessageAddressTableLookup">MessageAddressTableLookup</a>],
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="v0/struct.LoadedAddresses.html" title="struct solana_message::v0::LoadedAddresses">LoadedAddresses</a>, <a class="enum" href="enum.AddressLoaderError.html" title="enum solana_message::AddressLoaderError">AddressLoaderError</a>&gt;</h4></section></div><h2 id="dyn-compatibility" class="section-header">Dyn Compatibility<a href="#dyn-compatibility" class="anchor">§</a></h2><div class="dyn-compatibility-info"><p>This trait is <b>not</b> <a href="https://doc.rust-lang.org/nightly/reference/items/traits.html#dyn-compatibility">dyn compatible</a>.</p><p><i>In older versions of Rust, dyn compatibility was called "object safety", so this trait is not object safe.</i></p></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"><section id="impl-AddressLoader-for-SimpleAddressLoader" class="impl"><a class="src rightside" href="../src/solana_message/address_loader.rs.html#21-31">Source</a><a href="#impl-AddressLoader-for-SimpleAddressLoader" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.AddressLoader.html" title="trait solana_message::AddressLoader">AddressLoader</a> for <a class="enum" href="enum.SimpleAddressLoader.html" title="enum solana_message::SimpleAddressLoader">SimpleAddressLoader</a></h3></section></div><script src="../trait.impl/solana_message/non_bpf_modules/address_loader/trait.AddressLoader.js" async></script></section></div></main></body></html>