<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Sequences of `Instruction`s executed within a single transaction."><title>solana_message - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="../crates.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod crate"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_message/index.html">solana_<wbr>message</a><span class="version">2.2.1</span></h2></div><div class="sidebar-elems"><ul class="block"><li><a id="all-types" href="all.html">All Items</a></li></ul><section id="rustdoc-toc"><h3><a href="#reexports">Crate Items</a></h3><ul class="block"><li><a href="#reexports" title="Re-exports">Re-exports</a></li><li><a href="#modules" title="Modules">Modules</a></li><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#constants" title="Constants">Constants</a></li><li><a href="#traits" title="Traits">Traits</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1>Crate <span>solana_message</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/solana_message/lib.rs.html#1-132">Source</a> </span></div><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>Sequences of <a href="https://docs.rs/solana-instruction/latest/solana_instruction/struct.Instruction.html"><code>Instruction</code></a>s executed within a single transaction.</p>
<p>In Solana, programs execute instructions, and clients submit sequences
of instructions to the network to be atomically executed as <a href="https://docs.rs/solana-sdk/latest/solana-sdk/transaction/struct.Transaction.html"><code>Transaction</code></a>s.</p>
<p>A <a href="legacy/struct.Message.html" title="struct solana_message::legacy::Message"><code>Message</code></a> is the compact internal encoding of a transaction, as
transmitted across the network and stored in, and operated on, by the
runtime. It contains a flat array of all accounts accessed by all
instructions in the message, a <a href="struct.MessageHeader.html" title="struct solana_message::MessageHeader"><code>MessageHeader</code></a> that describes the layout
of that account array, a <a href="https://solana.com/docs/core/transactions#recent-blockhash">recent blockhash</a>, and a compact encoding of the
message’s instructions.</p>
<p>Clients most often deal with <code>Instruction</code>s and <code>Transaction</code>s, with
<code>Message</code>s being created by <code>Transaction</code> constructors.</p>
<p>To ensure reliable network delivery, serialized messages must fit into the
IPv6 MTU size, conservatively assumed to be 1280 bytes. Thus constrained,
care must be taken in the amount of data consumed by instructions, and the
number of accounts they require to function.</p>
<p>This module defines two versions of <code>Message</code> in their own modules:
<a href="legacy/index.html" title="mod solana_message::legacy"><code>legacy</code></a> and <a href="v0/index.html" title="mod solana_message::v0"><code>v0</code></a>. <code>legacy</code> is reexported here and is the current
version as of Solana 1.10.0. <code>v0</code> is a <a href="https://docs.solanalabs.com/proposals/versioned-transactions">future message format</a> that encodes
more account keys into a transaction than the legacy format. The
<a href="enum.VersionedMessage.html" title="enum solana_message::VersionedMessage"><code>VersionedMessage</code></a> type is a thin wrapper around either message version.</p>
<p>Despite living in the <code>solana-program</code> crate, there is no way to access the
runtime’s messages from within a Solana program, and only the legacy message
types continue to be exposed to Solana programs, for backwards compatibility
reasons.</p>
</div></details><h2 id="reexports" class="section-header">Re-exports<a href="#reexports" class="anchor">§</a></h2><dl class="item-table reexports"><dt id="reexport.Message"><code>pub use legacy::<a class="struct" href="legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a>;</code></dt></dl><h2 id="modules" class="section-header">Modules<a href="#modules" class="anchor">§</a></h2><dl class="item-table"><dt><a class="mod" href="compiled_instruction/index.html" title="mod solana_message::compiled_instruction">compiled_<wbr>instruction</a></dt><dt><a class="mod" href="inner_instruction/index.html" title="mod solana_message::inner_instruction">inner_<wbr>instruction</a></dt><dt><a class="mod" href="legacy/index.html" title="mod solana_message::legacy">legacy</a></dt><dd>The original and current Solana message format.</dd><dt><a class="mod" href="v0/index.html" title="mod solana_message::v0">v0</a></dt><dd>A future Solana message format.</dd></dl><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.AccountKeys.html" title="struct solana_message::AccountKeys">Account<wbr>Keys</a></dt><dd>Collection of static and dynamically loaded keys used to load accounts
during transaction processing.</dd><dt><a class="struct" href="struct.AddressLookupTableAccount.html" title="struct solana_message::AddressLookupTableAccount">Address<wbr>Lookup<wbr>Table<wbr>Account</a></dt><dd>The definition of address lookup table accounts.</dd><dt><a class="struct" href="struct.LegacyMessage.html" title="struct solana_message::LegacyMessage">Legacy<wbr>Message</a></dt><dt><a class="struct" href="struct.MessageHeader.html" title="struct solana_message::MessageHeader">Message<wbr>Header</a></dt><dd>Describes the organization of a <code>Message</code>’s account keys.</dd><dt><a class="struct" href="struct.SanitizedVersionedMessage.html" title="struct solana_message::SanitizedVersionedMessage">Sanitized<wbr>Versioned<wbr>Message</a></dt><dd>Wraps a sanitized <code>VersionedMessage</code> to provide a safe API</dd><dt><a class="struct" href="struct.TransactionSignatureDetails.html" title="struct solana_message::TransactionSignatureDetails">Transaction<wbr>Signature<wbr>Details</a></dt><dd>Transaction signature details including the number of transaction signatures
and precompile signatures.</dd></dl><h2 id="enums" class="section-header">Enums<a href="#enums" class="anchor">§</a></h2><dl class="item-table"><dt><a class="enum" href="enum.AddressLoaderError.html" title="enum solana_message::AddressLoaderError">Address<wbr>Loader<wbr>Error</a></dt><dt><a class="enum" href="enum.CompileError.html" title="enum solana_message::CompileError">Compile<wbr>Error</a></dt><dt><a class="enum" href="enum.SanitizeMessageError.html" title="enum solana_message::SanitizeMessageError">Sanitize<wbr>Message<wbr>Error</a></dt><dt><a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">Sanitized<wbr>Message</a></dt><dd>Sanitized message of a transaction.</dd><dt><a class="enum" href="enum.SimpleAddressLoader.html" title="enum solana_message::SimpleAddressLoader">Simple<wbr>Address<wbr>Loader</a></dt><dt><a class="enum" href="enum.VersionedMessage.html" title="enum solana_message::VersionedMessage">Versioned<wbr>Message</a></dt><dd>Either a legacy message or a v0 message.</dd></dl><h2 id="constants" class="section-header">Constants<a href="#constants" class="anchor">§</a></h2><dl class="item-table"><dt><a class="constant" href="constant.MESSAGE_HEADER_LENGTH.html" title="constant solana_message::MESSAGE_HEADER_LENGTH">MESSAGE_<wbr>HEADER_<wbr>LENGTH</a></dt><dd>The length of a message header in bytes.</dd><dt><a class="constant" href="constant.MESSAGE_VERSION_PREFIX.html" title="constant solana_message::MESSAGE_VERSION_PREFIX">MESSAGE_<wbr>VERSION_<wbr>PREFIX</a></dt><dd>Bit mask that indicates whether a serialized message is versioned.</dd></dl><h2 id="traits" class="section-header">Traits<a href="#traits" class="anchor">§</a></h2><dl class="item-table"><dt><a class="trait" href="trait.AddressLoader.html" title="trait solana_message::AddressLoader">Address<wbr>Loader</a></dt></dl><script type="text/json" id="notable-traits-data">{"&<Vec<T, A> as Index<I>>::Output":"<h3>Notable traits for <code><a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;</code></h3><pre><code><div class=\"where\">impl&lt;A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/std/io/trait.Write.html\" title=\"trait std::io::Write\">Write</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></div>","&mut <Vec<T, A> as Index<I>>::Output":"<h3>Notable traits for <code><a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;</code></h3><pre><code><div class=\"where\">impl&lt;A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/std/io/trait.Write.html\" title=\"trait std::io::Write\">Write</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></div>","<Vec<T, A> as IntoIterator>::IntoIter":"<h3>Notable traits for <code><a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;</code></h3><pre><code><div class=\"where\">impl&lt;A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/std/io/trait.Write.html\" title=\"trait std::io::Write\">Write</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></div>","<Vec<T> as IntoDeserializer<'de, E>>::Deserializer":"<h3>Notable traits for <code><a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;</code></h3><pre><code><div class=\"where\">impl&lt;A&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/std/io/trait.Write.html\" title=\"trait std::io::Write\">Write</a> for <a class=\"struct\" href=\"https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html\" title=\"struct alloc::vec::Vec\">Vec</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/nightly/std/primitive.u8.html\">u8</a>, A&gt;<div class=\"where\">where\n    A: <a class=\"trait\" href=\"https://doc.rust-lang.org/nightly/core/alloc/trait.Allocator.html\" title=\"trait core::alloc::Allocator\">Allocator</a>,</div></div>"}</script></section></div></main></body></html>