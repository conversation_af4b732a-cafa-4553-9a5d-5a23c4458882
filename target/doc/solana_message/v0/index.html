<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="A future Solana message format."><title>solana_message::v0 - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../solana_message/index.html">solana_<wbr>message</a><span class="version">2.2.1</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module v0</a></h2><h3><a href="#structs">Module Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="../index.html">In crate solana_<wbr>message</a></h2></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">solana_message</a></div><h1>Module <span>v0</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/solana_message/versions/v0/mod.rs.html#1-781">Source</a> </span></div><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>A future Solana message format.</p>
<p>This crate defines two versions of <code>Message</code> in their own modules:
<a href="../legacy/index.html" title="mod solana_message::legacy"><code>legacy</code></a> and <a href="index.html" title="mod solana_message::v0"><code>v0</code></a>. <code>legacy</code> is the current version as of Solana 1.10.0.
<code>v0</code> is a <a href="https://docs.solanalabs.com/proposals/versioned-transactions">future message format</a> that encodes more account keys into a
transaction than the legacy format.</p>
</div></details><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.LoadedAddresses.html" title="struct solana_message::v0::LoadedAddresses">Loaded<wbr>Addresses</a></dt><dd>Collection of addresses loaded from on-chain lookup tables, split
by readonly and writable.</dd><dt><a class="struct" href="struct.LoadedMessage.html" title="struct solana_message::v0::LoadedMessage">Loaded<wbr>Message</a></dt><dd>Combination of a version #0 message and its loaded addresses</dd><dt><a class="struct" href="struct.Message.html" title="struct solana_message::v0::Message">Message</a></dt><dd>A Solana transaction message (v0).</dd><dt><a class="struct" href="struct.MessageAddressTableLookup.html" title="struct solana_message::v0::MessageAddressTableLookup">Message<wbr>Address<wbr>Table<wbr>Lookup</a></dt><dd>Address table lookups describe an on-chain address lookup table to use
for loading more readonly and writable accounts in a single tx.</dd></dl></section></div></main></body></html>