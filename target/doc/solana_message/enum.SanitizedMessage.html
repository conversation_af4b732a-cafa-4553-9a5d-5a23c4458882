<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Sanitized message of a transaction."><title>SanitizedMessage in solana_message - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-84e720fa.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="solana_message" data-themes="" data-resource-suffix="" data-rustdoc-version="1.89.0-nightly (cdd545be1 2025-06-07)" data-channel="nightly" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../static.files/main-fd3af306.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-32bb7600.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc enum"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../solana_message/index.html">solana_<wbr>message</a><span class="version">2.2.1</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Sanitized<wbr>Message</a></h2><h3><a href="#variants">Variants</a></h3><ul class="block variant"><li><a href="#variant.Legacy" title="Legacy">Legacy</a></li><li><a href="#variant.V0" title="V0">V0</a></li></ul><h3><a href="#implementations">Methods</a></h3><ul class="block method"><li><a href="#method.account_keys" title="account_keys">account_keys</a></li><li><a href="#method.decompile_instructions" title="decompile_instructions">decompile_instructions</a></li><li><a href="#method.fee_payer" title="fee_payer">fee_payer</a></li><li><a href="#method.get_durable_nonce" title="get_durable_nonce">get_durable_nonce</a></li><li><a href="#method.get_ix_signers" title="get_ix_signers">get_ix_signers</a></li><li><a href="#method.get_signature_details" title="get_signature_details">get_signature_details</a></li><li><a href="#method.has_duplicates" title="has_duplicates">has_duplicates</a></li><li><a href="#method.header" title="header">header</a></li><li><a href="#method.instructions" title="instructions">instructions</a></li><li><a href="#method.is_instruction_account" title="is_instruction_account">is_instruction_account</a></li><li><a href="#method.is_invoked" title="is_invoked">is_invoked</a></li><li><a href="#method.is_key_passed_to_program" title="is_key_passed_to_program">is_key_passed_to_program</a></li><li><a href="#method.is_non_loader_key" title="is_non_loader_key">is_non_loader_key</a></li><li><a href="#method.is_signer" title="is_signer">is_signer</a></li><li><a href="#method.is_upgradeable_loader_present" title="is_upgradeable_loader_present">is_upgradeable_loader_present</a></li><li><a href="#method.is_writable" title="is_writable">is_writable</a></li><li><a href="#method.legacy_message" title="legacy_message">legacy_message</a></li><li><a href="#method.message_address_table_lookups" title="message_address_table_lookups">message_address_table_lookups</a></li><li><a href="#method.num_readonly_accounts" title="num_readonly_accounts">num_readonly_accounts</a></li><li><a href="#method.num_signatures" title="num_signatures">num_signatures</a></li><li><a href="#method.num_total_signatures" title="num_total_signatures">num_total_signatures</a></li><li><a href="#method.num_write_locks" title="num_write_locks">num_write_locks</a></li><li><a href="#method.program_instructions_iter" title="program_instructions_iter">program_instructions_iter</a></li><li><a href="#method.recent_blockhash" title="recent_blockhash">recent_blockhash</a></li><li><a href="#method.static_account_keys" title="static_account_keys">static_account_keys</a></li><li><a href="#method.try_from_legacy_message" title="try_from_legacy_message">try_from_legacy_message</a></li><li><a href="#method.try_new" title="try_new">try_new</a></li></ul><h3><a href="#trait-implementations">Trait Implementations</a></h3><ul class="block trait-implementation"><li><a href="#impl-Clone-for-SanitizedMessage" title="Clone">Clone</a></li><li><a href="#impl-Debug-for-SanitizedMessage" title="Debug">Debug</a></li><li><a href="#impl-Eq-for-SanitizedMessage" title="Eq">Eq</a></li><li><a href="#impl-PartialEq-for-SanitizedMessage" title="PartialEq">PartialEq</a></li><li><a href="#impl-StructuralPartialEq-for-SanitizedMessage" title="StructuralPartialEq">StructuralPartialEq</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-Freeze-for-SanitizedMessage" title="Freeze">Freeze</a></li><li><a href="#impl-RefUnwindSafe-for-SanitizedMessage" title="RefUnwindSafe">RefUnwindSafe</a></li><li><a href="#impl-Send-for-SanitizedMessage" title="Send">Send</a></li><li><a href="#impl-Sync-for-SanitizedMessage" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-SanitizedMessage" title="Unpin">Unpin</a></li><li><a href="#impl-UnwindSafe-for-SanitizedMessage" title="UnwindSafe">UnwindSafe</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-CloneToUninit-for-T" title="CloneToUninit">CloneToUninit</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-Same-for-T" title="Same">Same</a></li><li><a href="#impl-ToOwned-for-T" title="ToOwned">ToOwned</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="index.html">In crate solana_<wbr>message</a></h2></div></div></nav><div class="sidebar-resizer" title="Drag to resize sidebar"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="index.html">solana_message</a></div><h1>Enum <span class="enum">SanitizedMessage</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/solana_message/sanitized.rs.html#82-87">Source</a> </span></div><pre class="rust item-decl"><code>pub enum SanitizedMessage {
    Legacy(<a class="struct" href="struct.LegacyMessage.html" title="struct solana_message::LegacyMessage">LegacyMessage</a>&lt;'static&gt;),
    V0(<a class="struct" href="v0/struct.LoadedMessage.html" title="struct solana_message::v0::LoadedMessage">LoadedMessage</a>&lt;'static&gt;),
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>Sanitized message of a transaction.</p>
</div></details><h2 id="variants" class="variants section-header">Variants<a href="#variants" class="anchor">§</a></h2><div class="variants"><section id="variant.Legacy" class="variant"><a href="#variant.Legacy" class="anchor">§</a><h3 class="code-header">Legacy(<a class="struct" href="struct.LegacyMessage.html" title="struct solana_message::LegacyMessage">LegacyMessage</a>&lt;'static&gt;)</h3></section><div class="docblock"><p>Sanitized legacy message</p>
</div><section id="variant.V0" class="variant"><a href="#variant.V0" class="anchor">§</a><h3 class="code-header">V0(<a class="struct" href="v0/struct.LoadedMessage.html" title="struct solana_message::v0::LoadedMessage">LoadedMessage</a>&lt;'static&gt;)</h3></section><div class="docblock"><p>Sanitized version #0 message with dynamically loaded addresses</p>
</div></div><h2 id="implementations" class="section-header">Implementations<a href="#implementations" class="anchor">§</a></h2><div id="implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-SanitizedMessage" class="impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#89-426">Source</a><a href="#impl-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.try_new" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#93-112">Source</a><h4 class="code-header">pub fn <a href="#method.try_new" class="fn">try_new</a>(
    sanitized_msg: <a class="struct" href="struct.SanitizedVersionedMessage.html" title="struct solana_message::SanitizedVersionedMessage">SanitizedVersionedMessage</a>,
    address_loader: impl <a class="trait" href="trait.AddressLoader.html" title="trait solana_message::AddressLoader">AddressLoader</a>,
    reserved_account_keys: &amp;<a class="struct" href="https://doc.rust-lang.org/nightly/std/collections/hash/set/struct.HashSet.html" title="struct std::collections::hash::set::HashSet">HashSet</a>&lt;Pubkey&gt;,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, <a class="enum" href="enum.SanitizeMessageError.html" title="enum solana_message::SanitizeMessageError">SanitizeMessageError</a>&gt;</h4></section></summary><div class="docblock"><p>Create a sanitized message from a sanitized versioned message.
If the input message uses address tables, attempt to look up the
address for each table index.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from_legacy_message" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#115-124">Source</a><h4 class="code-header">pub fn <a href="#method.try_from_legacy_message" class="fn">try_from_legacy_message</a>(
    message: <a class="struct" href="legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a>,
    reserved_account_keys: &amp;<a class="struct" href="https://doc.rust-lang.org/nightly/std/collections/hash/set/struct.HashSet.html" title="struct std::collections::hash::set::HashSet">HashSet</a>&lt;Pubkey&gt;,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, <a class="enum" href="enum.SanitizeMessageError.html" title="enum solana_message::SanitizeMessageError">SanitizeMessageError</a>&gt;</h4></section></summary><div class="docblock"><p>Create a sanitized legacy message</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.has_duplicates" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#127-132">Source</a><h4 class="code-header">pub fn <a href="#method.has_duplicates" class="fn">has_duplicates</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class="docblock"><p>Return true if this message contains duplicate account keys</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.header" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#136-141">Source</a><h4 class="code-header">pub fn <a href="#method.header" class="fn">header</a>(&amp;self) -&gt; &amp;<a class="struct" href="struct.MessageHeader.html" title="struct solana_message::MessageHeader">MessageHeader</a></h4></section></summary><div class="docblock"><p>Message header which identifies the number of signer and writable or
readonly accounts</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.legacy_message" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#144-150">Source</a><h4 class="code-header">pub fn <a href="#method.legacy_message" class="fn">legacy_message</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;&amp;<a class="struct" href="legacy/struct.Message.html" title="struct solana_message::legacy::Message">Message</a>&gt;</h4></section></summary><div class="docblock"><p>Returns a legacy message if this sanitized message wraps one</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.fee_payer" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#153-157">Source</a><h4 class="code-header">pub fn <a href="#method.fee_payer" class="fn">fee_payer</a>(&amp;self) -&gt; &amp;Pubkey</h4></section></summary><div class="docblock"><p>Returns the fee payer for the transaction</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.recent_blockhash" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#160-165">Source</a><h4 class="code-header">pub fn <a href="#method.recent_blockhash" class="fn">recent_blockhash</a>(&amp;self) -&gt; &amp;Hash</h4></section></summary><div class="docblock"><p>The hash of a recent block, used for timing out a transaction</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.instructions" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#169-174">Source</a><h4 class="code-header">pub fn <a href="#method.instructions" class="fn">instructions</a>(&amp;self) -&gt; &amp;[<a class="struct" href="compiled_instruction/struct.CompiledInstruction.html" title="struct solana_message::compiled_instruction::CompiledInstruction">CompiledInstruction</a>]</h4></section></summary><div class="docblock"><p>Program instructions that will be executed in sequence and committed in
one atomic transaction if all succeed.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.program_instructions_iter" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#178-189">Source</a><h4 class="code-header">pub fn <a href="#method.program_instructions_iter" class="fn">program_instructions_iter</a>(
    &amp;self,
) -&gt; impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/iter/traits/iterator/trait.Iterator.html" title="trait core::iter::traits::iterator::Iterator">Iterator</a>&lt;Item = (&amp;Pubkey, &amp;<a class="struct" href="compiled_instruction/struct.CompiledInstruction.html" title="struct solana_message::compiled_instruction::CompiledInstruction">CompiledInstruction</a>)&gt; + <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a></h4></section></summary><div class="docblock"><p>Program instructions iterator which includes each instruction’s program
id.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.static_account_keys" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#192-197">Source</a><h4 class="code-header">pub fn <a href="#method.static_account_keys" class="fn">static_account_keys</a>(&amp;self) -&gt; &amp;[Pubkey]</h4></section></summary><div class="docblock"><p>Return the list of statically included account keys.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.account_keys" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#200-205">Source</a><h4 class="code-header">pub fn <a href="#method.account_keys" class="fn">account_keys</a>(&amp;self) -&gt; <a class="struct" href="struct.AccountKeys.html" title="struct solana_message::AccountKeys">AccountKeys</a>&lt;'_&gt;</h4></section></summary><div class="docblock"><p>Returns the list of account keys that are loaded for this message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.message_address_table_lookups" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#208-213">Source</a><h4 class="code-header">pub fn <a href="#method.message_address_table_lookups" class="fn">message_address_table_lookups</a>(&amp;self) -&gt; &amp;[<a class="struct" href="v0/struct.MessageAddressTableLookup.html" title="struct solana_message::v0::MessageAddressTableLookup">MessageAddressTableLookup</a>]</h4></section></summary><div class="docblock"><p>Returns the list of account keys used for account lookup tables.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_key_passed_to_program" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#218-220">Source</a><h4 class="code-header">pub fn <a href="#method.is_key_passed_to_program" class="fn">is_key_passed_to_program</a>(&amp;self, key_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section><span class="item-info"><div class="stab deprecated"><span class="emoji">👎</span><span>Deprecated since 2.0.0: Please use <code>is_instruction_account</code> instead</span></div></span></summary><div class="docblock"><p>Returns true if the account at the specified index is an input to some
program instruction in this message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_instruction_account" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#224-232">Source</a><h4 class="code-header">pub fn <a href="#method.is_instruction_account" class="fn">is_instruction_account</a>(&amp;self, key_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class="docblock"><p>Returns true if the account at the specified index is an input to some
program instruction in this message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_invoked" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#236-241">Source</a><h4 class="code-header">pub fn <a href="#method.is_invoked" class="fn">is_invoked</a>(&amp;self, key_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class="docblock"><p>Returns true if the account at the specified index is invoked as a
program in this message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_non_loader_key" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#249-251">Source</a><h4 class="code-header">pub fn <a href="#method.is_non_loader_key" class="fn">is_non_loader_key</a>(&amp;self, key_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section><span class="item-info"><div class="stab deprecated"><span class="emoji">👎</span><span>Deprecated since 2.0.0: Please use <code>is_invoked</code> and <code>is_instruction_account</code> instead</span></div></span></summary><div class="docblock"><p>Returns true if the account at the specified index is not invoked as a
program or, if invoked, is passed to a program.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_writable" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#255-260">Source</a><h4 class="code-header">pub fn <a href="#method.is_writable" class="fn">is_writable</a>(&amp;self, index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class="docblock"><p>Returns true if the account at the specified index is writable by the
instructions in this message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_signer" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#264-266">Source</a><h4 class="code-header">pub fn <a href="#method.is_signer" class="fn">is_signer</a>(&amp;self, index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class="docblock"><p>Returns true if the account at the specified index signed this
message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.num_readonly_accounts" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#277-285">Source</a><h4 class="code-header">pub fn <a href="#method.num_readonly_accounts" class="fn">num_readonly_accounts</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a></h4></section></summary><div class="docblock"><p>Return the number of readonly accounts loaded by this message.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.decompile_instructions" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#288-312">Source</a><h4 class="code-header">pub fn <a href="#method.decompile_instructions" class="fn">decompile_instructions</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/nightly/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;BorrowedInstruction&lt;'_&gt;&gt;</h4></section></summary><div class="docblock"><p>Decompile message instructions without cloning account keys</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.is_upgradeable_loader_present" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#315-320">Source</a><h4 class="code-header">pub fn <a href="#method.is_upgradeable_loader_present" class="fn">is_upgradeable_loader_present</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class="docblock"><p>Inspect all message keys for the bpf upgradeable loader</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_ix_signers" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#323-335">Source</a><h4 class="code-header">pub fn <a href="#method.get_ix_signers" class="fn">get_ix_signers</a>(&amp;self, ix_index: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.usize.html">usize</a>) -&gt; impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/iter/traits/iterator/trait.Iterator.html" title="trait core::iter::traits::iterator::Iterator">Iterator</a>&lt;Item = &amp;Pubkey&gt;</h4></section></summary><div class="docblock"><p>Get a list of signers for the instruction at the given index</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_durable_nonce" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#339-366">Source</a><h4 class="code-header">pub fn <a href="#method.get_durable_nonce" class="fn">get_durable_nonce</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;&amp;Pubkey&gt;</h4></section></summary><div class="docblock"><p>If the message uses a durable nonce, return the pubkey of the nonce account</p>
</div></details><section id="method.num_signatures" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#372-374">Source</a><h4 class="code-header">pub fn <a href="#method.num_signatures" class="fn">num_signatures</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u64.html">u64</a></h4></section><span class="item-info"><div class="stab deprecated"><span class="emoji">👎</span><span>Deprecated since 2.1.0: Please use <code>SanitizedMessage::num_total_signatures</code> instead.</span></div></span><details class="toggle method-toggle" open><summary><section id="method.num_total_signatures" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#379-381">Source</a><h4 class="code-header">pub fn <a href="#method.num_total_signatures" class="fn">num_total_signatures</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u64.html">u64</a></h4></section></summary><div class="docblock"><p>Returns the total number of signatures in the message.
This includes required transaction signatures as well as any
pre-compile signatures that are attached in instructions.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.num_write_locks" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#385-389">Source</a><h4 class="code-header">pub fn <a href="#method.num_write_locks" class="fn">num_write_locks</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u64.html">u64</a></h4></section></summary><div class="docblock"><p>Returns the number of requested write-locks in this message.
This does not consider if write-locks are demoted.</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_signature_details" class="method"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#392-425">Source</a><h4 class="code-header">pub fn <a href="#method.get_signature_details" class="fn">get_signature_details</a>(&amp;self) -&gt; <a class="struct" href="struct.TransactionSignatureDetails.html" title="struct solana_message::TransactionSignatureDetails">TransactionSignatureDetails</a></h4></section></summary><div class="docblock"><p>return detailed signature counts</p>
</div></details></div></details></div><h2 id="trait-implementations" class="section-header">Trait Implementations<a href="#trait-implementations" class="anchor">§</a></h2><div id="trait-implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-Clone-for-SanitizedMessage" class="impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#impl-Clone-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.clone" class="method trait-impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#method.clone" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#tymethod.clone" class="fn">clone</a>(&amp;self) -&gt; <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h4></section></summary><div class='docblock'>Returns a duplicate of the value. <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#tymethod.clone">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clone_from" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/nightly/src/core/clone.rs.html#209">Source</a></span><a href="#method.clone_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#method.clone_from" class="fn">clone_from</a>(&amp;mut self, source: &amp;Self)</h4></section></summary><div class='docblock'>Performs copy-assignment from <code>source</code>. <a href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html#method.clone_from">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Debug-for-SanitizedMessage" class="impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#impl-Debug-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html" title="trait core::fmt::Debug">Debug</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.fmt" class="method trait-impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#method.fmt" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html#tymethod.fmt" class="fn">fmt</a>(&amp;self, f: &amp;mut <a class="struct" href="https://doc.rust-lang.org/nightly/core/fmt/struct.Formatter.html" title="struct core::fmt::Formatter">Formatter</a>&lt;'_&gt;) -&gt; <a class="type" href="https://doc.rust-lang.org/nightly/core/fmt/type.Result.html" title="type core::fmt::Result">Result</a></h4></section></summary><div class='docblock'>Formats the value using the given formatter. <a href="https://doc.rust-lang.org/nightly/core/fmt/trait.Debug.html#tymethod.fmt">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-PartialEq-for-SanitizedMessage" class="impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#impl-PartialEq-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html" title="trait core::cmp::PartialEq">PartialEq</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.eq" class="method trait-impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#method.eq" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#tymethod.eq" class="fn">eq</a>(&amp;self, other: &amp;<a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class="toggle method-toggle" open><summary><section id="method.ne" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/nightly/src/core/cmp.rs.html#262">Source</a></span><a href="#method.ne" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/cmp/trait.PartialEq.html#method.ne" class="fn">ne</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,
and should not be overridden without very good reason.</div></details></div></details><section id="impl-Eq-for-SanitizedMessage" class="impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#impl-Eq-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/cmp/trait.Eq.html" title="trait core::cmp::Eq">Eq</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section><section id="impl-StructuralPartialEq-for-SanitizedMessage" class="impl"><a class="src rightside" href="../src/solana_message/sanitized.rs.html#81">Source</a><a href="#impl-StructuralPartialEq-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.StructuralPartialEq.html" title="trait core::marker::StructuralPartialEq">StructuralPartialEq</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-SanitizedMessage" class="impl"><a href="#impl-Freeze-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section><section id="impl-RefUnwindSafe-for-SanitizedMessage" class="impl"><a href="#impl-RefUnwindSafe-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section><section id="impl-Send-for-SanitizedMessage" class="impl"><a href="#impl-Send-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section><section id="impl-Sync-for-SanitizedMessage" class="impl"><a href="#impl-Sync-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section><section id="impl-Unpin-for-SanitizedMessage" class="impl"><a href="#impl-Unpin-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section><section id="impl-UnwindSafe-for-SanitizedMessage" class="impl"><a href="#impl-UnwindSafe-for-SanitizedMessage" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/nightly/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="enum" href="enum.SanitizedMessage.html" title="enum solana_message::SanitizedMessage">SanitizedMessage</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/nightly/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/nightly/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/nightly/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/nightly/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-CloneToUninit-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/clone.rs.html#476">Source</a><a href="#impl-CloneToUninit-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.CloneToUninit.html" title="trait core::clone::CloneToUninit">CloneToUninit</a> for T<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.clone_to_uninit" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/clone.rs.html#478">Source</a><a href="#method.clone_to_uninit" class="anchor">§</a><h4 class="code-header">unsafe fn <a href="https://doc.rust-lang.org/nightly/core/clone/trait.CloneToUninit.html#tymethod.clone_to_uninit" class="fn">clone_to_uninit</a>(&amp;self, dest: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.pointer.html">*mut </a><a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.u8.html">u8</a>)</h4></section></summary><span class="item-info"><div class="stab unstable"><span class="emoji">🔬</span><span>This is a nightly-only experimental API. (<code>clone_to_uninit</code>)</span></div></span><div class='docblock'>Performs copy-assignment from <code>self</code> to <code>dest</code>. <a href="https://doc.rust-lang.org/nightly/core/clone/trait.CloneToUninit.html#tymethod.clone_to_uninit">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#773">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#776">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#756-758">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#766">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/nightly/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Same-for-T" class="impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#34">Source</a><a href="#impl-Same-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html" title="trait typenum::type_operators::Same">Same</a> for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Output" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#35">Source</a><a href="#associatedtype.Output" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html#associatedtype.Output" class="associatedtype">Output</a> = T</h4></section></summary><div class='docblock'>Should always be <code>Self</code></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-ToOwned-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#82-84">Source</a><a href="#impl-ToOwned-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html" title="trait alloc::borrow::ToOwned">ToOwned</a> for T<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/nightly/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Owned" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#86">Source</a><a href="#associatedtype.Owned" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#associatedtype.Owned" class="associatedtype">Owned</a> = T</h4></section></summary><div class='docblock'>The resulting type after obtaining ownership.</div></details><details class="toggle method-toggle" open><summary><section id="method.to_owned" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#87">Source</a><a href="#method.to_owned" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#tymethod.to_owned" class="fn">to_owned</a>(&amp;self) -&gt; T</h4></section></summary><div class='docblock'>Creates owned data from borrowed data, usually by cloning. <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#tymethod.to_owned">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clone_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/alloc/borrow.rs.html#91">Source</a><a href="#method.clone_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#method.clone_into" class="fn">clone_into</a>(&amp;self, target: <a class="primitive" href="https://doc.rust-lang.org/nightly/std/primitive.reference.html">&amp;mut T</a>)</h4></section></summary><div class='docblock'>Uses borrowed data to replace owned data, usually by cloning. <a href="https://doc.rust-lang.org/nightly/alloc/borrow/trait.ToOwned.html#method.clone_into">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#812-814">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#816">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/nightly/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#819">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#797-799">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#801">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/nightly/src/core/convert/mod.rs.html#804">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/nightly/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/nightly/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/nightly/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details></div></section></div></main></body></html>