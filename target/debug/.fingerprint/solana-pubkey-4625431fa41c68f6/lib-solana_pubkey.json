{"rustc": 8210029788606052455, "features": "[\"bytemuck\", \"curve25519\", \"serde\", \"sha2\"]", "declared_features": "[\"borsh\", \"bytemuck\", \"curve25519\", \"default\", \"dev-context-only-utils\", \"frozen-abi\", \"rand\", \"serde\", \"sha2\", \"std\"]", "target": 6003644542170993130, "profile": 13904601386562544002, "path": 7846583149578050980, "deps": [[4145337479814240220, "solana_decode_error", false, 14848448109210918783], [5157631553186200874, "num_traits", false, 18416246797249802859], [5646760554799980987, "five8_const", false, 14592483611826505405], [6511429716036861196, "bytemuck", false, 3465288428922990175], [6616501577376279788, "bs58", false, 8893011888688935960], [9689903380558560274, "serde", false, 8600153198446266041], [11104455582174147483, "solana_sha256_hasher", false, 16870290279556647223], [13595581133353633439, "curve25519_dalek", false, 10825861153840618700], [14254950316256772154, "solana_atomic_u64", false, 1712885184526983026], [15246557919602675095, "bytemuck_derive", false, 6923692177051377693], [15429715045911386410, "solana_sanitize", false, 13705721463190134362], [16257276029081467297, "serde_derive", false, 10806385234479948477]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-pubkey-4625431fa41c68f6/dep-lib-solana_pubkey", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}