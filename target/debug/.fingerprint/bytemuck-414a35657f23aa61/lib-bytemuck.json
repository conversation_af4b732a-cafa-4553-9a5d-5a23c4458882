{"rustc": 8210029788606052455, "features": "[\"bytemuck_derive\", \"derive\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"impl_core_error\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\", \"zeroable_unwind_fn\"]", "target": 5195934831136530909, "profile": 9981887903420318623, "path": 1857221265319292174, "deps": [[15246557919602675095, "bytemuck_derive", false, 6923692177051377693]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bytemuck-414a35657f23aa61/dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}