{"rustc": 8210029788606052455, "features": "[\"alloc\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 8276155916380437441, "path": 8356946712815800394, "deps": [[1740877332521282793, "rand_core", false, 10999792042705015305], [3712811570531045576, "byteorder", false, 2288843357063403], [6374421995994392543, "digest", false, 8291266506500145642], [6528079939221783635, "zeroize", false, 7778648977734695747], [17003143334332120809, "subtle", false, 15925667687469689302]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-f8bff8dfc46b6fc7/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}