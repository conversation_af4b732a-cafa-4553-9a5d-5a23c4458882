{"rustc": 8210029788606052455, "features": "[\"bincode\", \"blake3\", \"serde\"]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 3503294605475723963, "path": 5857062759363820540, "deps": [[65234016722529558, "bincode", false, 5943561751687806917], [1461800729938538396, "solana_instruction", false, 4916075300473704924], [4113188218898653100, "solana_short_vec", false, 14791544955438167968], [7355047358885037824, "solana_hash", false, 8077812868183294051], [9241925498456048256, "blake3", false, 2859632934853465767], [9475981483390999469, "solana_pubkey", false, 6465396783187643338], [9556858120010252096, "solana_transaction_error", false, 11647597089342464445], [9689903380558560274, "serde", false, 9369136447857529070], [11702702251883620295, "solana_bincode", false, 3253274102643020830], [14591356476411885690, "solana_sdk_ids", false, 8707974222632099048], [15341883195918613377, "solana_system_interface", false, 14389750614429548378], [15429715045911386410, "solana_sanitize", false, 9793455129316311649], [16257276029081467297, "serde_derive", false, 16343034442169492840], [17917672826516349275, "lazy_static", false, 6304238075501016141]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-message-b37c880aa7dbc0ce/dep-lib-solana_message", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}