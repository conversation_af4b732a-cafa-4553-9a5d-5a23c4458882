{"rustc": 8210029788606052455, "features": "[\"bincode\", \"blake3\", \"serde\"]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 13904601386562544002, "path": 5857062759363820540, "deps": [[65234016722529558, "bincode", false, 14208841544713846712], [1461800729938538396, "solana_instruction", false, 8165029981806311004], [4113188218898653100, "solana_short_vec", false, 6081415785062493288], [7355047358885037824, "solana_hash", false, 13371013576212038596], [9241925498456048256, "blake3", false, 17658277825876201554], [9475981483390999469, "solana_pubkey", false, 4577721170137437933], [9556858120010252096, "solana_transaction_error", false, 13754908510498555184], [9689903380558560274, "serde", false, 8600153198446266041], [11702702251883620295, "solana_bincode", false, 8202430327820855185], [14591356476411885690, "solana_sdk_ids", false, 6060043400293335704], [15341883195918613377, "solana_system_interface", false, 17005997554201252425], [15429715045911386410, "solana_sanitize", false, 13705721463190134362], [16257276029081467297, "serde_derive", false, 10806385234479948477], [17917672826516349275, "lazy_static", false, 13712216839209692897]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-message-8ae38a0f1d66776d/dep-lib-solana_message", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}