{"rustc": 8210029788606052455, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 8276155916380437441, "path": 17749628708327961150, "deps": [[4731167174326621189, "rand", false, 14741516869569663331], [6528079939221783635, "zeroize", false, 7778648977734695747], [9689903380558560274, "serde_crate", false, 8600153198446266041], [10150151165539439550, "curve25519_dalek", false, 18385425517294835842], [11472355562936271783, "sha2", false, 3276351321884458334], [16629266738323756185, "ed25519", false, 10241209187180792456]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-afe5daac58a15011/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}