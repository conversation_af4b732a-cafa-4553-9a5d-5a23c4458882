[package]
name = "shreder"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "protogen"
path = "protogen.rs"

[dependencies]
anyhow = "1.0.98"
async-trait = "0.1.88"
bincode = "1.3.3"
bs58 = "0.5.1"
config = "0.15.11"
futures-util = "0.3.31"
prost = "0.13.5"
prost-build = "0.13.5"
prost-types = "0.13.5"
rustls = { version = "0.23.27", features = ["ring"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_with = "3.12.0"
solana-entry = "2.2.7"
solana-pubkey = "2.2.1"
solana-transaction = "2.2.2"
thiserror = "2.0.12"
tokio = { version = "1.45.1", features = ["rt-multi-thread"] }
tokio-stream = { version = "0.1.17", features = ["net"] }
tonic = { version = "0.13.1", features = ["tls-webpki-roots"] }
tonic-build = "0.13.1"
tonic-web = "0.13.1"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["json", "env-filter"] }
validator = { version = "0.20.0", features = ["derive"] }
xxhash-rust = { version = "0.8.15", features = ["xxh3"] }

[build-dependencies]
prost-build = "0.13.5"
tonic-build = "0.13.1"

[dev-dependencies]
